@tailwind base;
@tailwind components;
@tailwind utilities;

/* PrimeVue CSS imports */
@import 'primevue/resources/themes/lara-light-blue/theme.css';
@import 'primevue/resources/primevue.min.css';
@import 'primeicons/primeicons.css';

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }
}

/* Custom component styles */
@layer components {
  /* Modern Button Styles */
  .btn {
    @apply inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-500 text-white shadow-sm hover:bg-primary-600 focus:ring-primary-500 active:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-brand-800 text-white shadow-sm hover:bg-brand-900 focus:ring-brand-700 active:bg-brand-900;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100;
  }

  .btn-ghost {
    @apply border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200;
  }

  .btn-danger {
    @apply bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-red-500 active:bg-red-800;
  }

  .btn-success {
    @apply bg-green-600 text-white shadow-sm hover:bg-green-700 focus:ring-green-500 active:bg-green-800;
  }

  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  /* Admin-specific component styles */
  .admin-sidebar {
    transition: width 0.3s ease;
  }

  .admin-sidebar.collapsed {
    width: 4rem;
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  .nav-shadow {
    box-shadow: 0 4px 20px rgba(0, 104, 255, 0.1);
  }

  .menu-item {
    transition: all 0.2s ease;
  }

  .menu-item:hover {
    transform: translateX(4px);
  }

  /* Mobile touch targets */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
  }

  /* Enhanced Input Styles - Ring only, no borders */
  .form-input {
    @apply block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-all duration-200 sm:text-sm sm:leading-6;
  }

  .form-input:hover:not(:disabled) {
    @apply ring-gray-400;
  }

  .form-input:disabled {
    @apply bg-gray-50 text-gray-500 ring-gray-200 cursor-not-allowed;
  }

  .form-input.error {
    @apply ring-red-300 text-red-900 placeholder:text-red-300 focus:ring-red-500;
  }

  .form-input.success {
    @apply ring-green-300 text-green-900 placeholder:text-green-300 focus:ring-green-500;
  }

  /* Override any conflicting styles from Tailwind Forms plugin */
  /* input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="search"],
  input[type="tel"],
  input[type="url"] {
    border: 0 !important;
  } */

  /* Ensure focus states use only rings - no borders or box-shadows */
  input:focus {
    border-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
  }

  /* Fix error state focus - single red ring only */
  input.error:focus,
  .form-input.error:focus,
  input[class*="ring-red"]:focus {
    --tw-ring-color: rgb(239 68 68) !important; /* red-500 */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px rgb(239 68 68) !important;
  }

  /* Fix success state focus - single green ring only */
  input.success:focus,
  .form-input.success:focus,
  input[class*="ring-green"]:focus {
    --tw-ring-color: rgb(34 197 94) !important; /* green-500 */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px rgb(34 197 94) !important;
  }

  /* Ensure normal focus state works properly */
  input:not(.error):not(.success):focus {
    --tw-ring-color: #0068ff !important; /* brand primary */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px #0068ff !important;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-label.error {
    @apply text-red-700;
  }

  .form-label.disabled {
    @apply text-gray-400;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Admin-specific utilities */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .safe-area-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  /* Admin mobile optimizations */
  @media (max-width: 768px) {
    .admin-sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 50;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      width: 280px !important;
      max-width: 85vw;
    }

    .admin-sidebar.mobile-open {
      transform: translateX(0);
    }

    .admin-content {
      margin-left: 0 !important;
    }

    /* Prevent body scroll when mobile menu is open */
    body.mobile-menu-open {
      overflow: hidden;
      position: fixed;
      width: 100%;
    }

    /* Disable hover effects on mobile */
    .menu-item:hover {
      transform: none;
    }
  }

  /* Prevent horizontal scroll on small screens */
  @media (max-width: 640px) {
    .admin-sidebar {
      width: 100vw !important;
      max-width: 100vw !important;
    }
  }
}

