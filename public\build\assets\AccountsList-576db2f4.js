import{h as R,r as o,R as T,L as U,o as f,m as G,x as i,u,f as b,k as q,g as t,t as c,v as p,H as J,_ as v,F as K}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as O,e as Q}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as C}from"./app-admin-1baa1658.js";import{_ as W}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as X}from"./useNotifications-98e2c61c.js";const Y={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Z={class:"px-6 py-4"},ee={class:"text-sm font-medium text-gray-900"},te=["href"],se={class:"px-6 py-4 whitespace-nowrap"},ae={class:"text-sm text-gray-900"},oe={class:"px-6 py-4 whitespace-nowrap text-right"},ne={class:"text-sm font-medium text-gray-900"},re={class:"px-6 py-4 whitespace-nowrap"},ce={class:"text-sm text-gray-900"},le={class:"px-6 py-4 whitespace-nowrap"},ie={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},ue={class:"flex justify-center space-x-2"},ve=R({__name:"AccountsList",setup(de){const m=O(),{showNotification:d}=X(),h=o(!1),g=o(null),n=o([]),l=o(1),_=o(20),A=o(0),S=o(1),y=o([]),w=T({search:""}),$=[{key:"index",label:"#",sortable:!1},{key:"customer_name",label:"Customer Name",sortable:!0},{key:"account_name",label:"Account Name",sortable:!0},{key:"amount",label:"Amount",sortable:!0},{key:"account_number",label:"Account Number",sortable:!0},{key:"account_type",label:"Account Type",sortable:!0}],r=async()=>{h.value=!0;try{const e={page:l.value.toString(),per_page:_.value.toString(),...w},s=await C.get("/accounts",e)}catch(e){g.value="Failed to fetch accounts",console.error("Error fetching accounts:",e)}finally{h.value=!1}},N=()=>{m.push("/admin-spa/accounts/create")},B=e=>{w.search=e,l.value=1,r()},F=(e,s)=>{console.log("Sort:",e,s)},M=e=>{l.value=e,r()},z=e=>{n.value=e?y.value.map(s=>s.id.toString()):[]},L=e=>{m.push(`/admin-spa/accounts/edit/${e.id}`)},V=e=>{m.push(`/admin-spa/accounts/view/${e.id}`)},j=async e=>{if(confirm("Are you sure you want to delete this account?"))try{await C.delete(`/accounts/${e.id}`),d("Account deleted successfully","success"),await r()}catch{d("Failed to delete account","error")}},D=async()=>{if(n.value.length!==0&&confirm(`Are you sure you want to delete ${n.value.length} accounts?`))try{d(`${n.value.length} accounts deleted successfully`,"success"),n.value=[],await r()}catch{d("Failed to delete accounts","error")}},P=e=>{if(!e)return"-";const s=typeof e=="string"?parseFloat(e):e;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s)},H=e=>{if(!e)return"default";switch(e.toLowerCase()){case"savings":return"success";case"checking":return"primary";case"credit":return"warning";case"loan":return"danger";default:return"default"}};return U(()=>{r()}),(e,s)=>(f(),G(u(W),{title:"Accounts",subtitle:"Manage user accounts and balances",loading:h.value,error:g.value,items:y.value,columns:$,"selected-items":n.value,"show-bulk-actions":!0,"current-page":l.value,"total-pages":S.value,"total-items":A.value,"per-page":_.value,"create-button-text":"Add Account","empty-state-title":"No accounts found","empty-state-message":"Get started by adding your first account.",onCreate:N,onSearch:B,onSort:F,onPageChange:M,onSelectAll:z,onBulkDelete:D,onRefresh:r},{rows:i(({items:E})=>[(f(!0),b(K,null,q(E,(a,I)=>{var k;return f(),b("tr",{key:a.id,class:"hover:bg-gray-50"},[t("td",Y,c((l.value-1)*_.value+I+1),1),t("td",Z,[t("div",ee,[t("a",{href:`/users/${a.user_id}`,class:"hover:text-blue-600"},c(((k=a.user)==null?void 0:k.name)||"-"),9,te)])]),t("td",se,[t("div",ae,c(a.account_name||"-"),1)]),t("td",oe,[t("div",ne,c(P(a.amount)),1)]),t("td",re,[t("div",ce,c(a.account_number||"-"),1)]),t("td",le,[p(u(Q),{variant:H(a.account_type)},{default:i(()=>[J(c(a.account_type||"-"),1)]),_:2},1032,["variant"])]),t("td",ie,[t("div",ue,[p(u(v),{size:"sm",variant:"outline",onClick:x=>L(a)},{default:i(()=>s[0]||(s[0]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[0]},1032,["onClick"]),p(u(v),{size:"sm",onClick:x=>V(a)},{default:i(()=>s[1]||(s[1]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[1]},1032,["onClick"]),p(u(v),{size:"sm",onClick:x=>j(a)},{default:i(()=>s[2]||(s[2]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[2]},1032,["onClick"])])])])}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{ve as default};
