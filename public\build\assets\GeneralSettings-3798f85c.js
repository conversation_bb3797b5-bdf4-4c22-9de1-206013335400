import{h as R,r as o,L as K,o as i,m as T,x as m,u as h,f as u,k as q,g as a,t as g,j as J,v as y,_ as k,F as O}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Q}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as B}from"./app-admin-1baa1658.js";import{_ as U}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as W}from"./useNotifications-98e2c61c.js";const X={class:"px-6 py-4"},Y={class:"text-sm font-medium text-gray-900"},Z={class:"px-6 py-4"},ee={class:"text-sm text-gray-900"},te={key:0,class:"flex items-center space-x-2"},se=["src"],ae=["href"],oe={key:1,class:"max-w-xs"},ne=["title"],re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ie={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},ce={class:"flex justify-center space-x-2"},ve=R({__name:"GeneralSettings",setup(ue){const v=Q(),{showNotification:d}=W(),f=o(!1),w=o(null),r=o([]),c=o(1),x=o(20),b=o(0),C=o(1),_=o([]),S=o({search:""}),j=[{key:"key",label:"Setting Key",sortable:!0},{key:"value",label:"Value",sortable:!1},{key:"created_by",label:"Created By",sortable:!0},{key:"created_at",label:"Created",sortable:!0}],l=async()=>{var e,t,p;f.value=!0;try{const s={page:c.value.toString(),per_page:x.value.toString(),...S.value},n=await B.get("/admin/settings",s);_.value=n.data,b.value=((e=n.meta)==null?void 0:e.total)||0,C.value=((t=n.meta)==null?void 0:t.last_page)||1,c.value=((p=n.meta)==null?void 0:p.current_page)||1}catch(s){w.value="Failed to fetch settings",console.error("Error fetching settings:",s)}finally{f.value=!1}},D=()=>{c.value=1,l()},F=()=>{v.push("/admin-spa/settings/create")},V=e=>{S.value.search=e,D()},z=(e,t)=>{console.log("Sort:",e,t)},A=e=>{c.value=e,l()},M=e=>{r.value=e?_.value.map(t=>t.id.toString()):[]},N=e=>{v.push(`/admin-spa/settings/edit/${e.id}`)},L=e=>{v.push(`/admin-spa/settings/view/${e.id}`)},P=async e=>{if(confirm("Are you sure you want to delete this setting?"))try{await B.delete(`/admin/settings/${e.id}`),d("Setting deleted successfully","success"),await l()}catch{d("Failed to delete setting","error")}},E=async()=>{if(r.value.length!==0&&confirm(`Are you sure you want to delete ${r.value.length} settings?`))try{d(`${r.value.length} settings deleted successfully`,"success"),r.value=[],await l()}catch{d("Failed to delete settings","error")}},H=(e,t)=>e?e.length>t?e.substring(0,t)+"...":e:"-",I=e=>e?/\.(jpg|jpeg|png|gif|webp)$/i.test(e):!1,G=e=>e?new Date(e).toLocaleDateString():"-";return K(()=>{l()}),(e,t)=>(i(),T(h(U),{title:"General Settings",subtitle:"Configure general system settings",loading:f.value,error:w.value,items:_.value,columns:j,"selected-items":r.value,"show-bulk-actions":!0,"current-page":c.value,"total-pages":C.value,"total-items":b.value,"per-page":x.value,"create-button-text":"Add Setting","empty-state-title":"No settings found","empty-state-message":"Start by creating a new system setting.",onCreate:F,onSearch:V,onSort:z,onPageChange:A,onSelectAll:M,onBulkDelete:E,onRefresh:l},{rows:m(({items:p})=>[(i(!0),u(O,null,q(p,s=>{var n;return i(),u("tr",{key:s.id,class:"hover:bg-gray-50"},[a("td",X,[a("div",Y,g(s.key||"-"),1)]),a("td",Z,[a("div",ee,[s.path?(i(),u("div",te,[I(s.path)?(i(),u("img",{key:0,src:s.path,alt:"Setting image",class:"h-8 w-8 object-cover rounded"},null,8,se)):J("",!0),a("a",{href:s.path,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"}," View File ",8,ae)])):(i(),u("div",oe,[a("span",{class:"truncate block",title:s.value},g(H(s.value,50)),9,ne)]))])]),a("td",re,g(((n=s.created_by_user)==null?void 0:n.name)||"System"),1),a("td",le,g(G(s.created_at)),1),a("td",ie,[a("div",ce,[y(h(k),{size:"sm",variant:"outline",onClick:$=>N(s)},{default:m(()=>t[0]||(t[0]=[a("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[0]},1032,["onClick"]),y(h(k),{size:"sm",variant:"info",onClick:$=>L(s)},{default:m(()=>t[1]||(t[1]=[a("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[1]},1032,["onClick"]),y(h(k),{size:"sm",variant:"danger",onClick:$=>P(s)},{default:m(()=>t[2]||(t[2]=[a("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[2]},1032,["onClick"])])])])}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{ve as default};
