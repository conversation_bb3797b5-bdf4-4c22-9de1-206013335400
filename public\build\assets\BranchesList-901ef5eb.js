import{h as I,r as n,L as R,o as y,m as T,x as d,u,f as S,k as U,g as a,t as h,v,H as q,_ as k,F as G}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as J,e as K}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as $}from"./app-admin-1baa1658.js";import{_ as O}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as Q}from"./useNotifications-98e2c61c.js";const W={class:"px-6 py-4"},X={class:"text-sm font-medium text-gray-900"},Y=["href"],Z={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},te={class:"px-6 py-4 whitespace-nowrap"},ae={class:"px-6 py-4 text-sm text-gray-900"},se=["title"],ne={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},oe={class:"flex justify-center space-x-2"},he=I({__name:"BranchesList",setup(re){const f=J(),{showNotification:p}=Q(),g=n(!1),w=n(null),r=n([]),i=n(1),x=n(20),b=n(0),C=n(1),_=n([]),B=n({search:""}),A=[{key:"name",label:"Name",sortable:!0},{key:"phone",label:"Phone",sortable:!1},{key:"email",label:"Email",sortable:!1},{key:"status",label:"Status",sortable:!0},{key:"address",label:"Address",sortable:!1}],l=async()=>{var e,t,m;g.value=!0;try{const s={page:i.value.toString(),per_page:x.value.toString(),...B.value},o=await $.get("/admin/branches",s);_.value=o.data,b.value=((e=o.meta)==null?void 0:e.total)||0,C.value=((t=o.meta)==null?void 0:t.last_page)||1,i.value=((m=o.meta)==null?void 0:m.current_page)||1}catch(s){w.value="Failed to fetch branches",console.error("Error fetching branches:",s)}finally{g.value=!1}},N=()=>{i.value=1,l()},z=()=>{f.push("/admin-spa/branches/create")},F=e=>{B.value.search=e,N()},M=(e,t)=>{console.log("Sort:",e,t)},P=e=>{i.value=e,l()},V=e=>{r.value=e?_.value.map(t=>t.id.toString()):[]},j=e=>{f.push(`/admin-spa/branches/edit/${e.id}`)},L=e=>{f.push(`/admin-spa/branches/view/${e.id}`)},D=async e=>{if(confirm("Are you sure you want to delete this branch?"))try{await $.delete(`/admin/branches/${e.id}`),p("Branch deleted successfully","success"),await l()}catch{p("Failed to delete branch","error")}},E=async()=>{if(r.value.length!==0&&confirm(`Are you sure you want to delete ${r.value.length} branches?`))try{p(`${r.value.length} branches deleted successfully`,"success"),r.value=[],await l()}catch{p("Failed to delete branches","error")}},H=(e,t)=>e?e.length>t?e.substring(0,t)+"...":e:"-";return R(()=>{l()}),(e,t)=>(y(),T(u(O),{title:"All Branches",subtitle:"View and manage all branches",loading:g.value,error:w.value,items:_.value,columns:A,"selected-items":r.value,"show-bulk-actions":!0,"current-page":i.value,"total-pages":C.value,"total-items":b.value,"per-page":x.value,"create-button-text":"Add Branch","empty-state-title":"No branches found","empty-state-message":"Start by creating a new branch location.",onCreate:z,onSearch:F,onSort:M,onPageChange:P,onSelectAll:V,onBulkDelete:E,onRefresh:l},{rows:d(({items:m})=>[(y(!0),S(G,null,U(m,s=>{var o;return y(),S("tr",{key:s.id,class:"hover:bg-gray-50"},[a("td",W,[a("div",X,[a("a",{href:`/admin-spa/branches/view/${s.id}`,class:"hover:text-blue-600"},h(s.name||"-"),9,Y)])]),a("td",Z,h(s.phone||"-"),1),a("td",ee,h(s.email||"-"),1),a("td",te,[v(u(K),{variant:((o=s.status)==null?void 0:o.id)===1?"success":"secondary"},{default:d(()=>{var c;return[q(h(((c=s.status)==null?void 0:c.name)||"Unknown"),1)]}),_:2},1032,["variant"])]),a("td",ae,[a("span",{class:"truncate block max-w-xs",title:s.address},h(H(s.address,50)),9,se)]),a("td",ne,[a("div",oe,[v(u(k),{size:"sm",variant:"outline",onClick:c=>j(s)},{default:d(()=>t[0]||(t[0]=[a("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[0]},1032,["onClick"]),v(u(k),{size:"sm",variant:"info",onClick:c=>L(s)},{default:d(()=>t[1]||(t[1]=[a("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[1]},1032,["onClick"]),v(u(k),{size:"sm",variant:"danger",onClick:c=>D(s)},{default:d(()=>t[2]||(t[2]=[a("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[2]},1032,["onClick"])])])])}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{he as default};
