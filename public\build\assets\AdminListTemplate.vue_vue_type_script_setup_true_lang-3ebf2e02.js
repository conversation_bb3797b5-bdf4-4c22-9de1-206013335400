import{h as j,L as M,r as y,i as w,o as s,m as g,x as n,K as p,u,_ as B,g as t,H as C,t as i,j as o,d as _,v as k,b as N,f as a,k as F,n as A,F as H}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{a as L}from"./app-admin-1baa1658.js";import{i as z}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";const D={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4"},E={class:"flex-1 max-w-md"},G={key:0,class:"flex items-center space-x-3"},K={key:1,class:"flex items-center space-x-3"},O={key:0,class:"bg-blue-50 border-b border-blue-200 px-6 py-3"},Q={class:"flex items-center justify-between"},R={class:"text-sm text-blue-700"},U={key:0,class:"flex items-center space-x-2"},q={class:"overflow-x-auto"},J={class:"min-w-full divide-y divide-gray-200"},W={class:"bg-gray-50"},X={key:0,class:"w-12 px-6 py-3"},Y=["checked","indeterminate"],Z=["onClick"],ee={class:"flex items-center space-x-1"},te={key:1,class:"w-24 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},se={class:"bg-white divide-y divide-gray-200"},oe={key:1,class:"text-center py-12"},ae={class:"text-lg font-medium text-gray-900 mb-2"},le={class:"text-gray-500 mb-4"},re={key:1,class:"flex justify-center"},ce=j({__name:"AdminListTemplate",props:{title:{},subtitle:{},loading:{type:Boolean},error:{},breadcrumbs:{},items:{default:()=>[]},columns:{default:()=>[]},selectedItems:{default:()=>[]},showFilters:{type:Boolean,default:!0},showBulkActions:{type:Boolean,default:!1},showActions:{type:Boolean,default:!0},showCreateButton:{type:Boolean,default:!0},createButtonText:{default:"Create New"},createRoute:{},emptyStateTitle:{default:"No items found"},emptyStateMessage:{default:"Get started by creating your first item."},showPagination:{type:Boolean,default:!0},currentPage:{default:1},totalPages:{default:1},totalItems:{default:0},perPage:{default:20}},emits:["create","search","sort","pageChange","selectAll","selectItem"],setup(x,{emit:I}){M(()=>{console.log("AdminListTemplate mounted - Vue 3 Composition API working!")});const d=x,c=I,h=y(""),f=y(""),m=y("asc"),v=w(()=>d.items.length>0&&d.selectedItems.length===d.items.length),P=w(()=>d.selectedItems.length>0&&d.selectedItems.length<d.items.length),b=()=>{c("create")},$=()=>{c("search",h.value)},S=e=>{f.value===e?m.value=m.value==="asc"?"desc":"asc":(f.value=e,m.value="asc"),c("sort",e,m.value)},V=e=>{c("pageChange",e)},T=()=>{c("selectAll",!v.value)};return(e,l)=>(s(),g(L,{title:e.title,subtitle:e.subtitle,loading:e.loading,error:e.error,breadcrumbs:e.breadcrumbs},{actions:n(()=>[p(e.$slots,"actions",{},()=>[e.showCreateButton?(s(),g(u(B),{key:0,variant:"primary",size:"sm",onClick:b},{default:n(()=>[l[1]||(l[1]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)),C(" "+i(e.createButtonText),1)]),_:1,__:[1]})):o("",!0)])]),default:n(()=>[e.showFilters?(s(),g(u(_),{key:0,class:"p-6"},{default:n(()=>[t("div",D,[t("div",E,[k(u(N),{modelValue:h.value,"onUpdate:modelValue":l[0]||(l[0]=r=>h.value=r),type:"text",placeholder:"Search...",onInput:$},{prefix:n(()=>l[2]||(l[2]=[t("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"])]),e.$slots.filters?(s(),a("div",G,[p(e.$slots,"filters")])):o("",!0),e.$slots.listActions?(s(),a("div",K,[p(e.$slots,"listActions")])):o("",!0)])]),_:3})):o("",!0),k(u(_),{class:"overflow-hidden"},{default:n(()=>[e.showBulkActions&&e.selectedItems.length>0?(s(),a("div",O,[t("div",Q,[t("span",R,i(e.selectedItems.length)+" item"+i(e.selectedItems.length===1?"":"s")+" selected ",1),e.$slots.bulkActions?(s(),a("div",U,[p(e.$slots,"bulkActions",{selectedItems:e.selectedItems})])):o("",!0)])])):o("",!0),t("div",q,[t("table",J,[t("thead",W,[t("tr",null,[e.showBulkActions?(s(),a("th",X,[t("input",{type:"checkbox",checked:v.value,indeterminate:P.value,onChange:T,class:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,40,Y)])):o("",!0),(s(!0),a(H,null,F(e.columns,r=>(s(),a("th",{key:r.key,class:A(["px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",r.sortable?"cursor-pointer hover:bg-gray-100":""]),onClick:ne=>r.sortable?S(r.key):null},[t("div",ee,[t("span",null,i(r.label),1),r.sortable&&f.value===r.key?(s(),a("svg",{key:0,class:A(["w-4 h-4",m.value==="asc"?"transform rotate-180":""]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},l[3]||(l[3]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2)):o("",!0)])],10,Z))),128)),e.showActions?(s(),a("th",te," Actions ")):o("",!0)])]),t("tbody",se,[p(e.$slots,"rows",{items:e.items,selectedItems:e.selectedItems})])])]),e.items.length===0&&!e.loading?(s(),a("div",oe,[l[4]||(l[4]=t("div",{class:"text-gray-400 mb-4"},[t("svg",{class:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0V3"})])],-1)),t("h3",ae,i(e.emptyStateTitle),1),t("p",le,i(e.emptyStateMessage),1),e.showCreateButton?(s(),g(u(B),{key:0,variant:"primary",onClick:b},{default:n(()=>[C(i(e.createButtonText),1)]),_:1})):o("",!0)])):o("",!0)]),_:3}),e.showPagination&&e.totalPages>1?(s(),a("div",re,[k(u(z),{"current-page":e.currentPage,"total-pages":e.totalPages,"total-items":e.totalItems,"per-page":e.perPage,onPageChange:V},null,8,["current-page","total-pages","total-items","per-page"])])):o("",!0)]),_:3},8,["title","subtitle","loading","error","breadcrumbs"]))}});export{ce as _};
