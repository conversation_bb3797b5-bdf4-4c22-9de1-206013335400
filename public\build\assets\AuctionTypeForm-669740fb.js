import{h as Y,r as p,R,i as y,L as Z,o as u,m as z,x as r,u as i,g as o,v as l,d as U,f,k as M,t as b,H as h,_ as x,F as O,j,B as ee}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as te,f as ae}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{_ as T}from"./FormField.vue_vue_type_script_setup_true_lang-529d40d6.js";import"./app-admin-1baa1658.js";import{A as se}from"./AdminFormTemplate-3cd16ecb.js";import{A as oe}from"./AdminFileUpload-1a895ff4.js";import{A as ie}from"./AdminBadge-74cb3994.js";import{u as le}from"./auctionTypes-eeea8f1c.js";import{u as re}from"./items-578e557a.js";import{u as ne}from"./useNotifications-98e2c61c.js";import"./axios-917b1704.js";const ue={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},de={class:"md:col-span-2"},ce={class:"md:col-span-2"},me={class:"space-y-3"},pe={class:"flex items-center space-x-3"},ve=["src","alt"],ye={class:"text-sm font-medium text-gray-900"},fe={class:"text-xs text-gray-500"},ge={class:"flex items-center space-x-2"},_e={key:0,class:"mt-4 text-center"},be={key:0,class:"mt-6"},he={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},xe=["src","alt"],Ae={class:"flex justify-end space-x-3 pt-6 border-t"},Ee=Y({__name:"AuctionTypeForm",setup(we){const V=te(),C=ae(),S=le(),F=re(),{showNotification:v}=ne(),$=p(!1),A=p(!1),w=p(null),g=p([]),I=p([]),B=p(0),k=p(!1),a=R({name:"",type:"online",description:"",is_active:!0,media:[]}),d=R({name:"",type:"",description:"",is_active:"",media:""}),n=y(()=>!!C.params.id),c=y(()=>C.params.id?parseInt(C.params.id):null),D=y(()=>[{label:"Dashboard",href:"/admin-spa"},{label:"Auction Types",href:"/admin-spa/auction-types/list"},{label:n.value?"Edit Auction Type":"Create Auction Type"}]),E=y(()=>a.name.trim()!==""&&a.type!==""),G=y(()=>[{label:"Online Auction",value:"online"},{label:"Live Auction",value:"live"},{label:"Cash Sale",value:"cash"}]),q=y(()=>[{label:"Active",value:!0},{label:"Inactive",value:!1}]),P=async()=>{if(c.value){$.value=!0;try{const s=await S.fetchAuctionType(c.value);a.name=s.name||"",a.type=s.type||"online",a.description=s.description||"",a.is_active=s.is_active!==!1,s.media&&(g.value=s.media),await H()}catch{w.value="Failed to load auction type",v("Failed to load auction type","error")}finally{$.value=!1}}},H=async()=>{if(c.value)try{await F.fetchItems({auction_type_id:c.value.toString(),per_page:k.value?void 0:5}),I.value=F.itemsList,B.value=F.totalItems}catch(s){console.error("Failed to load associated items:",s)}},N=async()=>{var s,e,t,_;if(E.value){A.value=!0,X();try{const m={name:a.name,type:a.type,description:a.description||void 0,is_active:a.is_active,media:a.media.length>0?a.media:void 0};n.value&&c.value?(await S.updateAuctionType({id:c.value,...m}),v("Auction type updated successfully","success")):(await S.createAuctionType(m),v("Auction type created successfully","success")),V.push("/admin-spa/auction-types/list")}catch(m){(e=(s=m.response)==null?void 0:s.data)!=null&&e.errors?Object.assign(d,m.response.data.errors):(w.value=((_=(t=m.response)==null?void 0:t.data)==null?void 0:_.message)||"Failed to save auction type",v("Failed to save auction type","error"))}finally{A.value=!1}}},L=()=>{V.push("/admin-spa/auction-types/list")},J=s=>{a.media=[...a.media,...s]},K=s=>{a.media.splice(s,1)},Q=async s=>{if(confirm("Are you sure you want to remove this image?"))try{await axios.delete(`/api/auction-types/${c.value}/media/${s}`),g.value=g.value.filter(e=>e.id!==s),v("Image removed successfully","success")}catch{v("Failed to remove image","error")}},W=s=>{V.push(`/admin-spa/items/view/${s.id}`)},X=()=>{Object.keys(d).forEach(s=>{d[s]=""}),w.value=null};return Z(async()=>{n.value&&await P()}),(s,e)=>(u(),z(i(se),{title:n.value?"Edit Auction Type":"Create Auction Type",subtitle:n.value?"Update auction type details":"Add a new auction type",loading:$.value,error:w.value,breadcrumbs:D.value,onSubmit:N,onCancel:L},{default:r(()=>[o("form",{onSubmit:ee(N,["prevent"]),class:"space-y-6"},[l(i(U),{class:"p-6"},{default:r(()=>[e[6]||(e[6]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),o("div",ue,[o("div",de,[l(i(T),{modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),label:"Auction Type Name",placeholder:"Enter auction type name",error:d.name,required:""},null,8,["modelValue","error"])]),l(i(T),{modelValue:a.type,"onUpdate:modelValue":e[1]||(e[1]=t=>a.type=t),label:"Type",type:"select",placeholder:"Select type",options:G.value,error:d.type,required:""},null,8,["modelValue","options","error"]),l(i(T),{modelValue:a.is_active,"onUpdate:modelValue":e[2]||(e[2]=t=>a.is_active=t),label:"Status",type:"select",placeholder:"Select status",options:q.value,error:d.is_active},null,8,["modelValue","options","error"]),o("div",ce,[l(i(T),{modelValue:a.description,"onUpdate:modelValue":e[3]||(e[3]=t=>a.description=t),label:"Description",type:"textarea",placeholder:"Enter auction type description",error:d.description,rows:"4"},null,8,["modelValue","error"])])])]),_:1,__:[6]}),n.value&&I.value.length>0?(u(),z(i(U),{key:0,class:"p-6"},{default:r(()=>[e[8]||(e[8]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Associated Items",-1)),o("div",me,[(u(!0),f(O,null,M(I.value,t=>(u(),f("div",{key:t.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg border"},[o("div",pe,[o("img",{src:t.image||"/img/product.jpeg",alt:t.name,class:"w-10 h-10 rounded-lg object-cover"},null,8,ve),o("div",null,[o("p",ye,b(t.name),1),o("p",fe,"Ref: "+b(t.reference_number||"N/A"),1)])]),o("div",ge,[l(i(ie),{variant:t.closed_by?"success":"warning",size:"sm"},{default:r(()=>[h(b(t.closed_by?"Sold":"Available"),1)]),_:2},1032,["variant"]),l(i(x),{variant:"ghost",size:"sm",onClick:_=>W(t),class:"text-blue-600 hover:text-blue-700"},{default:r(()=>e[7]||(e[7]=[h(" View ")])),_:2,__:[7]},1032,["onClick"])])]))),128))]),I.value.length>5?(u(),f("div",_e,[l(i(x),{variant:"outline",size:"sm",onClick:e[4]||(e[4]=t=>k.value=!k.value)},{default:r(()=>[h(b(k.value?"Show Less":`Show All ${B.value} Items`),1)]),_:1})])):j("",!0)]),_:1,__:[8]})):j("",!0),l(i(U),{class:"p-6"},{default:r(()=>[e[11]||(e[11]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Images",-1)),l(i(oe),{modelValue:a.media,"onUpdate:modelValue":e[5]||(e[5]=t=>a.media=t),accept:"image/*",multiple:!0,"max-size":10,"upload-text":"Click to upload images or drag and drop","support-text":"PNG, JPG, GIF up to 10MB each",onUpload:J,onRemove:K},null,8,["modelValue"]),n.value&&g.value.length>0?(u(),f("div",be,[e[10]||(e[10]=o("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Current Images",-1)),o("div",he,[(u(!0),f(O,null,M(g.value,(t,_)=>(u(),f("div",{key:t.id,class:"relative group"},[o("img",{src:t.url,alt:`Image ${_+1}`,class:"w-full h-24 object-cover rounded-lg border"},null,8,xe),l(i(x),{variant:"ghost",size:"sm",onClick:m=>Q(t.id),class:"absolute top-1 right-1 bg-red-500 text-white hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"},{default:r(()=>e[9]||(e[9]=[o("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:2,__:[9]},1032,["onClick"])]))),128))])])):j("",!0)]),_:1,__:[11]}),o("div",Ae,[l(i(x),{type:"button",variant:"outline",onClick:L,disabled:A.value},{default:r(()=>e[12]||(e[12]=[h(" Cancel ")])),_:1,__:[12]},8,["disabled"]),l(i(x),{type:"submit",variant:"primary",loading:A.value,disabled:!E.value},{default:r(()=>[h(b(n.value?"Update Auction Type":"Create Auction Type"),1)]),_:1},8,["loading","disabled"])])],32)]),_:1},8,["title","subtitle","loading","error","breadcrumbs"]))}});export{Ee as default};
