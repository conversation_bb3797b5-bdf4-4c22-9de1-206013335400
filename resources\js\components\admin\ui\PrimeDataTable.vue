<template>
  <div class="prime-data-table-wrapper">
    <!-- Header Section -->
    <div v-if="title || subtitle || showToolbar" class="mb-6">
      <div v-if="title || subtitle" class="mb-4">
        <h2 v-if="title" class="text-2xl font-bold text-gray-900">{{ title }}</h2>
        <p v-if="subtitle" class="text-gray-600 mt-1">{{ subtitle }}</p>
      </div>
      
      <!-- Toolbar -->
      <Toolbar v-if="showToolbar" class="mb-4">
        <template #start>
          <div class="flex items-center space-x-3">
            <!-- Global Filter -->
            <span v-if="showGlobalFilter" class="p-input-icon-left">
              <i class="pi pi-search" />
              <InputText 
                v-model="globalFilterValue" 
                :placeholder="globalFilterPlaceholder"
                class="w-64"
                @input="onGlobalFilter"
              />
            </span>
            
            <!-- Custom filters slot -->
            <slot name="filters" />
          </div>
        </template>
        
        <template #end>
          <div class="flex items-center space-x-2">
            <!-- Refresh button -->
            <PrimeButton 
              v-if="showRefresh"
              icon="pi pi-refresh" 
              severity="secondary" 
              outlined
              :loading="refreshing"
              @click="handleRefresh"
              v-tooltip="'Refresh'"
            />
            
            <!-- Export button -->
            <PrimeButton 
              v-if="showExport"
              icon="pi pi-download" 
              severity="secondary" 
              outlined
              :loading="exporting"
              @click="handleExport"
              v-tooltip="'Export'"
            />
            
            <!-- Create button -->
            <PrimeButton 
              v-if="showCreateButton"
              icon="pi pi-plus" 
              :label="createButtonText"
              @click="$emit('create')"
            />
            
            <!-- Custom actions slot -->
            <slot name="actions" />
          </div>
        </template>
      </Toolbar>
    </div>

    <!-- Data Table -->
    <DataTable
      :value="items"
      :loading="loading"
      :paginator="showPagination"
      :rows="perPage"
      :totalRecords="totalItems"
      :lazy="lazy"
      :first="(currentPage - 1) * perPage"
      :globalFilterFields="globalFilterFields"
      :sortField="sortField"
      :sortOrder="sortOrder === 'asc' ? 1 : -1"
      :selection="selectedItems"
      :selectionMode="selectionMode"
      :metaKeySelection="false"
      :rowHover="true"
      :stripedRows="striped"
      :showGridlines="showGridlines"
      :size="tableSize"
      :responsiveLayout="responsiveLayout"
      :breakpoint="breakpoint"
      :scrollable="scrollable"
      :scrollHeight="scrollHeight"
      :virtualScrollerOptions="virtualScrollerOptions"
      :rowClass="rowClass"
      :rowStyle="rowStyle"
      :expandedRows="expandedRows"
      :dataKey="dataKey"
      :editMode="editMode"
      :editingRows="editingRows"
      :rowGroupMode="rowGroupMode"
      :groupRowsBy="groupRowsBy"
      :expandableRowGroups="expandableRowGroups"
      :rowGroupHeaderTemplate="rowGroupHeaderTemplate"
      :rowGroupFooterTemplate="rowGroupFooterTemplate"
      :sortMode="sortMode"
      :defaultSortOrder="defaultSortOrder"
      :multiSortMeta="multiSortMeta"
      :removableSort="removableSort"
      :filters="filters"
      :filterMode="filterMode"
      :filterLocale="filterLocale"
      :resizableColumns="resizableColumns"
      :columnResizeMode="columnResizeMode"
      :reorderableColumns="reorderableColumns"
      :contextMenu="contextMenu"
      :contextMenuSelection="contextMenuSelection"
      :rowReorder="rowReorder"
      :dragSelection="dragSelection"
      :compareSelectionBy="compareSelectionBy"
      :csvSeparator="csvSeparator"
      :exportFilename="exportFilename"
      :autoLayout="autoLayout"
      :stateStorage="stateStorage"
      :stateKey="stateKey"
      :tableStyle="tableStyle"
      :tableClass="tableClass"
      class="p-datatable-sm"
      @page="onPage"
      @sort="onSort"
      @filter="onFilter"
      @row-select="onRowSelect"
      @row-unselect="onRowUnselect"
      @select-all-change="onSelectAllChange"
      @row-click="onRowClick"
      @row-dblclick="onRowDblClick"
      @cell-edit-init="onCellEditInit"
      @cell-edit-complete="onCellEditComplete"
      @cell-edit-cancel="onCellEditCancel"
      @row-edit-init="onRowEditInit"
      @row-edit-save="onRowEditSave"
      @row-edit-cancel="onRowEditCancel"
      @column-resize-end="onColumnResizeEnd"
      @column-reorder="onColumnReorder"
      @row-reorder="onRowReorder"
      @row-expand="onRowExpand"
      @row-collapse="onRowCollapse"
      @contextmenu-selection-change="onContextMenuSelectionChange"
      @state-restore="onStateRestore"
      @state-save="onStateSave"
    >
      <!-- Selection column -->
      <Column 
        v-if="selectionMode === 'multiple'" 
        selectionMode="multiple" 
        headerStyle="width: 3rem"
      />
      
      <!-- Dynamic columns -->
      <Column
        v-for="col in columns"
        :key="col.field || col.key"
        :field="col.field"
        :header="col.header"
        :sortable="col.sortable"
        :filterField="col.filterField"
        :dataType="col.dataType"
        :sortField="col.sortField"
        :filterMatchMode="col.filterMatchMode"
        :filterFunction="col.filterFunction"
        :excludeGlobalFilter="col.excludeGlobalFilter"
        :filterHeaderClass="col.filterHeaderClass"
        :filterHeaderStyle="col.filterHeaderStyle"
        :filterMenuClass="col.filterMenuClass"
        :filterMenuStyle="col.filterMenuStyle"
        :selectionMode="col.selectionMode"
        :expander="col.expander"
        :rowReorder="col.rowReorder"
        :rowEditor="col.rowEditor"
        :frozen="col.frozen"
        :alignFrozen="col.alignFrozen"
        :exportable="col.exportable"
        :exportHeader="col.exportHeader"
        :exportFooter="col.exportFooter"
        :hidden="col.hidden"
        :headerStyle="col.headerStyle"
        :headerClass="col.headerClass"
        :bodyStyle="col.bodyStyle"
        :bodyClass="col.bodyClass"
        :footerStyle="col.footerStyle"
        :footerClass="col.footerClass"
        :showFilterMenu="col.showFilterMenu"
        :showFilterOperator="col.showFilterOperator"
        :showClearButton="col.showClearButton"
        :showApplyButton="col.showApplyButton"
        :showFilterMatchModes="col.showFilterMatchModes"
        :showAddButton="col.showAddButton"
        :filterMatchModeOptions="col.filterMatchModeOptions"
        :maxConstraints="col.maxConstraints"
        :style="col.style"
        :class="col.class"
      >
        <!-- Custom column header -->
        <template v-if="col.headerSlot" #header>
          <slot :name="col.headerSlot" :column="col" />
        </template>
        
        <!-- Custom column body -->
        <template v-if="col.bodySlot" #body="slotProps">
          <slot :name="col.bodySlot" :data="slotProps.data" :column="col" :index="slotProps.index" />
        </template>
        
        <!-- Custom column footer -->
        <template v-if="col.footerSlot" #footer>
          <slot :name="col.footerSlot" :column="col" />
        </template>
        
        <!-- Custom column filter -->
        <template v-if="col.filterSlot" #filter="slotProps">
          <slot :name="col.filterSlot" :value="slotProps.value" :filterCallback="slotProps.filterCallback" :column="col" />
        </template>
        
        <!-- Custom column editor -->
        <template v-if="col.editorSlot" #editor="slotProps">
          <slot :name="col.editorSlot" :data="slotProps.data" :column="col" :field="col.field" :index="slotProps.index" />
        </template>
      </Column>
      
      <!-- Actions column -->
      <Column v-if="showActions" header="Actions" :exportable="false" style="min-width: 8rem">
        <template #body="slotProps">
          <div class="flex items-center space-x-2">
            <PrimeButton 
              v-if="showViewAction"
              icon="pi pi-eye" 
              severity="info" 
              outlined 
              size="small"
              @click="$emit('view', slotProps.data)"
              v-tooltip="'View'"
            />
            <PrimeButton 
              v-if="showEditAction"
              icon="pi pi-pencil" 
              severity="success" 
              outlined 
              size="small"
              @click="$emit('edit', slotProps.data)"
              v-tooltip="'Edit'"
            />
            <PrimeButton 
              v-if="showDeleteAction"
              icon="pi pi-trash" 
              severity="danger" 
              outlined 
              size="small"
              @click="$emit('delete', slotProps.data)"
              v-tooltip="'Delete'"
            />
            
            <!-- Custom actions slot -->
            <slot name="row-actions" :data="slotProps.data" :index="slotProps.index" />
          </div>
        </template>
      </Column>
      
      <!-- Empty state -->
      <template #empty>
        <div class="text-center py-8">
          <div class="text-gray-400 text-6xl mb-4">
            <i class="pi pi-inbox"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ emptyStateTitle }}</h3>
          <p class="text-gray-500">{{ emptyStateMessage }}</p>
        </div>
      </template>
      
      <!-- Loading template -->
      <template #loading>
        <div class="flex items-center justify-center py-8">
          <ProgressBar mode="indeterminate" style="height: 6px" class="w-64" />
        </div>
      </template>
      
      <!-- Custom slots -->
      <template v-for="(_, name) in $slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </DataTable>

    <!-- Footer -->
    <div v-if="showFooter" class="mt-4 flex items-center justify-between text-sm text-gray-500">
      <div>
        Showing {{ ((currentPage - 1) * perPage) + 1 }} to {{ Math.min(currentPage * perPage, totalItems) }} of {{ totalItems }} entries
      </div>
      <div v-if="selectedItems && selectedItems.length > 0">
        {{ selectedItems.length }} item(s) selected
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// Define interfaces for better type safety
interface Column {
  field?: string;
  key?: string;
  header: string;
  sortable?: boolean;
  filterField?: string;
  dataType?: string;
  sortField?: string;
  filterMatchMode?: string;
  filterFunction?: Function;
  excludeGlobalFilter?: boolean;
  filterHeaderClass?: string;
  filterHeaderStyle?: any;
  filterMenuClass?: string;
  filterMenuStyle?: any;
  selectionMode?: string;
  expander?: boolean;
  rowReorder?: boolean;
  rowEditor?: boolean;
  frozen?: boolean;
  alignFrozen?: string;
  exportable?: boolean;
  exportHeader?: string;
  exportFooter?: string;
  hidden?: boolean;
  headerStyle?: any;
  headerClass?: string;
  bodyStyle?: any;
  bodyClass?: string;
  footerStyle?: any;
  footerClass?: string;
  showFilterMenu?: boolean;
  showFilterOperator?: boolean;
  showClearButton?: boolean;
  showApplyButton?: boolean;
  showFilterMatchModes?: boolean;
  showAddButton?: boolean;
  filterMatchModeOptions?: any[];
  maxConstraints?: number;
  style?: any;
  class?: string;
  headerSlot?: string;
  bodySlot?: string;
  footerSlot?: string;
  filterSlot?: string;
  editorSlot?: string;
}

interface Props {
  // Data props
  items?: any[];
  columns?: Column[];
  loading?: boolean;
  
  // Header props
  title?: string;
  subtitle?: string;
  showToolbar?: boolean;
  
  // Filter props
  showGlobalFilter?: boolean;
  globalFilterPlaceholder?: string;
  globalFilterFields?: string[];
  filters?: any;
  filterMode?: string;
  filterLocale?: string;
  
  // Pagination props
  showPagination?: boolean;
  currentPage?: number;
  totalItems?: number;
  perPage?: number;
  lazy?: boolean;
  
  // Sorting props
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  sortMode?: string;
  defaultSortOrder?: number;
  multiSortMeta?: any[];
  removableSort?: boolean;
  
  // Selection props
  selectionMode?: 'single' | 'multiple' | null;
  selectedItems?: any[];
  dataKey?: string;
  compareSelectionBy?: string;
  metaKeySelection?: boolean;
  dragSelection?: boolean;
  
  // Appearance props
  striped?: boolean;
  showGridlines?: boolean;
  tableSize?: 'small' | 'normal' | 'large';
  responsiveLayout?: string;
  breakpoint?: string;
  
  // Scrolling props
  scrollable?: boolean;
  scrollHeight?: string;
  virtualScrollerOptions?: any;
  
  // Row props
  rowClass?: Function;
  rowStyle?: Function;
  expandedRows?: any[];
  
  // Editing props
  editMode?: string;
  editingRows?: any[];
  
  // Grouping props
  rowGroupMode?: string;
  groupRowsBy?: string;
  expandableRowGroups?: boolean;
  rowGroupHeaderTemplate?: string;
  rowGroupFooterTemplate?: string;
  
  // Column props
  resizableColumns?: boolean;
  columnResizeMode?: string;
  reorderableColumns?: boolean;
  
  // Context menu props
  contextMenu?: boolean;
  contextMenuSelection?: any;
  
  // Row reorder props
  rowReorder?: boolean;
  
  // Export props
  csvSeparator?: string;
  exportFilename?: string;
  
  // State props
  stateStorage?: string;
  stateKey?: string;
  
  // Style props
  autoLayout?: boolean;
  tableStyle?: any;
  tableClass?: string;
  
  // Action props
  showActions?: boolean;
  showViewAction?: boolean;
  showEditAction?: boolean;
  showDeleteAction?: boolean;
  
  // Toolbar props
  showRefresh?: boolean;
  showExport?: boolean;
  showCreateButton?: boolean;
  createButtonText?: string;
  
  // Footer props
  showFooter?: boolean;
  
  // Empty state props
  emptyStateTitle?: string;
  emptyStateMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  columns: () => [],
  loading: false,
  showToolbar: true,
  showGlobalFilter: true,
  globalFilterPlaceholder: 'Search...',
  globalFilterFields: () => [],
  showPagination: true,
  currentPage: 1,
  totalItems: 0,
  perPage: 10,
  lazy: false,
  sortOrder: 'asc',
  sortMode: 'single',
  defaultSortOrder: 1,
  removableSort: false,
  selectionMode: null,
  selectedItems: () => [],
  dataKey: 'id',
  compareSelectionBy: 'deepEquals',
  metaKeySelection: true,
  dragSelection: false,
  striped: false,
  showGridlines: true,
  tableSize: 'normal',
  responsiveLayout: 'scroll',
  breakpoint: '960px',
  scrollable: false,
  scrollHeight: '400px',
  editMode: 'cell',
  editingRows: () => [],
  resizableColumns: false,
  columnResizeMode: 'fit',
  reorderableColumns: false,
  contextMenu: false,
  rowReorder: false,
  csvSeparator: ',',
  exportFilename: 'data',
  stateStorage: 'session',
  stateKey: 'dt-state',
  autoLayout: false,
  showActions: true,
  showViewAction: true,
  showEditAction: true,
  showDeleteAction: true,
  showRefresh: true,
  showExport: true,
  showCreateButton: true,
  createButtonText: 'Create New',
  showFooter: true,
  emptyStateTitle: 'No data found',
  emptyStateMessage: 'There are no items to display.'
});

const emit = defineEmits<{
  // Data events
  'page': [event: any];
  'sort': [event: any];
  'filter': [event: any];

  // Selection events
  'row-select': [event: any];
  'row-unselect': [event: any];
  'select-all-change': [event: any];

  // Row events
  'row-click': [event: any];
  'row-dblclick': [event: any];

  // Edit events
  'cell-edit-init': [event: any];
  'cell-edit-complete': [event: any];
  'cell-edit-cancel': [event: any];
  'row-edit-init': [event: any];
  'row-edit-save': [event: any];
  'row-edit-cancel': [event: any];

  // Column events
  'column-resize-end': [event: any];
  'column-reorder': [event: any];

  // Row reorder events
  'row-reorder': [event: any];

  // Expand/collapse events
  'row-expand': [event: any];
  'row-collapse': [event: any];

  // Context menu events
  'contextmenu-selection-change': [event: any];

  // State events
  'state-restore': [event: any];
  'state-save': [event: any];

  // Action events
  'view': [item: any];
  'edit': [item: any];
  'delete': [item: any];
  'create': [];
  'refresh': [];
  'export': [];
}>();

// Reactive state
const globalFilterValue = ref('');
const refreshing = ref(false);
const exporting = ref(false);

// Event handlers
const onGlobalFilter = () => {
  // Global filter is handled by PrimeVue DataTable automatically
};

const onPage = (event: any) => {
  emit('page', event);
};

const onSort = (event: any) => {
  emit('sort', event);
};

const onFilter = (event: any) => {
  emit('filter', event);
};

const onRowSelect = (event: any) => {
  emit('row-select', event);
};

const onRowUnselect = (event: any) => {
  emit('row-unselect', event);
};

const onSelectAllChange = (event: any) => {
  emit('select-all-change', event);
};

const onRowClick = (event: any) => {
  emit('row-click', event);
};

const onRowDblClick = (event: any) => {
  emit('row-dblclick', event);
};

const onCellEditInit = (event: any) => {
  emit('cell-edit-init', event);
};

const onCellEditComplete = (event: any) => {
  emit('cell-edit-complete', event);
};

const onCellEditCancel = (event: any) => {
  emit('cell-edit-cancel', event);
};

const onRowEditInit = (event: any) => {
  emit('row-edit-init', event);
};

const onRowEditSave = (event: any) => {
  emit('row-edit-save', event);
};

const onRowEditCancel = (event: any) => {
  emit('row-edit-cancel', event);
};

const onColumnResizeEnd = (event: any) => {
  emit('column-resize-end', event);
};

const onColumnReorder = (event: any) => {
  emit('column-reorder', event);
};

const onRowReorder = (event: any) => {
  emit('row-reorder', event);
};

const onRowExpand = (event: any) => {
  emit('row-expand', event);
};

const onRowCollapse = (event: any) => {
  emit('row-collapse', event);
};

const onContextMenuSelectionChange = (event: any) => {
  emit('contextmenu-selection-change', event);
};

const onStateRestore = (event: any) => {
  emit('state-restore', event);
};

const onStateSave = (event: any) => {
  emit('state-save', event);
};

const handleRefresh = async () => {
  refreshing.value = true;
  try {
    emit('refresh');
  } finally {
    setTimeout(() => {
      refreshing.value = false;
    }, 500);
  }
};

const handleExport = async () => {
  exporting.value = true;
  try {
    emit('export');
  } finally {
    setTimeout(() => {
      exporting.value = false;
    }, 500);
  }
};
</script>

<style scoped>
.prime-data-table-wrapper {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
  padding: 1.5rem;
}

/* Custom PrimeVue DataTable styling to match admin theme */
:deep(.p-datatable) {
  @apply border-0;
}

:deep(.p-datatable .p-datatable-header) {
  @apply bg-gray-50 border-b border-gray-200;
  padding: 1rem;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  @apply bg-gray-50 text-gray-700 font-semibold text-sm;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  @apply text-gray-900 text-sm;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
  @apply bg-gray-50;
}

:deep(.p-datatable .p-datatable-tbody > tr.p-highlight) {
  @apply bg-blue-50;
}

:deep(.p-datatable .p-datatable-footer) {
  @apply bg-gray-50 border-t border-gray-200;
  padding: 1rem;
}

:deep(.p-paginator) {
  @apply bg-white border-t border-gray-200;
  padding: 1rem;
}

:deep(.p-toolbar) {
  @apply bg-white border border-gray-200 rounded-lg;
  padding: 1rem;
}

:deep(.p-button.p-button-outlined) {
  @apply border-gray-300 text-gray-700;
}

:deep(.p-button.p-button-outlined:hover) {
  @apply bg-gray-50 border-gray-400;
}

:deep(.p-inputtext) {
  @apply border-gray-300 rounded-md;
}

:deep(.p-inputtext:focus) {
  @apply border-blue-500 ring-1 ring-blue-500;
}
</style>
