import{h as G,r as p,i as K,o as l,f as r,g as s,t as n,j as i,n as Y,B as z,l as q,F as J,k as Q,m as w,x as m,H as h,u as v,_ as g,v as L,e as X}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{_ as Z}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";const ee={class:"admin-file-upload"},te={class:"space-y-2"},se={class:"text-sm font-medium text-gray-900"},ae={class:"text-xs text-gray-500"},le={class:"text-xs text-gray-400 space-y-1"},oe={key:0},re={key:1},ne={key:2},ie=["multiple","accept","disabled"],de={key:0,class:"mt-4"},ue={class:"flex items-center justify-between text-sm text-gray-600 mb-2"},ce={class:"w-full bg-gray-200 rounded-full h-2"},pe={key:1,class:"mt-4 space-y-2"},me={class:"text-sm font-medium text-gray-900"},ve={class:"space-y-2 max-h-60 overflow-y-auto"},fe={class:"flex items-center space-x-3 flex-1 min-w-0"},ye={class:"flex-1 min-w-0"},he={class:"text-sm font-medium text-gray-900 truncate"},ge={class:"flex items-center space-x-2 text-xs text-gray-500"},xe={key:0},_e={key:1,class:"text-gray-400"},we={class:"flex items-center space-x-2"},be={class:"space-y-4"},ke={key:0,class:"text-center"},Ce=["src","alt"],ze={key:1,class:"bg-gray-50 p-4 rounded-lg"},Fe={class:"text-sm text-gray-800 whitespace-pre-wrap"},Me={key:2,class:"text-center py-8"},Be={class:"text-xs text-gray-500 mt-1"},Ae={class:"flex justify-end space-x-3"},De=G({__name:"AdminFileUpload",props:{modelValue:{default:()=>[]},multiple:{type:Boolean,default:!1},accept:{default:"*"},maxSize:{default:10},disabled:{type:Boolean,default:!1},uploadText:{default:"Click to upload or drag and drop"},supportText:{default:"Select files from your computer"}},emits:["update:modelValue","upload","remove","preview"],setup(S,{emit:T}){const d=S,f=T,F=p(),x=p(!1),U=p(!1),M=p(0),u=p([...d.modelValue]),b=p(!1),k=p(null),_=p(""),c=K(()=>k.value),$=()=>{var e;d.disabled||(e=F.value)==null||e.click()},j=e=>{const t=e.target;t.files&&B(Array.from(t.files))},W=e=>{d.disabled||(x.value=!0)},P=()=>{x.value=!1},R=e=>{var t;x.value=!1,!d.disabled&&((t=e.dataTransfer)!=null&&t.files)&&B(Array.from(e.dataTransfer.files))},B=e=>{const t=e.filter(a=>d.maxSize&&a.size>d.maxSize*1024*1024?(console.warn(`File ${a.name} is too large`),!1):d.accept&&d.accept!=="*"&&!d.accept.split(",").map(y=>y.trim()).some(y=>y.startsWith(".")?a.name.toLowerCase().endsWith(y.toLowerCase()):a.type.match(y.replace("*",".*")))?(console.warn(`File ${a.name} is not an accepted type`),!1):!0);if(t.length>0){const a=t.map(o=>({id:Math.random().toString(36).substr(2,9),name:o.name,size:o.size,type:o.type,lastModified:o.lastModified,file:o,url:URL.createObjectURL(o)}));d.multiple?u.value=[...u.value,...a]:u.value=a,f("update:modelValue",u.value),f("upload",t)}},H=(e,t)=>{e.url&&e.url.startsWith("blob:")&&URL.revokeObjectURL(e.url),u.value.splice(t,1),f("update:modelValue",u.value),f("remove",e,t)},I=e=>e.type?e.type.startsWith("image/")||e.type.startsWith("text/"):!1,N=async e=>{var t;if(k.value=e,b.value=!0,(t=e.type)!=null&&t.startsWith("text/")&&e.file)try{_.value=await e.file.text()}catch{_.value="Error reading file content"}f("preview",e)},A=()=>{b.value=!1,k.value=null,_.value=""},D=e=>{if(e.url){const t=document.createElement("a");t.href=e.url,t.download=e.name,document.body.appendChild(t),t.click(),document.body.removeChild(t)}},O=e=>e.url||"",V=e=>{if(e===0)return"0 Bytes";const t=1024,a=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,o)).toFixed(2))+" "+a[o]},E=e=>new Date(e).toLocaleDateString();return(e,t)=>(l(),r("div",ee,[s("div",{class:Y(["border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer",x.value?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400",e.disabled?"opacity-50 cursor-not-allowed":""]),onClick:$,onDragover:z(W,["prevent"]),onDragleave:z(P,["prevent"]),onDrop:z(R,["prevent"])},[s("div",te,[t[1]||(t[1]=s("div",{class:"mx-auto h-12 w-12 text-gray-400"},[s("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})])],-1)),s("div",null,[s("p",se,n(e.uploadText),1),s("p",ae,n(e.supportText),1)]),s("div",le,[e.maxSize?(l(),r("p",oe,"Maximum file size: "+n(V(e.maxSize*1024*1024)),1)):i("",!0),e.accept&&e.accept!=="*"?(l(),r("p",re,"Accepted formats: "+n(e.accept),1)):i("",!0),e.multiple?(l(),r("p",ne,"You can upload multiple files")):i("",!0)])])],34),s("input",{ref_key:"fileInput",ref:F,type:"file",multiple:e.multiple,accept:e.accept,disabled:e.disabled,class:"hidden",onChange:j},null,40,ie),U.value?(l(),r("div",de,[s("div",ue,[t[2]||(t[2]=s("span",null,"Uploading files...",-1)),s("span",null,n(M.value)+"%",1)]),s("div",ce,[s("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:q({width:`${M.value}%`})},null,4)])])):i("",!0),u.value.length>0?(l(),r("div",pe,[s("h4",me," Uploaded Files ("+n(u.value.length)+") ",1),s("div",ve,[(l(!0),r(J,null,Q(u.value,(a,o)=>(l(),r("div",{key:a.id||o,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg border"},[s("div",fe,[t[3]||(t[3]=s("div",{class:"flex-shrink-0"},[s("div",{class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},[s("svg",{class:"w-4 h-4 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[s("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})])])],-1)),s("div",ye,[s("p",he,n(a.name),1),s("div",ge,[s("span",null,n(V(a.size)),1),a.type?(l(),r("span",xe,n(a.type),1)):i("",!0),a.lastModified?(l(),r("span",_e,n(E(a.lastModified)),1)):i("",!0)])])]),s("div",we,[I(a)?(l(),w(v(g),{key:0,variant:"ghost",size:"sm",onClick:C=>N(a),class:"text-blue-600 hover:text-blue-700"},{default:m(()=>t[4]||(t[4]=[h(" Preview ")])),_:2,__:[4]},1032,["onClick"])):i("",!0),a.url?(l(),w(v(g),{key:1,variant:"ghost",size:"sm",onClick:C=>D(a),class:"text-green-600 hover:text-green-700"},{default:m(()=>t[5]||(t[5]=[h(" Download ")])),_:2,__:[5]},1032,["onClick"])):i("",!0),L(v(g),{variant:"ghost",size:"sm",onClick:C=>H(a,o),disabled:e.disabled,class:"text-red-600 hover:text-red-700"},{default:m(()=>t[6]||(t[6]=[h(" Remove ")])),_:2,__:[6]},1032,["onClick","disabled"])])]))),128))])])):i("",!0),c.value&&b.value?(l(),w(v(X),{key:2,onClose:A},{header:m(()=>t[7]||(t[7]=[s("h3",{class:"text-lg font-medium"},"File Preview",-1)])),footer:m(()=>[s("div",Ae,[L(v(g),{variant:"outline",onClick:A},{default:m(()=>t[10]||(t[10]=[h("Close")])),_:1,__:[10]}),c.value.url?(l(),w(v(g),{key:0,variant:"primary",onClick:t[0]||(t[0]=a=>D(c.value))},{default:m(()=>t[11]||(t[11]=[h(" Download ")])),_:1,__:[11]})):i("",!0)])]),default:m(()=>{var a,o;return[s("div",be,[(a=c.value.type)!=null&&a.startsWith("image/")?(l(),r("div",ke,[s("img",{src:O(c.value),alt:c.value.name,class:"max-w-full max-h-96 mx-auto rounded-lg"},null,8,Ce)])):(o=c.value.type)!=null&&o.startsWith("text/")?(l(),r("div",ze,[s("pre",Fe,n(_.value),1)])):(l(),r("div",Me,[t[8]||(t[8]=s("div",{class:"mx-auto h-16 w-16 text-gray-400 mb-4"},[s("svg",{fill:"currentColor",viewBox:"0 0 20 20"},[s("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z","clip-rule":"evenodd"})])],-1)),t[9]||(t[9]=s("p",{class:"text-sm text-gray-600"},"Preview not available for this file type",-1)),s("p",Be,n(c.value.name),1)]))])]}),_:1})):i("",!0)]))}}),Te=Z(De,[["__scopeId","data-v-5439cc60"]]);export{Te as A};
