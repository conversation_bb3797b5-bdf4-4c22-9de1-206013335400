import{h,r as o,i as u,L as f,o as _,f as y,g as t,t as e,C as w,D as k,v as C,x as T,u as D,H as V,_ as B}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as A}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";const N={class:"min-h-screen bg-gray-100 p-8"},S={class:"max-w-4xl mx-auto"},R={class:"bg-white rounded-lg shadow-lg p-6"},F={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},I={class:"space-y-4"},L={class:"p-4 bg-blue-50 rounded-lg"},M={class:"text-blue-600"},U={class:"p-4 bg-green-50 rounded-lg"},E={class:"mt-2 text-green-600"},G={class:"p-4 bg-purple-50 rounded-lg"},J={class:"text-purple-600"},O={class:"text-purple-600"},P={class:"space-y-4"},H={class:"p-4 bg-yellow-50 rounded-lg"},W={class:"text-sm text-yellow-700 mt-2"},Y={class:"p-4 bg-indigo-50 rounded-lg"},$={class:"text-indigo-600 text-sm break-all"},j={class:"p-4 bg-pink-50 rounded-lg"},q={class:"text-sm text-pink-700 mt-2"},z={class:"mt-8 p-4 bg-gray-50 rounded-lg"},tt=h({__name:"AdminVue3Test",setup(K){const i=A(),l=o(0),n=o(""),a=o(window.user||null),d=o(window.csrfToken||""),r=o(window.branches||[]),g=u(()=>l.value*2),p=u(()=>n.value.length),c=()=>{l.value++},m=()=>{i.push("/admin-spa/dashboard")},x=()=>{i.push("/admin-spa/auctions/list")},b=()=>{alert("Button component is working!")};return f(()=>{console.log("Vue 3 Test Component mounted successfully!"),console.log("User:",a.value),console.log("CSRF Token:",d.value),console.log("Branches:",r.value)}),(Q,s)=>(_(),y("div",N,[t("div",S,[t("div",R,[s[12]||(s[12]=t("h1",{class:"text-3xl font-bold text-green-600 mb-6"}," ✅ Vue 3 is Working! ",-1)),t("div",F,[t("div",I,[s[4]||(s[4]=t("h2",{class:"text-xl font-semibold text-gray-800"},"Vue 3 Features",-1)),t("div",L,[s[1]||(s[1]=t("h3",{class:"font-medium text-blue-800"},"Composition API",-1)),t("p",M,"Counter: "+e(l.value),1),t("button",{onClick:c,class:"mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"}," Increment ")]),t("div",U,[s[2]||(s[2]=t("h3",{class:"font-medium text-green-800"},"Reactive Data",-1)),w(t("input",{"onUpdate:modelValue":s[0]||(s[0]=v=>n.value=v),placeholder:"Type something...",class:"w-full p-2 border rounded"},null,512),[[k,n.value]]),t("p",E,"You typed: "+e(n.value),1)]),t("div",G,[s[3]||(s[3]=t("h3",{class:"font-medium text-purple-800"},"Computed Properties",-1)),t("p",J,"Doubled counter: "+e(g.value),1),t("p",O,"Message length: "+e(p.value),1)])]),t("div",P,[s[8]||(s[8]=t("h2",{class:"text-xl font-semibold text-gray-800"},"Environment Info",-1)),t("div",H,[s[5]||(s[5]=t("h3",{class:"font-medium text-yellow-800"},"User Authentication",-1)),t("pre",W,e(JSON.stringify(a.value,null,2)),1)]),t("div",Y,[s[6]||(s[6]=t("h3",{class:"font-medium text-indigo-800"},"CSRF Token",-1)),t("p",$,e(d.value),1)]),t("div",j,[s[7]||(s[7]=t("h3",{class:"font-medium text-pink-800"},"Branches Data",-1)),t("pre",q,e(JSON.stringify(r.value,null,2)),1)])])]),t("div",{class:"mt-8 p-4 bg-gray-50 rounded-lg"},[s[9]||(s[9]=t("h2",{class:"text-xl font-semibold text-gray-800 mb-4"},"Navigation Test",-1)),t("div",{class:"space-x-4"},[t("button",{onClick:m,class:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"}," Go to Dashboard "),t("button",{onClick:x,class:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"}," Go to Auctions List ")])]),t("div",z,[s[11]||(s[11]=t("h2",{class:"text-xl font-semibold text-gray-800 mb-4"},"Component Test",-1)),C(D(B),{variant:"primary",onClick:b},{default:T(()=>s[10]||(s[10]=[V("Test Button Component")])),_:1,__:[10]})])])])]))}});export{tt as default};
