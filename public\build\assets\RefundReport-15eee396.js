import{h as A,r as i,L as D,o as r,f as l,g as e,n as y,H as h,t as n,C as x,Q as g,p as b,F as w,k,j as H}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{c as F}from"./export-37598506.js";const S={class:"space-y-6"},N={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},z={class:"flex items-center justify-between mb-6"},L={class:"flex items-center space-x-3"},E=["disabled"],q={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},P={class:"bg-red-50 border border-red-200 rounded-lg p-4"},U={class:"flex items-center"},$={class:"ml-4"},X={class:"text-2xl font-semibold text-red-900"},W={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},G={class:"flex items-center"},I={class:"ml-4"},Q={class:"text-2xl font-semibold text-yellow-900"},Y={class:"bg-green-50 border border-green-200 rounded-lg p-4"},J={class:"flex items-center"},K={class:"ml-4"},O={class:"text-2xl font-semibold text-green-900"},Z={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-semibold text-blue-900"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ne=["value"],re={key:0,class:"flex items-center justify-center py-12"},le={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-6"},ae={class:"flex items-center"},ie={class:"text-red-800"},de={key:2,class:"overflow-x-auto"},ue={class:"min-w-full divide-y divide-gray-200"},ce={class:"bg-white divide-y divide-gray-200"},pe={class:"px-6 py-4 whitespace-nowrap"},me={class:"text-sm font-medium text-gray-900"},xe={class:"px-6 py-4 whitespace-nowrap"},ge={class:"text-sm font-medium text-gray-900"},fe={class:"text-sm text-gray-500"},ve={class:"px-6 py-4 whitespace-nowrap"},ye={class:"text-sm font-medium text-gray-900"},he={class:"text-sm text-gray-500"},be={class:"px-6 py-4 whitespace-nowrap"},we={class:"text-sm font-medium text-gray-900"},ke={class:"px-6 py-4 whitespace-nowrap"},_e={class:"text-sm text-gray-900"},Re={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},je={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Me=["onClick"],Te=["onClick"],Ve={key:3,class:"text-center py-12"},He=A({__name:"RefundReport",setup(Be){const d=i(!1),p=i(""),u=i([]),f=i([]),c=i({totalRefunds:0,pendingRefunds:0,completedRefunds:0,totalAmount:0}),a=i({dateRange:"month",status:"",branch:""}),m=async()=>{d.value=!0,p.value="";try{const o=await fetch("/api/admin/reports/refunds",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);const t=await o.json();if(t.success)u.value=t.refunds||[],c.value=t.summary||{totalRefunds:0,pendingRefunds:0,completedRefunds:0,totalAmount:0};else throw new Error(t.error||"Failed to fetch refund data")}catch(o){p.value=o.message||"Failed to load refund data",console.error("Refunds report error:",o)}finally{d.value=!1}},_=async()=>{try{const o=await fetch("/api/admin/reports/branches",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(o.ok){const t=await o.json();t.success&&(f.value=t.branches||[])}}catch(o){console.error("Failed to load branches:",o)}},R=()=>{m()},C=()=>{if(u.value.length===0){alert("No data to export");return}F(u.value,"csv")},j=o=>{console.log("Viewing refund:",o)},M=o=>{console.log("Processing refund:",o)},v=o=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(o),T=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),V=o=>({pending:"bg-yellow-100 text-yellow-800",approved:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800";return D(()=>{m(),_()}),(o,t)=>(r(),l("div",S,[e("div",N,[e("div",z,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Refund List Report"),e("p",{class:"text-gray-600 mt-1"},"View refund transactions and status")],-1)),e("div",L,[e("button",{onClick:m,disabled:d.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[(r(),l("svg",{class:y(["w-4 h-4 mr-2",{"animate-spin":d.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[3]||(t[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),t[4]||(t[4]=h(" Refresh "))],8,E),e("button",{onClick:C,class:"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),h(" Export ")]))])]),e("div",q,[e("div",P,[e("div",U,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"})])],-1)),e("div",$,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-red-600"},"Total Refunds",-1)),e("p",X,n(c.value.totalRefunds),1)])])]),e("div",W,[e("div",G,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",I,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-yellow-600"},"Pending",-1)),e("p",Q,n(c.value.pendingRefunds),1)])])]),e("div",Y,[e("div",J,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",K,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-green-600"},"Completed",-1)),e("p",O,n(c.value.completedRefunds),1)])])]),e("div",Z,[e("div",ee,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",te,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-blue-600"},"Total Amount",-1)),e("p",se,"$"+n(v(c.value.totalAmount)),1)])])])]),e("div",oe,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),x(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>a.value.dateRange=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},t[15]||(t[15]=[b('<option value="today">Today</option><option value="week">This Week</option><option value="month">This Month</option><option value="quarter">This Quarter</option><option value="year">This Year</option><option value="custom">Custom Range</option>',6)]),512),[[g,a.value.dateRange]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),x(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>a.value.status=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},t[17]||(t[17]=[b('<option value="">All Statuses</option><option value="pending">Pending</option><option value="approved">Approved</option><option value="completed">Completed</option><option value="rejected">Rejected</option>',5)]),512),[[g,a.value.status]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Branch",-1)),x(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>a.value.branch=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[t[19]||(t[19]=e("option",{value:""},"All Branches",-1)),(r(!0),l(w,null,k(f.value,s=>(r(),l("option",{key:s.id,value:s.id},n(s.name),9,ne))),128))],512),[[g,a.value.branch]])]),e("div",{class:"flex items-end"},[e("button",{onClick:R,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Apply Filters ")])]),d.value?(r(),l("div",re,t[21]||(t[21]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"ml-2 text-gray-600"},"Loading refund data...",-1)]))):p.value?(r(),l("div",le,[e("div",ae,[t[22]||(t[22]=e("svg",{class:"h-5 w-5 text-red-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("span",ie,n(p.value),1)])])):u.value.length>0?(r(),l("div",de,[e("table",ue,[t[23]||(t[23]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Transaction ID"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Customer"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Auction"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Amount"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Reason"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",ce,[(r(!0),l(w,null,k(u.value,s=>(r(),l("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",pe,[e("div",me,"#"+n(s.transaction_id),1)]),e("td",xe,[e("div",ge,n(s.customer_name),1),e("div",fe,n(s.customer_email),1)]),e("td",ve,[e("div",ye,n(s.auction_title),1),e("div",he,"#"+n(s.auction_id),1)]),e("td",be,[e("div",we,"$"+n(v(s.amount)),1)]),e("td",ke,[e("div",_e,n(s.reason),1)]),e("td",Re,[e("span",{class:y([V(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(s.status),3)]),e("td",Ce,n(T(s.created_at)),1),e("td",je,[e("button",{onClick:B=>j(s),class:"text-blue-600 hover:text-blue-900 mr-3"}," View ",8,Me),s.status==="pending"?(r(),l("button",{key:0,onClick:B=>M(s),class:"text-green-600 hover:text-green-900"}," Process ",8,Te)):H("",!0)])]))),128))])])])):(r(),l("div",Ve,t[24]||(t[24]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No refunds found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No refund transactions match the current filters.",-1)])))])]))}});export{He as default};
