import{h as V,r as f,L as F,o as r,m as v,x as n,u as o,f as m,v as l,g as t,H as u,t as i,d as k,F as N,k as $,j as x,_ as p}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as L,f as Z}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{A as j}from"./AdminDetailTemplate-67d04137.js";import{A}from"./AdminBadge-74cb3994.js";import{u as J}from"./useNotifications-98e2c61c.js";const R={key:0,class:"space-y-6"},H={class:"p-6"},I={class:"flex items-center justify-between mb-4"},M={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},O={class:"mt-1 text-sm text-gray-900"},W={class:"mt-1 text-sm text-gray-900"},q={class:"mt-1 text-sm text-gray-900"},z={class:"mt-1 text-sm text-gray-900"},G={class:"mt-1 text-sm text-gray-900"},K={class:"mt-1 text-sm text-gray-900"},Q={class:"mt-6"},U={class:"mt-1 text-sm text-gray-900"},X={class:"p-6"},Y={class:"overflow-x-auto"},tt={class:"min-w-full divide-y divide-gray-200"},et={class:"bg-white divide-y divide-gray-200"},st={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},at={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},it={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},nt={class:"px-6 py-4 whitespace-nowrap"},ot={key:0,class:"text-center py-8"},rt={class:"flex justify-end space-x-4"},yt=V({__name:"AuctionDetail",setup(dt){const h=L(),B=Z(),{showNotification:c}=J(),y=f(!1),g=f(null),s=f(null),_=async()=>{y.value=!0,g.value=null;try{const a=B.params.id;await new Promise(e=>setTimeout(e,1e3)),s.value={id:a,title:"Vintage Watch Collection",description:"A collection of rare vintage watches from the 1920s-1950s",starting_price:"150.00",current_bid:"275.00",start_date:"2024-02-01T10:00:00Z",end_date:"2024-02-15T18:00:00Z",status:"active",total_bids:12,recent_bids:[{id:1,bidder_name:"John Smith",amount:"275.00",created_at:"2024-02-10T14:30:00Z",status:"active"},{id:2,bidder_name:"Jane Doe",amount:"250.00",created_at:"2024-02-10T12:15:00Z",status:"outbid"},{id:3,bidder_name:"Bob Johnson",amount:"225.00",created_at:"2024-02-09T16:45:00Z",status:"outbid"}]}}catch(a){g.value="Failed to load auction details",console.error("Error fetching auction:",a)}finally{y.value=!1}},w=()=>{h.push("/admin-spa/auctions/list")},D=()=>{h.push(`/admin-spa/auctions/edit/${s.value.id}`)},T=async()=>{try{c({type:"success",title:"Auction Published",message:"Auction has been successfully published."}),await _()}catch{c({type:"error",title:"Publish Failed",message:"Failed to publish auction. Please try again."})}},C=async()=>{try{c({type:"success",title:"Auction Ended",message:"Auction has been successfully ended."}),await _()}catch{c({type:"error",title:"End Failed",message:"Failed to end auction. Please try again."})}},E=a=>{switch(a){case"active":return"success";case"ended":return"secondary";case"draft":return"warning";default:return"secondary"}},S=a=>{switch(a){case"active":return"success";case"outbid":return"secondary";default:return"secondary"}},b=a=>new Date(a).toLocaleDateString(),P=a=>new Date(a).toLocaleString();return F(()=>{_()}),(a,e)=>(r(),v(o(j),{title:"Auction Details",subtitle:"View auction information and bids",loading:y.value,error:g.value,onBack:w},{default:n(()=>[s.value?(r(),m("div",R,[l(o(k),null,{default:n(()=>[t("div",H,[t("div",I,[e[0]||(e[0]=t("h3",{class:"text-lg font-medium text-gray-900"},"Auction Overview",-1)),l(o(A),{variant:E(s.value.status)},{default:n(()=>[u(i(s.value.status),1)]),_:1},8,["variant"])]),t("div",M,[t("div",null,[e[1]||(e[1]=t("dt",{class:"text-sm font-medium text-gray-500"},"Title",-1)),t("dd",O,i(s.value.title),1)]),t("div",null,[e[2]||(e[2]=t("dt",{class:"text-sm font-medium text-gray-500"},"Starting Price",-1)),t("dd",W,"$"+i(s.value.starting_price),1)]),t("div",null,[e[3]||(e[3]=t("dt",{class:"text-sm font-medium text-gray-500"},"Current Bid",-1)),t("dd",q,"$"+i(s.value.current_bid),1)]),t("div",null,[e[4]||(e[4]=t("dt",{class:"text-sm font-medium text-gray-500"},"Start Date",-1)),t("dd",z,i(b(s.value.start_date)),1)]),t("div",null,[e[5]||(e[5]=t("dt",{class:"text-sm font-medium text-gray-500"},"End Date",-1)),t("dd",G,i(b(s.value.end_date)),1)]),t("div",null,[e[6]||(e[6]=t("dt",{class:"text-sm font-medium text-gray-500"},"Total Bids",-1)),t("dd",K,i(s.value.total_bids),1)])]),t("div",Q,[e[7]||(e[7]=t("dt",{class:"text-sm font-medium text-gray-500"},"Description",-1)),t("dd",U,i(s.value.description),1)])])]),_:1}),l(o(k),null,{default:n(()=>[t("div",X,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Recent Bids",-1)),t("div",Y,[t("table",tt,[e[8]||(e[8]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Bidder "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Amount "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Time "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status ")])],-1)),t("tbody",et,[(r(!0),m(N,null,$(s.value.recent_bids,d=>(r(),m("tr",{key:d.id},[t("td",st,i(d.bidder_name),1),t("td",at," $"+i(d.amount),1),t("td",it,i(P(d.created_at)),1),t("td",nt,[l(o(A),{variant:S(d.status)},{default:n(()=>[u(i(d.status),1)]),_:2},1032,["variant"])])]))),128))])])]),s.value.recent_bids.length===0?(r(),m("div",ot,e[9]||(e[9]=[t("p",{class:"text-gray-500"},"No bids yet",-1)]))):x("",!0)])]),_:1}),t("div",rt,[l(o(p),{variant:"outline",onClick:w},{default:n(()=>e[11]||(e[11]=[u(" Back to List ")])),_:1,__:[11]}),l(o(p),{variant:"outline",onClick:D},{default:n(()=>e[12]||(e[12]=[u(" Edit Auction ")])),_:1,__:[12]}),s.value.status==="draft"?(r(),v(o(p),{key:0,onClick:T},{default:n(()=>e[13]||(e[13]=[u(" Publish Auction ")])),_:1,__:[13]})):x("",!0),s.value.status==="active"?(r(),v(o(p),{key:1,variant:"danger",onClick:C},{default:n(()=>e[14]||(e[14]=[u(" End Auction ")])),_:1,__:[14]})):x("",!0)])])):x("",!0)]),_:1},8,["loading","error"]))}});export{yt as default};
