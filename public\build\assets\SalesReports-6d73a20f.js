import{h as A,r as d,L as j,o as y,m as D,x as s,u as l,g as e,v as r,H as m,_ as c,d as v,f,k as M,t as i,n as U,F as G}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{A as x,a as P}from"./app-admin-1baa1658.js";import{h}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";const W={class:"flex space-x-3"},F={class:"p-6"},H={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},N={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},E={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},$={class:"p-6"},q={class:"flex items-center justify-between mb-6"},L={class:"flex space-x-2"},Q={class:"overflow-x-auto"},I={class:"min-w-full divide-y divide-gray-200"},Y={class:"bg-white divide-y divide-gray-200"},J={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},K={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},X={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Z={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ee={class:"px-6 py-4 whitespace-nowrap"},se=A({__name:"SalesReports",setup(te){const b=d(!1),k=d(null),o=d({dateRange:"month",reportType:"sales",groupBy:"day",status:"all"}),w=[{value:"today",label:"Today"},{value:"week",label:"This Week"},{value:"month",label:"This Month"},{value:"quarter",label:"This Quarter"},{value:"year",label:"This Year"},{value:"custom",label:"Custom Range"}],_=[{value:"sales",label:"Sales Report"},{value:"products",label:"Product Performance"},{value:"customers",label:"Customer Analysis"},{value:"geographic",label:"Geographic Report"}],C=[{value:"day",label:"Daily"},{value:"week",label:"Weekly"},{value:"month",label:"Monthly"},{value:"quarter",label:"Quarterly"}],R=[{value:"all",label:"All Orders"},{value:"completed",label:"Completed Only"},{value:"pending",label:"Pending Only"},{value:"cancelled",label:"Cancelled Only"}],n=d({totalRevenue:125430,revenueChange:12,totalOrders:342,ordersChange:8,avgOrderValue:366.75,avgOrderChange:4,conversionRate:3.2,conversionChange:.5}),O=d([{period:"Week 1",orders:85,revenue:31250,avgOrder:367.65,growth:12},{period:"Week 2",orders:92,revenue:33780,avgOrder:367.17,growth:8},{period:"Week 3",orders:78,revenue:28600,avgOrder:366.67,growth:-15},{period:"Week 4",orders:87,revenue:31800,avgOrder:365.52,growth:11}]),u=g=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(g),V=g=>g>=0?"text-green-600":"text-red-600",p=()=>{console.log("Updating report with filters:",o.value)},T=()=>{console.log("Generating new report...")},B=()=>{console.log("Exporting report...")},S=()=>{console.log("Scheduling report...")},z=()=>{console.log("Refreshing report data...")};return j(()=>{}),(g,t)=>(y(),D(l(P),{title:"Sales Reports",subtitle:"Analyze sales performance and trends",loading:b.value,error:k.value},{actions:s(()=>[e("div",W,[r(l(c),{variant:"outline",size:"sm",onClick:B},{default:s(()=>t[4]||(t[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})],-1),m(" Export Report ")])),_:1,__:[4]}),r(l(c),{variant:"outline",size:"sm",onClick:S},{default:s(()=>t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),m(" Schedule ")])),_:1,__:[5]}),r(l(c),{variant:"primary",size:"sm",onClick:T},{default:s(()=>t[6]||(t[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})],-1),m(" Generate Report ")])),_:1,__:[6]})])]),default:s(()=>[r(l(v),{class:"mb-8"},{default:s(()=>[e("div",F,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Report Filters",-1)),e("div",H,[e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),r(l(h),{modelValue:o.value.dateRange,"onUpdate:modelValue":t[0]||(t[0]=a=>o.value.dateRange=a),options:w,onChange:p},null,8,["modelValue"])]),e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Report Type",-1)),r(l(h),{modelValue:o.value.reportType,"onUpdate:modelValue":t[1]||(t[1]=a=>o.value.reportType=a),options:_,onChange:p},null,8,["modelValue"])]),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Group By",-1)),r(l(h),{modelValue:o.value.groupBy,"onUpdate:modelValue":t[2]||(t[2]=a=>o.value.groupBy=a),options:C,onChange:p},null,8,["modelValue"])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),r(l(h),{modelValue:o.value.status,"onUpdate:modelValue":t[3]||(t[3]=a=>o.value.status=a),options:R,onChange:p},null,8,["modelValue"])])])])]),_:1}),e("div",N,[r(l(x),{title:"Total Revenue",value:u(n.value.totalRevenue),change:n.value.revenueChange,icon:"revenue",color:"green"},null,8,["value","change"]),r(l(x),{title:"Total Orders",value:n.value.totalOrders,change:n.value.ordersChange,icon:"orders",color:"blue"},null,8,["value","change"]),r(l(x),{title:"Avg Order Value",value:u(n.value.avgOrderValue),change:n.value.avgOrderChange,icon:"analytics",color:"indigo"},null,8,["value","change"]),r(l(x),{title:"Conversion Rate",value:n.value.conversionRate+"%",change:n.value.conversionChange,icon:"analytics",color:"purple"},null,8,["value","change"])]),e("div",E,[r(l(v),null,{default:s(()=>t[12]||(t[12]=[e("div",{class:"p-6"},[e("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Sales Trend"),e("div",{class:"h-64 bg-gray-50 rounded-lg flex items-center justify-center"},[e("p",{class:"text-gray-500"},"Sales trend chart will be implemented here")])],-1)])),_:1,__:[12]}),r(l(v),null,{default:s(()=>t[13]||(t[13]=[e("div",{class:"p-6"},[e("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Top Products"),e("div",{class:"h-64 bg-gray-50 rounded-lg flex items-center justify-center"},[e("p",{class:"text-gray-500"},"Top products chart will be implemented here")])],-1)])),_:1,__:[13]})]),r(l(v),null,{default:s(()=>[e("div",$,[e("div",q,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900"},"Detailed Report",-1)),e("div",L,[r(l(c),{variant:"outline",size:"sm",onClick:z},{default:s(()=>t[14]||(t[14]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),m(" Refresh ")])),_:1,__:[14]})])]),e("div",Q,[e("table",I,[t[16]||(t[16]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Period "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Orders "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Revenue "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Avg Order "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Growth ")])],-1)),e("tbody",Y,[(y(!0),f(G,null,M(O.value,a=>(y(),f("tr",{key:a.period},[e("td",J,i(a.period),1),e("td",K,i(a.orders),1),e("td",X,i(u(a.revenue)),1),e("td",Z,i(u(a.avgOrder)),1),e("td",ee,[e("span",{class:U(V(a.growth))},i(a.growth>=0?"+":"")+i(a.growth)+"% ",3)])]))),128))])])])])]),_:1})]),_:1},8,["loading","error"]))}});export{se as default};
