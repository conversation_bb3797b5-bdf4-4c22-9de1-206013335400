import{h as le,r as g,i as A,L as oe,o as d,f as m,v as r,x as l,u as o,m as I,j as _,F as B,g as e,_ as v,H as c,d as y,t as n,k as U}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as ie,f as ne}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{A as re}from"./AdminDetailTemplate-67d04137.js";import{A as ue}from"./AdminModal-15ebbba8.js";import{A as M}from"./AdminBadge-74cb3994.js";import{u as de}from"./auctionTypes-eeea8f1c.js";import{u as ce}from"./items-578e557a.js";import{u as me}from"./useNotifications-98e2c61c.js";import"./axios-917b1704.js";const ve={class:"flex space-x-3"},pe={key:0,class:"space-y-6"},fe={class:"flex items-center justify-between"},ge={class:"flex space-x-3"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},xe={class:"mt-1 text-sm text-gray-900"},_e={class:"mt-1 text-sm text-gray-900"},be={key:0,class:"md:col-span-2"},he={class:"mt-1 text-sm text-gray-900 whitespace-pre-wrap"},ke={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},we={class:"text-center"},Ae={class:"text-2xl font-bold text-blue-600"},Ie={class:"text-center"},Ce={class:"text-2xl font-bold text-green-600"},Te={class:"text-center"},$e={class:"text-2xl font-bold text-purple-600"},je={class:"flex items-center justify-between mb-4"},De={class:"space-y-3"},Se=["onClick"],Ne={class:"flex items-center space-x-3"},Be=["src","alt"],Me={class:"text-sm font-medium text-gray-900"},Le={class:"text-xs text-gray-500"},ze={class:"text-xs text-gray-400"},Fe={class:"flex items-center space-x-2"},Ve={key:0,class:"mt-4 text-center"},Ee={class:"text-center py-8"},He={class:"mt-6"},Ue={class:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4"},Re=["onClick"],Oe=["src","alt"],Pe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},qe={class:"mt-1 text-sm text-gray-900"},Ge={class:"mt-1 text-sm text-gray-900"},Je={class:"text-lg font-medium"},Ke={class:"space-y-4"},Qe=["src","alt"],We={key:0,class:"flex justify-center space-x-4"},Xe={class:"flex items-center text-sm text-gray-500"},Ye={class:"flex justify-end space-x-3"},ct=le({__name:"AuctionTypeView",setup(Ze){const b=ie(),L=ne(),C=de(),T=ce(),{showNotification:w}=me(),$=g(!1),j=g(null),h=g([]),z=g(0),k=g(!1),D=g(!1),p=g(null),u=g(0),f=A(()=>L.params.id?parseInt(L.params.id):null),s=A(()=>C.currentAuctionType),R=A(()=>{var a;return[{label:"Dashboard",href:"/admin-spa"},{label:"Auction Types",href:"/admin-spa/auction-types/list"},{label:((a=s.value)==null?void 0:a.name)||"Auction Type Details"}]}),O=A(()=>k.value?h.value:h.value.slice(0,5)),P=async()=>{if(f.value){$.value=!0,j.value=null;try{await C.fetchAuctionType(f.value),await q()}catch{j.value="Failed to load auction type",w("Failed to load auction type","error")}finally{$.value=!1}}},q=async()=>{if(f.value)try{await T.fetchItems({auction_type_id:f.value.toString(),per_page:k.value?void 0:5}),h.value=T.itemsList,z.value=T.totalItems}catch(a){console.error("Failed to load associated items:",a)}},G=()=>{f.value&&b.push(`/admin-spa/auction-types/edit/${f.value}`)},J=async()=>{if(s.value){if((s.value.items_count||0)>0){w("Cannot delete auction type with associated items","error");return}if(confirm(`Are you sure you want to delete "${s.value.name}"?`))try{await C.deleteAuctionType(s.value.id),w("Auction type deleted successfully","success"),b.push("/admin-spa/auction-types/list")}catch{w("Failed to delete auction type","error")}}},K=a=>{b.push(`/admin-spa/items/view/${a.id}`)},Q=()=>{b.push(`/admin-spa/items/list?auction_type_id=${f.value}`)},W=()=>{b.push(`/admin-spa/items/create?auction_type_id=${f.value}`)},X=(a,t)=>{p.value=a,u.value=t,D.value=!0},F=()=>{D.value=!1,p.value=null,u.value=0},Y=()=>{var a;u.value>0&&(u.value--,p.value=(a=s.value)==null?void 0:a.media[u.value])},Z=()=>{var a;(a=s.value)!=null&&a.media&&u.value<s.value.media.length-1&&(u.value++,p.value=s.value.media[u.value])},ee=()=>{var a,t;if((a=p.value)!=null&&a.url){const x=document.createElement("a");x.href=p.value.url,x.download=`${((t=s.value)==null?void 0:t.name)||"image"}-${u.value+1}`,x.click()}},te=a=>{switch(a){case"live":return"success";case"online":return"primary";case"cash":return"warning";default:return"secondary"}},S=a=>{if(!a)return"Unknown";switch(a){case"live":return"Live Auction";case"online":return"Online Auction";case"cash":return"Cash Sale";default:return a}},se=a=>a?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a):"$0.00",ae=a=>a?new Date(a).toLocaleDateString():"N/A",V=a=>a?new Date(a).toLocaleString():"N/A";return oe(()=>{P()}),(a,t)=>{var x,E,H;return d(),m(B,null,[r(o(re),{title:((x=s.value)==null?void 0:x.name)||"Auction Type Details",subtitle:`${S((E=s.value)==null?void 0:E.type)} - ${((H=s.value)==null?void 0:H.is_active)!==!1?"Active":"Inactive"}`,loading:$.value,error:j.value,breadcrumbs:R.value},{actions:l(()=>[e("div",ve,[r(o(v),{variant:"outline",onClick:G,disabled:!s.value},{default:l(()=>t[1]||(t[1]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),c(" Edit Auction Type ")])),_:1,__:[1]},8,["disabled"]),r(o(v),{variant:"outline",onClick:J,disabled:!s.value||(s.value.items_count||0)>0,class:"text-red-600 hover:text-red-700"},{default:l(()=>t[2]||(t[2]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),c(" Delete Auction Type ")])),_:1,__:[2]},8,["disabled"])])]),default:l(()=>[s.value?(d(),m("div",pe,[r(o(y),{class:"p-6"},{default:l(()=>[e("div",fe,[t[3]||(t[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"Status & Type"),e("p",{class:"text-sm text-gray-500 mt-1"},"Current auction type configuration")],-1)),e("div",ge,[r(o(M),{variant:te(s.value.type),size:"lg"},{default:l(()=>[c(n(S(s.value.type)),1)]),_:1},8,["variant"]),r(o(M),{variant:s.value.is_active!==!1?"success":"secondary",size:"lg"},{default:l(()=>[c(n(s.value.is_active!==!1?"Active":"Inactive"),1)]),_:1},8,["variant"])])])]),_:1}),r(o(y),{class:"p-6"},{default:l(()=>[t[7]||(t[7]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),e("div",ye,[e("div",null,[t[4]||(t[4]=e("label",{class:"block text-sm font-medium text-gray-700"},"Name",-1)),e("p",xe,n(s.value.name),1)]),e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-700"},"Type",-1)),e("p",_e,n(S(s.value.type)),1)]),s.value.description?(d(),m("div",be,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-700"},"Description",-1)),e("p",he,n(s.value.description),1)])):_("",!0)])]),_:1,__:[7]}),r(o(y),{class:"p-6"},{default:l(()=>[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Statistics",-1)),e("div",ke,[e("div",we,[e("div",Ae,n(s.value.items_count||0),1),t[8]||(t[8]=e("div",{class:"text-sm text-gray-500"},"Associated Items",-1))]),e("div",Ie,[e("div",Ce,n(s.value.auctions_count||0),1),t[9]||(t[9]=e("div",{class:"text-sm text-gray-500"},"Total Auctions",-1))]),e("div",Te,[e("div",$e,n(ae(s.value.created_at)),1),t[10]||(t[10]=e("div",{class:"text-sm text-gray-500"},"Created Date",-1))])])]),_:1,__:[11]}),h.value.length>0?(d(),I(o(y),{key:0,class:"p-6"},{default:l(()=>[e("div",je,[t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900"},"Associated Items",-1)),r(o(v),{variant:"outline",size:"sm",onClick:Q},{default:l(()=>t[12]||(t[12]=[c(" View All Items ")])),_:1,__:[12]})]),e("div",De,[(d(!0),m(B,null,U(O.value,i=>(d(),m("div",{key:i.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg border hover:bg-gray-100 transition-colors cursor-pointer",onClick:N=>K(i)},[e("div",Ne,[e("img",{src:i.image||"/img/product.jpeg",alt:i.name,class:"w-12 h-12 rounded-lg object-cover"},null,8,Be),e("div",null,[e("p",Me,n(i.name),1),e("p",Le,"Ref: "+n(i.reference_number||"N/A"),1),e("p",ze,"Target: "+n(se(i.target_amount)),1)])]),e("div",Fe,[r(o(M),{variant:i.closed_by?"success":"warning",size:"sm"},{default:l(()=>[c(n(i.closed_by?"Sold":"Available"),1)]),_:2},1032,["variant"]),t[14]||(t[14]=e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])],8,Se))),128))]),h.value.length>5?(d(),m("div",Ve,[r(o(v),{variant:"outline",size:"sm",onClick:t[0]||(t[0]=i=>k.value=!k.value)},{default:l(()=>[c(n(k.value?"Show Less":`Show All ${z.value} Items`),1)]),_:1})])):_("",!0)]),_:1})):(d(),I(o(y),{key:1,class:"p-6"},{default:l(()=>[e("div",Ee,[t[16]||(t[16]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})],-1)),t[17]||(t[17]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No associated items",-1)),t[18]||(t[18]=e("p",{class:"mt-1 text-sm text-gray-500"},"This auction type doesn't have any items yet.",-1)),e("div",He,[r(o(v),{variant:"outline",onClick:W},{default:l(()=>t[15]||(t[15]=[c(" Add First Item ")])),_:1,__:[15]})])])]),_:1})),s.value.media&&s.value.media.length>0?(d(),I(o(y),{key:2,class:"p-6"},{default:l(()=>[t[20]||(t[20]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Images",-1)),e("div",Ue,[(d(!0),m(B,null,U(s.value.media,(i,N)=>(d(),m("div",{key:i.id,class:"relative group cursor-pointer",onClick:et=>X(i,N)},[e("img",{src:i.url,alt:`Image ${N+1}`,class:"w-full h-24 object-cover rounded-lg border hover:opacity-75 transition-opacity"},null,8,Oe),t[19]||(t[19]=e("div",{class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1))],8,Re))),128))])]),_:1,__:[20]})):_("",!0),r(o(y),{class:"p-6"},{default:l(()=>[t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Timestamps",-1)),e("div",Pe,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700"},"Created",-1)),e("p",qe,n(V(s.value.created_at)),1)]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700"},"Last Updated",-1)),e("p",Ge,n(V(s.value.updated_at)),1)])])]),_:1,__:[23]})])):_("",!0)]),_:1},8,["title","subtitle","loading","error","breadcrumbs"]),D.value&&p.value?(d(),I(o(ue),{key:0,onClose:F,size:"lg"},{header:l(()=>{var i;return[e("h3",Je,n((i=s.value)==null?void 0:i.name)+" - Image "+n(u.value+1),1)]}),footer:l(()=>[e("div",Ye,[r(o(v),{variant:"outline",onClick:F},{default:l(()=>t[26]||(t[26]=[c("Close")])),_:1,__:[26]}),r(o(v),{variant:"primary",onClick:ee},{default:l(()=>t[27]||(t[27]=[c("Download")])),_:1,__:[27]})])]),default:l(()=>{var i;return[e("div",Ke,[e("img",{src:p.value.url,alt:`Image ${u.value+1}`,class:"w-full max-h-96 object-contain rounded-lg"},null,8,Qe),(i=s.value)!=null&&i.media&&s.value.media.length>1?(d(),m("div",We,[r(o(v),{variant:"outline",size:"sm",onClick:Y,disabled:u.value===0},{default:l(()=>t[24]||(t[24]=[c(" Previous ")])),_:1,__:[24]},8,["disabled"]),e("span",Xe,n(u.value+1)+" of "+n(s.value.media.length),1),r(o(v),{variant:"outline",size:"sm",onClick:Z,disabled:u.value===s.value.media.length-1},{default:l(()=>t[25]||(t[25]=[c(" Next ")])),_:1,__:[25]},8,["disabled"])])):_("",!0)])]}),_:1})):_("",!0)],64)}}});export{ct as default};
