import{h as I,r as i,i as L,L as P,o as C,m as b,x as o,u as l,g as r,v as n,H as u,_ as m,b as E,j as U}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as J,h as g}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{_ as F}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";const H={class:"flex space-x-3"},W={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},q={class:"flex space-x-2"},Z=I({__name:"CustomersList",setup(Q){const c=J(),f=i(!1),k=i(null),p=i(1),w=i(8),_=i(156),s=i({status:"",segment:"",search:"",dateRange:""}),D=[{value:"",label:"All Statuses"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"},{value:"suspended",label:"Suspended"}],x=[{value:"",label:"All Segments"},{value:"vip",label:"VIP"},{value:"regular",label:"Regular"},{value:"new",label:"New Customer"},{value:"returning",label:"Returning"}],V=[{value:"",label:"All Time"},{value:"today",label:"Today"},{value:"week",label:"This Week"},{value:"month",label:"This Month"},{value:"quarter",label:"This Quarter"}],S=[{key:"name",label:"Name",sortable:!0},{key:"email",label:"Email",sortable:!0},{key:"phone",label:"Phone",sortable:!1},{key:"totalOrders",label:"Orders",sortable:!0},{key:"totalSpent",label:"Total Spent",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"registeredDate",label:"Registered",sortable:!0},{key:"actions",label:"Actions",sortable:!1}],y=i([{id:1,name:"John Doe",email:"<EMAIL>",phone:"+****************",totalOrders:12,totalSpent:2499.99,status:"active",segment:"vip",registeredDate:new Date("2023-06-15"),lastOrderDate:new Date("2024-01-10")},{id:2,name:"Jane Smith",email:"<EMAIL>",phone:"+****************",totalOrders:5,totalSpent:899.5,status:"active",segment:"regular",registeredDate:new Date("2023-09-22"),lastOrderDate:new Date("2024-01-08")},{id:3,name:"Bob Johnson",email:"<EMAIL>",phone:"+****************",totalOrders:1,totalSpent:399.99,status:"active",segment:"new",registeredDate:new Date("2024-01-05"),lastOrderDate:new Date("2024-01-05")},{id:4,name:"Alice Brown",email:"<EMAIL>",phone:"+****************",totalOrders:8,totalSpent:1599.99,status:"inactive",segment:"returning",registeredDate:new Date("2023-03-10"),lastOrderDate:new Date("2023-11-20")},{id:5,name:"Charlie Wilson",email:"<EMAIL>",phone:"+****************",totalOrders:0,totalSpent:0,status:"suspended",segment:"new",registeredDate:new Date("2024-01-01"),lastOrderDate:null}]),O=L(()=>{let t=y.value;if(s.value.status&&(t=t.filter(e=>e.status===s.value.status)),s.value.segment&&(t=t.filter(e=>e.segment===s.value.segment)),s.value.search){const e=s.value.search.toLowerCase();t=t.filter(a=>a.name.toLowerCase().includes(e)||a.email.toLowerCase().includes(e)||a.phone.includes(e))}return t}),h=()=>{c.push("/admin-spa/sales/customers/create")},$=t=>{s.value.search=t,d()},A=(t,e)=>{console.log("Sorting by:",t,e)},R=t=>{p.value=t},d=()=>{p.value=1},B=()=>{console.log("Exporting customers...")},z=()=>{console.log("Importing customers...")},M=t=>{c.push(`/admin-spa/sales/customers/${t.id}`)},N=t=>{c.push(`/admin-spa/sales/customers/${t.id}/edit`)},T=t=>{c.push(`/admin-spa/sales/orders?customer=${t.id}`)},j=t=>{console.log("Deactivating customer:",t.name)};return P(()=>{}),(t,e)=>(C(),b(l(F),{title:"Customers",subtitle:"Manage customer information and relationships",loading:f.value,error:k.value,items:O.value,columns:S,"show-create-button":!0,"create-button-text":"Add Customer","create-route":"/admin-spa/sales/customers/create","empty-state-title":"No customers found","empty-state-message":"Customer profiles will appear here when users register or make purchases.","show-pagination":!0,"current-page":p.value,"total-pages":w.value,"total-items":_.value,onCreate:h,onSearch:$,onSort:A,onPageChange:R},{actions:o(()=>[r("div",H,[n(l(m),{variant:"outline",size:"sm",onClick:B},{default:o(()=>e[4]||(e[4]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})],-1),u(" Export ")])),_:1,__:[4]}),n(l(m),{variant:"outline",size:"sm",onClick:z},{default:o(()=>e[5]||(e[5]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})],-1),u(" Import ")])),_:1,__:[5]}),n(l(m),{variant:"primary",size:"sm",onClick:h},{default:o(()=>e[6]||(e[6]=[r("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),u(" Add Customer ")])),_:1,__:[6]})])]),filters:o(()=>[r("div",W,[n(l(g),{modelValue:s.value.status,"onUpdate:modelValue":e[0]||(e[0]=a=>s.value.status=a),placeholder:"Filter by status",options:D,onChange:d},null,8,["modelValue"]),n(l(g),{modelValue:s.value.segment,"onUpdate:modelValue":e[1]||(e[1]=a=>s.value.segment=a),placeholder:"Customer segment",options:x,onChange:d},null,8,["modelValue"]),n(l(E),{modelValue:s.value.search,"onUpdate:modelValue":e[2]||(e[2]=a=>s.value.search=a),placeholder:"Search customers...",onInput:d},null,8,["modelValue"]),n(l(g),{modelValue:s.value.dateRange,"onUpdate:modelValue":e[3]||(e[3]=a=>s.value.dateRange=a),placeholder:"Registration date",options:V,onChange:d},null,8,["modelValue"])])]),"item-actions":o(({item:a})=>[r("div",q,[n(l(m),{variant:"ghost",size:"sm",onClick:v=>M(a)},{default:o(()=>e[7]||(e[7]=[u(" View ")])),_:2,__:[7]},1032,["onClick"]),n(l(m),{variant:"ghost",size:"sm",onClick:v=>N(a)},{default:o(()=>e[8]||(e[8]=[u(" Edit ")])),_:2,__:[8]},1032,["onClick"]),n(l(m),{variant:"ghost",size:"sm",onClick:v=>T(a)},{default:o(()=>e[9]||(e[9]=[u(" Orders ")])),_:2,__:[9]},1032,["onClick"]),a.status==="active"?(C(),b(l(m),{key:0,variant:"ghost",size:"sm",onClick:v=>j(a)},{default:o(()=>e[10]||(e[10]=[u(" Deactivate ")])),_:2,__:[10]},1032,["onClick"])):U("",!0)])]),_:1},8,["loading","error","items","current-page","total-pages","total-items"]))}});export{Z as default};
