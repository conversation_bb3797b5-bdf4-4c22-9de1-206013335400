import{h as S,r as d,L as D,o as p,m as A,x as n,u as s,g as e,v as a,H as c,_ as g,f as x,k as B,t as i,F as T,d as h}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as V,e as N}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{A as u,a as j}from"./app-admin-1baa1658.js";const F={class:"flex space-x-3"},M={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},z={class:"p-6"},E={class:"flex items-center justify-between mb-6"},I={class:"overflow-x-auto"},J={class:"min-w-full divide-y divide-gray-200"},R={class:"bg-white divide-y divide-gray-200"},U={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},$={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},H={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},L={class:"px-6 py-4 whitespace-nowrap"},P={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Q=S({__name:"SalesOverview",setup(W){const v=V(),f=d(!1),y=d(null),r=d({totalSales:125430,salesChange:12,ordersToday:24,ordersChange:8,activeCustomers:156,customersChange:5,avgOrderValue:245.5,avgOrderChange:-2}),w=d([{id:1001,customer:"John Doe",amount:299.99,status:"completed",date:new Date},{id:1002,customer:"Jane Smith",amount:149.5,status:"pending",date:new Date},{id:1003,customer:"Bob Johnson",amount:399.99,status:"processing",date:new Date},{id:1004,customer:"Alice Brown",amount:199.99,status:"completed",date:new Date},{id:1005,customer:"Charlie Wilson",amount:89.99,status:"cancelled",date:new Date}]),m=l=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(l),_=l=>new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(l),k=l=>{switch(l){case"completed":return"success";case"processing":return"info";case"pending":return"warning";case"cancelled":return"error";default:return"default"}},C=()=>{console.log("Exporting sales data...")},b=()=>{v.push("/admin-spa/sales/orders")},O=()=>{v.push("/admin-spa/sales/orders")};return D(()=>{}),(l,t)=>(p(),A(s(j),{title:"Sales Overview",subtitle:"Monitor sales performance and key metrics",loading:f.value,error:y.value},{actions:n(()=>[e("div",F,[a(s(g),{variant:"outline",size:"sm",onClick:C},{default:n(()=>t[0]||(t[0]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})],-1),c(" Export Report ")])),_:1,__:[0]}),a(s(g),{variant:"primary",size:"sm",onClick:b},{default:n(()=>t[1]||(t[1]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),c(" New Order ")])),_:1,__:[1]})])]),default:n(()=>[e("div",M,[a(s(u),{title:"Total Sales",value:m(r.value.totalSales),change:r.value.salesChange,icon:"revenue",color:"green"},null,8,["value","change"]),a(s(u),{title:"Orders Today",value:r.value.ordersToday,change:r.value.ordersChange,icon:"orders",color:"blue"},null,8,["value","change"]),a(s(u),{title:"Active Customers",value:r.value.activeCustomers,change:r.value.customersChange,icon:"users",color:"purple"},null,8,["value","change"]),a(s(u),{title:"Avg Order Value",value:m(r.value.avgOrderValue),change:r.value.avgOrderChange,icon:"analytics",color:"indigo"},null,8,["value","change"])]),a(s(h),{class:"mb-8"},{default:n(()=>[e("div",z,[e("div",E,[t[3]||(t[3]=e("h3",{class:"text-lg font-medium text-gray-900"},"Recent Orders",-1)),a(s(g),{variant:"outline",size:"sm",onClick:O},{default:n(()=>t[2]||(t[2]=[c(" View All Orders ")])),_:1,__:[2]})]),e("div",I,[e("table",J,[t[4]||(t[4]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Order ID "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Customer "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Amount "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Date ")])],-1)),e("tbody",R,[(p(!0),x(T,null,B(w.value,o=>(p(),x("tr",{key:o.id},[e("td",U," #"+i(o.id),1),e("td",$,i(o.customer),1),e("td",H,i(m(o.amount)),1),e("td",L,[a(s(N),{variant:k(o.status)},{default:n(()=>[c(i(o.status),1)]),_:2},1032,["variant"])]),e("td",P,i(_(o.date)),1)]))),128))])])])])]),_:1}),a(s(h),null,{default:n(()=>t[5]||(t[5]=[e("div",{class:"p-6"},[e("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Sales Trend"),e("div",{class:"h-64 bg-gray-50 rounded-lg flex items-center justify-center"},[e("p",{class:"text-gray-500"},"Sales chart will be implemented here")])],-1)])),_:1,__:[5]})]),_:1},8,["loading","error"]))}});export{Q as default};
