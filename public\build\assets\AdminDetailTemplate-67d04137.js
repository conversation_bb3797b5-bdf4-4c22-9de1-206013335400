import{h as $,o as t,m as u,x as l,K as n,g as s,u as d,_ as y,H as m,j as o,f as r,v as c,t as p,k as C,n as A,d as f,F as D}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as E,A as R,_ as T}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{a as V}from"./app-admin-1baa1658.js";const j={class:"flex space-x-3"},z={key:0,class:"status-banner"},H={class:"space-y-6"},L={key:0,class:"section-header mb-6"},N={class:"flex items-center justify-between"},M={class:"text-lg font-medium text-gray-900"},S={key:0,class:"text-sm text-gray-600 mt-1"},F={key:0,class:"flex space-x-2"},I={key:1,class:"related-items"},K={key:2,class:"activity-timeline"},P=$({__name:"AdminDetailTemplate",props:{title:{},subtitle:{},loading:{type:Boolean},error:{},breadcrumbs:{},sections:{default:()=>[]},status:{default:null},showEditButton:{type:Boolean,default:!0},showDeleteButton:{type:Boolean,default:!1},editRoute:{},deleting:{type:Boolean,default:!1}},emits:["edit","delete"],setup(h,{emit:k}){const g=h,v=k,_=E(),b=()=>{g.editRoute?_.push(g.editRoute):v("edit")},B=()=>{v("delete")},w=e=>{switch(e.layout){case"grid":return"grid grid-cols-1 md:grid-cols-2 gap-6";case"columns":return"grid grid-cols-1 lg:grid-cols-3 gap-6";default:return"space-y-4"}};return(e,i)=>(t(),u(V,{title:e.title,subtitle:e.subtitle,loading:e.loading,error:e.error,breadcrumbs:e.breadcrumbs},{actions:l(()=>[n(e.$slots,"actions",{},()=>[s("div",j,[e.showEditButton?(t(),u(d(y),{key:0,variant:"outline",size:"sm",onClick:b},{default:l(()=>i[0]||(i[0]=[s("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),m(" Edit ")])),_:1,__:[0]})):o("",!0),e.showDeleteButton?(t(),u(d(y),{key:1,variant:"danger",size:"sm",onClick:B,loading:e.deleting},{default:l(()=>i[1]||(i[1]=[s("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),m(" Delete ")])),_:1,__:[1]},8,["loading"])):o("",!0)])],!0)]),default:l(()=>[e.status?(t(),r("div",z,[c(d(R),{variant:e.status.variant,class:"mb-6"},{title:l(()=>[m(p(e.status.title),1)]),default:l(()=>[m(" "+p(e.status.message),1)]),_:1},8,["variant"])])):o("",!0),s("div",H,[(t(!0),r(D,null,C(e.sections,a=>(t(),r("div",{key:a.key,class:"detail-section"},[c(d(f),{class:"p-6"},{default:l(()=>[a.title?(t(),r("div",L,[s("div",N,[s("div",null,[s("h3",M,p(a.title),1),a.description?(t(),r("p",S,p(a.description),1)):o("",!0)]),a.actions?(t(),r("div",F,[n(e.$slots,`section-${a.key}-actions`,{section:a},void 0,!0)])):o("",!0)])])):o("",!0),s("div",{class:A(w(a))},[n(e.$slots,`section-${a.key}`,{section:a},void 0,!0)],2)]),_:2},1024)]))),128)),e.sections.length===0?(t(),u(d(f),{key:0,class:"p-6"},{default:l(()=>[n(e.$slots,"default",{},void 0,!0)]),_:3})):o("",!0)]),e.$slots.related?(t(),r("div",I,[c(d(f),{class:"p-6"},{default:l(()=>[i[2]||(i[2]=s("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Related Items",-1)),n(e.$slots,"related",{},void 0,!0)]),_:3,__:[2]})])):o("",!0),e.$slots.timeline?(t(),r("div",K,[c(d(f),{class:"p-6"},{default:l(()=>[i[3]||(i[3]=s("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Activity Timeline",-1)),n(e.$slots,"timeline",{},void 0,!0)]),_:3,__:[3]})])):o("",!0)]),_:3},8,["title","subtitle","loading","error","breadcrumbs"]))}}),O=T(P,[["__scopeId","data-v-7756cb23"]]);export{O as A};
