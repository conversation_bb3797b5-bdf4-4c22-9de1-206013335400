import{h as L,r as i,i as E,L as M,o,f as d,g as t,t as a,v as u,x as m,u as c,j as N,H as x,_ as b}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as $,f as j,e as U}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{u as V}from"./useNotifications-98e2c61c.js";import{b as D}from"./app-admin-1baa1658.js";const z={class:"max-w-4xl mx-auto p-6"},A={key:0,class:"flex justify-center items-center h-64"},H={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},I={class:"flex"},R={class:"ml-3"},S={class:"mt-2 text-sm text-red-700"},F={key:2,class:"bg-white shadow rounded-lg"},P={class:"px-6 py-4 border-b border-gray-200"},T={class:"flex items-center justify-between"},q={class:"text-xl font-semibold text-gray-900"},G={class:"flex space-x-3"},J={class:"p-6"},K={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},O={class:"space-y-4"},Q={class:"mt-1 text-sm text-gray-900"},W={class:"mt-1"},X={class:"mt-1 text-sm text-gray-900"},Y={key:0,class:"whitespace-pre-line"},Z={key:1,class:"text-gray-400"},tt={class:"space-y-4"},st={class:"mt-1 text-sm text-gray-900"},et=["href"],at={key:1,class:"text-gray-400"},ot={class:"mt-1 text-sm text-gray-900"},dt=["href"],lt={key:1,class:"text-gray-400"},nt={class:"mt-8 pt-6 border-t border-gray-200"},rt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},it={class:"mt-1 text-sm text-gray-900"},ut={class:"mt-1 text-sm text-gray-900"},mt={class:"mt-1 text-sm text-gray-900"},ct={class:"mt-1 text-sm text-gray-900"},yt=L({__name:"BranchView",setup(xt){const v=$(),k=j();V();const r=i(!1),n=i(null),e=i(null),f=E(()=>k.params.id),w=async()=>{r.value=!0,n.value=null;try{const l=await D.get(`/admin/branches/${f.value}`);e.value=l}catch(l){n.value="Failed to fetch branch details",console.error("Error fetching branch:",l)}finally{r.value=!1}},B=()=>{v.push(`/admin-spa/branches/edit/${f.value}`)},C=()=>{v.push("/admin-spa/branches/list")},_=l=>l?new Date(l).toLocaleString():"-";return M(()=>{w()}),(l,s)=>{var p,y,g;return o(),d("div",z,[r.value?(o(),d("div",A,s[0]||(s[0]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):n.value?(o(),d("div",H,[t("div",I,[s[2]||(s[2]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),t("div",R,[s[1]||(s[1]=t("h3",{class:"text-sm font-medium text-red-800"},"Error",-1)),t("div",S,[t("p",null,a(n.value),1)])])])])):e.value?(o(),d("div",F,[t("div",P,[t("div",T,[t("div",null,[t("h1",q,a(e.value.name),1),s[3]||(s[3]=t("p",{class:"mt-1 text-sm text-gray-600"}," Branch Details ",-1))]),t("div",G,[u(c(b),{variant:"outline",onClick:B},{default:m(()=>s[4]||(s[4]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),x(" Edit ")])),_:1,__:[4]}),u(c(b),{variant:"outline",onClick:C},{default:m(()=>s[5]||(s[5]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1),x(" Back to List ")])),_:1,__:[5]})])])]),t("div",J,[t("div",K,[t("div",O,[s[9]||(s[9]=t("h3",{class:"text-lg font-medium text-gray-900"},"Basic Information",-1)),t("div",null,[s[6]||(s[6]=t("dt",{class:"text-sm font-medium text-gray-500"},"Name",-1)),t("dd",Q,a(e.value.name||"-"),1)]),t("div",null,[s[7]||(s[7]=t("dt",{class:"text-sm font-medium text-gray-500"},"Status",-1)),t("dd",W,[u(c(U),{variant:((p=e.value.status)==null?void 0:p.id)===1?"success":"secondary"},{default:m(()=>{var h;return[x(a(((h=e.value.status)==null?void 0:h.name)||"Unknown"),1)]}),_:1},8,["variant"])])]),t("div",null,[s[8]||(s[8]=t("dt",{class:"text-sm font-medium text-gray-500"},"Address",-1)),t("dd",X,[e.value.address?(o(),d("div",Y,a(e.value.address),1)):(o(),d("span",Z,"-"))])])]),t("div",tt,[s[12]||(s[12]=t("h3",{class:"text-lg font-medium text-gray-900"},"Contact Information",-1)),t("div",null,[s[10]||(s[10]=t("dt",{class:"text-sm font-medium text-gray-500"},"Phone",-1)),t("dd",st,[e.value.phone?(o(),d("a",{key:0,href:`tel:${e.value.phone}`,class:"text-blue-600 hover:text-blue-800"},a(e.value.phone),9,et)):(o(),d("span",at,"-"))])]),t("div",null,[s[11]||(s[11]=t("dt",{class:"text-sm font-medium text-gray-500"},"Email",-1)),t("dd",ot,[e.value.email?(o(),d("a",{key:0,href:`mailto:${e.value.email}`,class:"text-blue-600 hover:text-blue-800"},a(e.value.email),9,dt)):(o(),d("span",lt,"-"))])])])]),t("div",nt,[s[17]||(s[17]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Metadata",-1)),t("div",rt,[t("div",null,[s[13]||(s[13]=t("dt",{class:"text-sm font-medium text-gray-500"},"Created By",-1)),t("dd",it,a(((y=e.value.created_by_user)==null?void 0:y.name)||"Unknown"),1)]),t("div",null,[s[14]||(s[14]=t("dt",{class:"text-sm font-medium text-gray-500"},"Updated By",-1)),t("dd",ut,a(((g=e.value.updated_by_user)==null?void 0:g.name)||"Unknown"),1)]),t("div",null,[s[15]||(s[15]=t("dt",{class:"text-sm font-medium text-gray-500"},"Created At",-1)),t("dd",mt,a(_(e.value.created_at)),1)]),t("div",null,[s[16]||(s[16]=t("dt",{class:"text-sm font-medium text-gray-500"},"Updated At",-1)),t("dd",ct,a(_(e.value.updated_at)),1)])])])])])):N("",!0)])}}});export{yt as default};
