import{h as z,r as y,R as N,i as m,L as H,o as V,m as J,x as c,u as r,g as s,v as l,d as w,f as U,F as K,k as Q,_ as A,j as W,H as $,t as X,B as Y}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Z,f as ee}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{_ as n}from"./FormField.vue_vue_type_script_setup_true_lang-529d40d6.js";import{u as te}from"./app-admin-1baa1658.js";import{A as ae}from"./AdminFormTemplate-3cd16ecb.js";import{A as oe}from"./AdminFileUpload-1a895ff4.js";import{u as re}from"./items-578e557a.js";import{u as le}from"./auctionTypes-eeea8f1c.js";import{u as se}from"./branches-1476f76c.js";import{u as ie}from"./useNotifications-98e2c61c.js";import"./axios-917b1704.js";const ne={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},de={class:"md:col-span-2"},ue={class:"md:col-span-2"},me={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ce={key:0,class:"mt-6"},pe={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},_e=["src","alt"],fe={class:"flex justify-end space-x-3 pt-6 border-t"},ke=z({__name:"ItemForm",setup(ve){const C=Z(),h=ee(),I=re(),k=le(),S=se(),F=te(),{showNotification:p}=ie(),x=y(!1),v=y(!1),b=y(null),_=y([]),e=N({name:"",description:"",reference_number:"",code:"",target_amount:null,auction_type_id:null,user_id:null,branch_id:null,date_from:"",date_to:"",media:[]}),i=N({name:"",description:"",reference_number:"",code:"",target_amount:"",auction_type_id:"",user_id:"",branch_id:"",date_from:"",date_to:"",media:""}),d=m(()=>!!h.params.id),f=m(()=>h.params.id?parseInt(h.params.id):null),j=m(()=>[{label:"Dashboard",href:"/admin-spa"},{label:"Items",href:"/admin-spa/items/list"},{label:d.value?"Edit Item":"Create Item"}]),B=m(()=>e.name.trim()!==""&&e.auction_type_id!==null),O=m(()=>[{label:"Select auction type",value:null},...k.auctionTypes.map(a=>({label:a.name,value:a.id}))]),R=m(()=>[{label:"Select branch",value:null},...S.activeBranches.map(a=>({label:a.name,value:a.id}))]),D=m(()=>[{label:"Select owner",value:null},...F.users.map(a=>({label:a.name,value:a.id}))]),M=async()=>{if(f.value){x.value=!0;try{const a=await I.fetchItem(f.value);e.name=a.name||"",e.description=a.description||"",e.reference_number=a.reference_number||"",e.code=a.code||"",e.target_amount=a.target_amount||null,e.auction_type_id=a.auction_type_id||null,e.user_id=a.user_id||null,e.branch_id=a.branch_id||null,e.date_from=a.date_from?a.date_from.split("T")[0]:"",e.date_to=a.date_to?a.date_to.split("T")[0]:"",a.media&&(_.value=a.media)}catch{b.value="Failed to load item",p("Failed to load item","error")}finally{x.value=!1}}},E=async()=>{var a,t,o,g;if(B.value){v.value=!0,q();try{const u={name:e.name,description:e.description||void 0,reference_number:e.reference_number||void 0,code:e.code||void 0,target_amount:e.target_amount||void 0,auction_type_id:e.auction_type_id||void 0,user_id:e.user_id||void 0,branch_id:e.branch_id||void 0,date_from:e.date_from||void 0,date_to:e.date_to||void 0,media:e.media.length>0?e.media:void 0};d.value&&f.value?(await I.updateItem({id:f.value,...u}),p("Item updated successfully","success")):(await I.createItem(u),p("Item created successfully","success")),C.push("/admin-spa/items/list")}catch(u){(t=(a=u.response)==null?void 0:a.data)!=null&&t.errors?Object.assign(i,u.response.data.errors):(b.value=((g=(o=u.response)==null?void 0:o.data)==null?void 0:g.message)||"Failed to save item",p("Failed to save item","error"))}finally{v.value=!1}}},T=()=>{C.push("/admin-spa/items/list")},G=a=>{e.media=[...e.media,...a]},L=a=>{e.media.splice(a,1)},P=async a=>{if(confirm("Are you sure you want to remove this image?"))try{await axios.delete(`/api/items/${f.value}/media/${a}`),_.value=_.value.filter(t=>t.id!==a),p("Image removed successfully","success")}catch{p("Failed to remove image","error")}},q=()=>{Object.keys(i).forEach(a=>{i[a]=""}),b.value=null};return H(async()=>{await Promise.all([k.fetchAuctionTypes(),S.fetchBranches(),F.fetchUsers()]),d.value&&await M()}),(a,t)=>(V(),J(r(ae),{title:d.value?"Edit Item":"Create Item",subtitle:d.value?"Update item details":"Add a new auction item",loading:x.value,error:b.value,breadcrumbs:j.value,onSubmit:E,onCancel:T},{default:c(()=>[s("form",{onSubmit:Y(E,["prevent"]),class:"space-y-6"},[l(r(w),{class:"p-6"},{default:c(()=>[t[11]||(t[11]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),s("div",ne,[s("div",de,[l(r(n),{modelValue:e.name,"onUpdate:modelValue":t[0]||(t[0]=o=>e.name=o),label:"Item Name",placeholder:"Enter item name",error:i.name,required:""},null,8,["modelValue","error"])]),l(r(n),{modelValue:e.reference_number,"onUpdate:modelValue":t[1]||(t[1]=o=>e.reference_number=o),label:"Reference Number",placeholder:"Enter reference number",error:i.reference_number},null,8,["modelValue","error"]),l(r(n),{modelValue:e.code,"onUpdate:modelValue":t[2]||(t[2]=o=>e.code=o),label:"Item Code",placeholder:"Enter item code",error:i.code},null,8,["modelValue","error"]),s("div",ue,[l(r(n),{modelValue:e.description,"onUpdate:modelValue":t[3]||(t[3]=o=>e.description=o),label:"Description",type:"textarea",placeholder:"Enter item description",error:i.description,rows:"4"},null,8,["modelValue","error"])])])]),_:1,__:[11]}),l(r(w),{class:"p-6"},{default:c(()=>[t[12]||(t[12]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Auction Details",-1)),s("div",me,[l(r(n),{modelValue:e.auction_type_id,"onUpdate:modelValue":t[4]||(t[4]=o=>e.auction_type_id=o),label:"Auction Type",type:"select",placeholder:"Select auction type",options:O.value,error:i.auction_type_id,required:""},null,8,["modelValue","options","error"]),l(r(n),{modelValue:e.branch_id,"onUpdate:modelValue":t[5]||(t[5]=o=>e.branch_id=o),label:"Branch",type:"select",placeholder:"Select branch",options:R.value,error:i.branch_id},null,8,["modelValue","options","error"]),l(r(n),{modelValue:e.target_amount,"onUpdate:modelValue":t[6]||(t[6]=o=>e.target_amount=o),label:"Target Amount",type:"number",placeholder:"0.00",error:i.target_amount,step:"0.01",min:"0"},null,8,["modelValue","error"]),l(r(n),{modelValue:e.user_id,"onUpdate:modelValue":t[7]||(t[7]=o=>e.user_id=o),label:"Owner",type:"select",placeholder:"Select owner",options:D.value,error:i.user_id},null,8,["modelValue","options","error"]),l(r(n),{modelValue:e.date_from,"onUpdate:modelValue":t[8]||(t[8]=o=>e.date_from=o),label:"Available From",type:"date",error:i.date_from},null,8,["modelValue","error"]),l(r(n),{modelValue:e.date_to,"onUpdate:modelValue":t[9]||(t[9]=o=>e.date_to=o),label:"Available Until",type:"date",error:i.date_to},null,8,["modelValue","error"])])]),_:1,__:[12]}),l(r(w),{class:"p-6"},{default:c(()=>[t[15]||(t[15]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Images",-1)),l(r(oe),{modelValue:e.media,"onUpdate:modelValue":t[10]||(t[10]=o=>e.media=o),accept:"image/*",multiple:!0,"max-size":10,"upload-text":"Click to upload images or drag and drop","support-text":"PNG, JPG, GIF up to 10MB each",onUpload:G,onRemove:L},null,8,["modelValue"]),d.value&&_.value.length>0?(V(),U("div",ce,[t[14]||(t[14]=s("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Current Images",-1)),s("div",pe,[(V(!0),U(K,null,Q(_.value,(o,g)=>(V(),U("div",{key:o.id,class:"relative group"},[s("img",{src:o.url,alt:`Image ${g+1}`,class:"w-full h-24 object-cover rounded-lg border"},null,8,_e),l(r(A),{variant:"ghost",size:"sm",onClick:u=>P(o.id),class:"absolute top-1 right-1 bg-red-500 text-white hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"},{default:c(()=>t[13]||(t[13]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:2,__:[13]},1032,["onClick"])]))),128))])])):W("",!0)]),_:1,__:[15]}),s("div",fe,[l(r(A),{type:"button",variant:"outline",onClick:T,disabled:v.value},{default:c(()=>t[16]||(t[16]=[$(" Cancel ")])),_:1,__:[16]},8,["disabled"]),l(r(A),{type:"submit",variant:"primary",loading:v.value,disabled:!B.value},{default:c(()=>[$(X(d.value?"Update Item":"Create Item"),1)]),_:1},8,["loading","disabled"])])],32)]),_:1},8,["title","subtitle","loading","error","breadcrumbs"]))}});export{ke as default};
