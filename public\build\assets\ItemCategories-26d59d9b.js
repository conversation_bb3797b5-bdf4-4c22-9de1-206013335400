import{h as M,r as t,R as $,L as T,o as R,f as U,v as y,x as w,u as h,F as G,g as H}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as L,h as O}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{_ as j}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{A as q}from"./AdminModal-15ebbba8.js";import{u as z}from"./useNotifications-98e2c61c.js";const ee=M({__name:"ItemCategories",setup(J){const i=L(),{showNotification:u}=z(),c=t(!1),d=t(null),n=t([]),m=t([]),s=t(!1),g=t(!1),v=t(null),b=t(1),_=t(1),f=t(0),C=t(25),l=$({search:"",status:"",sort_by:"name",sort_direction:"asc"}),k=[{key:"name",label:"Category Name",sortable:!0},{key:"description",label:"Description",sortable:!1},{key:"items_count",label:"Items",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"created_at",label:"Created",sortable:!0},{key:"actions",label:"Actions",sortable:!1}],D=[{label:"All Status",value:""},{label:"Active",value:"active"},{label:"Inactive",value:"inactive"}],o=async()=>{c.value=!0,d.value=null;try{await new Promise(e=>setTimeout(e,1e3)),n.value=[{id:1,name:"Electronics",description:"Electronic devices and gadgets",items_count:45,status:"active",created_at:"2024-01-15"},{id:2,name:"Furniture",description:"Home and office furniture",items_count:23,status:"active",created_at:"2024-01-10"}],f.value=n.value.length,_.value=Math.ceil(f.value/C.value)}catch(e){d.value="Failed to load categories",console.error("Error fetching categories:",e)}finally{c.value=!1}},p=()=>{o()},V=()=>{i.push("/admin-spa/items/categories/create")},A=e=>{i.push(`/admin-spa/items/categories/view/${e.id}`)},F=e=>{i.push(`/admin-spa/items/categories/edit/${e.id}`)},P=e=>{v.value=e,s.value=!0},S=async()=>{if(v.value){g.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),u({type:"success",title:"Category Deleted",message:"Category has been successfully deleted."}),await o()}catch{u({type:"error",title:"Delete Failed",message:"Failed to delete category. Please try again."})}finally{g.value=!1,s.value=!1,v.value=null}}},B=e=>{l.search=e,p()},x=(e,a)=>{l.sort_by=e,l.sort_direction=a,p()},E=e=>{b.value=e,o()},I=e=>{m.value=e?[...n.value]:[]},N=async()=>{m.value.length!==0&&u({type:"info",title:"Bulk Delete",message:"Bulk delete functionality will be implemented soon."})};return T(()=>{o()}),(e,a)=>(R(),U(G,null,[y(h(j),{title:"Item Categories",subtitle:"Manage item categories and classifications",loading:c.value,error:d.value,items:n.value,columns:k,"selected-items":m.value,"show-bulk-actions":!0,"current-page":b.value,"total-pages":_.value,"total-items":f.value,"per-page":C.value,"create-button-text":"Add Category","empty-state-title":"No categories found","empty-state-message":"Get started by adding your first item category.",onCreate:V,onSearch:B,onSort:x,onPageChange:E,onSelectAll:I,onBulkDelete:N,onView:A,onEdit:F,onDelete:P,onRefresh:o},{filters:w(()=>[y(h(O),{modelValue:l.status,"onUpdate:modelValue":a[0]||(a[0]=r=>l.status=r),options:D,placeholder:"Filter by status",class:"w-48",onChange:p},null,8,["modelValue"])]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]),y(h(q),{modelValue:s.value,"onUpdate:modelValue":a[1]||(a[1]=r=>s.value=r),title:"Delete Category",loading:g.value,onConfirm:S,onCancel:a[2]||(a[2]=r=>s.value=!1)},{default:w(()=>a[3]||(a[3]=[H("p",{class:"text-gray-600"}," Are you sure you want to delete this category? This action cannot be undone. ",-1)])),_:1,__:[3]},8,["modelValue","loading"])],64))}});export{ee as default};
