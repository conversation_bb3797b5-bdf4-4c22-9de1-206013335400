import{h as R,r as M,i as B,L as U,o as r,m as g,x as l,u as i,g as t,v as u,H as d,_,f as w,t as a,j as c,d as m}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as O,f as P}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{d as q}from"./app-admin-1baa1658.js";import{A as G}from"./AdminDetailTemplate-67d04137.js";import{A as $}from"./AdminBadge-74cb3994.js";import{u as J}from"./useNotifications-98e2c61c.js";const K={class:"flex space-x-3"},Q={key:0,class:"space-y-6"},W={class:"flex items-center justify-between"},X={class:"flex space-x-3"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Z={class:"mt-1 text-sm text-gray-900"},tt={class:"mt-1 text-sm text-gray-900"},et={class:"mt-1 text-sm text-gray-900"},st={class:"mt-1 text-sm text-gray-900"},at={key:0,class:"md:col-span-2"},ot={class:"mt-1 text-sm text-gray-900 whitespace-pre-wrap"},lt={class:"flex items-start space-x-4"},it=["src","alt"],nt={class:"flex-1"},rt={class:"text-xl font-medium text-gray-900"},ut={class:"text-sm text-gray-600 mt-1"},dt={class:"text-sm text-gray-600"},ct={key:0,class:"text-sm text-gray-500 mt-2"},mt={class:"flex items-center space-x-2 mt-3"},vt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},xt={class:"text-center"},gt={class:"text-2xl font-bold text-blue-600"},ft={class:"text-center"},pt={class:"text-2xl font-bold text-green-600"},yt={class:"text-center"},_t={class:"text-2xl font-bold text-purple-600"},bt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},kt={class:"mt-1 text-sm text-gray-900"},ht={class:"mt-1 text-sm text-gray-900"},wt={key:0,class:"mt-4 p-3 bg-blue-50 rounded-lg"},At={class:"text-sm text-blue-800"},Ct={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Dt={class:"mt-1 text-sm text-gray-900"},Tt={class:"mt-1 text-sm text-gray-900"},Nt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Bt={class:"mt-1 text-sm text-gray-900"},$t={class:"mt-1 text-sm text-gray-900"},It={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Vt={class:"mt-1 text-sm text-gray-900"},Mt={class:"mt-1 text-sm text-gray-900"},Rt=R({__name:"AuctionListingView",setup(St){const A=O(),I=P(),f=q(),{showNotification:v}=J(),C=M(!1),D=M(null),b=B(()=>I.params.id?parseInt(I.params.id):null),s=B(()=>f.currentAuction),S=B(()=>{var o;return[{label:"Dashboard",href:"/admin-spa"},{label:"Auction Listings",href:"/admin-spa/auction-listings/list"},{label:((o=s.value)==null?void 0:o.name)||"Auction Details"}]}),T=async()=>{if(b.value){C.value=!0,D.value=null;try{await f.fetchAuction(b.value)}catch{D.value="Failed to load auction",v("Failed to load auction","error")}finally{C.value=!1}}},j=()=>{b.value&&A.push(`/admin-spa/auction-listings/edit/${b.value}`)},z=async()=>{if(s.value&&confirm("Are you sure you want to close this auction?"))try{await f.closeAuction(s.value.id),v("Auction closed successfully","success"),await T()}catch{v("Failed to close auction","error")}},L=async()=>{if(s.value&&confirm("Are you sure you want to reopen this auction?"))try{await f.reopenAuction(s.value.id),v("Auction reopened successfully","success"),await T()}catch{v("Failed to reopen auction","error")}},H=async()=>{if(s.value&&confirm(`Are you sure you want to delete "${s.value.name||"this auction"}"?`))try{await f.deleteAuction(s.value.id),v("Auction deleted successfully","success"),A.push("/admin-spa/auction-listings/list")}catch{v("Failed to delete auction","error")}},V=()=>{var o,e;(e=(o=s.value)==null?void 0:o.item)!=null&&e.id&&A.push(`/admin-spa/items/view/${s.value.item.id}`)},F=o=>{switch(o){case"live":return"success";case"online":return"primary";case"cash":return"warning";default:return"secondary"}},p=o=>o?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(o):"$0.00",k=o=>o?new Date(o).toLocaleString():"Not set",E=(o,e)=>{const h=new Date(o),y=new Date(e).getTime()-h.getTime(),n=Math.floor(y/(1e3*60*60*24)),x=Math.floor(y%(1e3*60*60*24)/(1e3*60*60));return n>0?`${n} day${n>1?"s":""} ${x} hour${x>1?"s":""}`:`${x} hour${x>1?"s":""}`};return U(()=>{T()}),(o,e)=>{var h,N,y;return r(),g(i(G),{title:((h=s.value)==null?void 0:h.name)||"Auction Listing Details",subtitle:`${((N=s.value)==null?void 0:N.code)||"No code"} - ${(y=s.value)!=null&&y.closed_by?"Closed":"Active"}`,loading:C.value,error:D.value,breadcrumbs:S.value},{actions:l(()=>{var n;return[t("div",K,[u(i(_),{variant:"outline",onClick:j,disabled:!s.value},{default:l(()=>e[0]||(e[0]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),d(" Edit Auction ")])),_:1,__:[0]},8,["disabled"]),(n=s.value)!=null&&n.closed_by?(r(),g(i(_),{key:1,variant:"outline",onClick:L,disabled:!s.value,class:"text-blue-600 hover:text-blue-700"},{default:l(()=>e[2]||(e[2]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"})],-1),d(" Reopen Auction ")])),_:1,__:[2]},8,["disabled"])):(r(),g(i(_),{key:0,variant:"outline",onClick:z,disabled:!s.value,class:"text-orange-600 hover:text-orange-700"},{default:l(()=>e[1]||(e[1]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})],-1),d(" Close Auction ")])),_:1,__:[1]},8,["disabled"])),u(i(_),{variant:"outline",onClick:H,disabled:!s.value,class:"text-red-600 hover:text-red-700"},{default:l(()=>e[3]||(e[3]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),d(" Delete Auction ")])),_:1,__:[3]},8,["disabled"])])]}),default:l(()=>[s.value?(r(),w("div",Q,[u(i(m),{class:"p-6"},{default:l(()=>[t("div",W,[e[4]||(e[4]=t("div",null,[t("h3",{class:"text-lg font-medium text-gray-900"},"Status & Type"),t("p",{class:"text-sm text-gray-500 mt-1"},"Current auction status and configuration")],-1)),t("div",X,[s.value.auctionType?(r(),g(i($),{key:0,variant:F(s.value.auctionType.type),size:"lg"},{default:l(()=>[d(a(s.value.auctionType.name),1)]),_:1},8,["variant"])):c("",!0),u(i($),{variant:s.value.closed_by?"success":"warning",size:"lg"},{default:l(()=>[d(a(s.value.closed_by?"Closed":"Active"),1)]),_:1},8,["variant"])])])]),_:1}),u(i(m),{class:"p-6"},{default:l(()=>{var n,x;return[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),t("div",Y,[t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-700"},"Auction Name",-1)),t("p",Z,a(s.value.name||"Unnamed Auction"),1)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-medium text-gray-700"},"Auction Code",-1)),t("p",tt,a(s.value.code||"No code assigned"),1)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-700"},"Auction Type",-1)),t("p",et,a(((n=s.value.auctionType)==null?void 0:n.name)||"No type assigned"),1)]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-700"},"Branch",-1)),t("p",st,a(((x=s.value.branch)==null?void 0:x.name)||"No branch assigned"),1)]),s.value.description?(r(),w("div",at,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700"},"Description",-1)),t("p",ot,a(s.value.description),1)])):c("",!0)])]}),_:1,__:[10]}),s.value.item?(r(),g(i(m),{key:0,class:"p-6"},{default:l(()=>[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Associated Item",-1)),t("div",lt,[t("img",{src:s.value.item.image||"/img/product.jpeg",alt:s.value.item.name,class:"w-32 h-32 rounded-lg object-cover cursor-pointer hover:opacity-75 transition-opacity",onClick:V},null,8,it),t("div",nt,[t("h4",rt,a(s.value.item.name),1),t("p",ut,"Ref: "+a(s.value.item.reference_number||"N/A"),1),t("p",dt,"Target Amount: "+a(p(s.value.item.target_amount)),1),s.value.item.description?(r(),w("p",ct,a(s.value.item.description),1)):c("",!0),t("div",mt,[u(i($),{variant:s.value.item.closed_by?"success":"warning",size:"sm"},{default:l(()=>[d(a(s.value.item.closed_by?"Sold":"Available"),1)]),_:1},8,["variant"]),u(i(_),{variant:"outline",size:"sm",onClick:V,class:"text-blue-600 hover:text-blue-700"},{default:l(()=>e[11]||(e[11]=[d(" View Item Details ")])),_:1,__:[11]})])])])]),_:1,__:[12]})):c("",!0),u(i(m),{class:"p-6"},{default:l(()=>{var n;return[e[16]||(e[16]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Bidding Information",-1)),t("div",vt,[t("div",xt,[t("div",gt,a(p(s.value.bid_amount)),1),e[13]||(e[13]=t("div",{class:"text-sm text-gray-500"},"Current Bid",-1))]),t("div",ft,[t("div",pt,a(p(s.value.initial_payment)),1),e[14]||(e[14]=t("div",{class:"text-sm text-gray-500"},"Initial Payment",-1))]),t("div",yt,[t("div",_t,a(p((n=s.value.item)==null?void 0:n.target_amount)),1),e[15]||(e[15]=t("div",{class:"text-sm text-gray-500"},"Target Amount",-1))])])]}),_:1,__:[16]}),u(i(m),{class:"p-6"},{default:l(()=>[e[20]||(e[20]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Schedule",-1)),t("div",bt,[t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700"},"Start Date & Time",-1)),t("p",kt,a(k(s.value.date_from)),1)]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700"},"End Date & Time",-1)),t("p",ht,a(k(s.value.date_to)),1)])]),s.value.date_from&&s.value.date_to?(r(),w("div",wt,[t("p",At,[e[19]||(e[19]=t("span",{class:"font-medium"},"Duration:",-1)),d(" "+a(E(s.value.date_from,s.value.date_to)),1)])])):c("",!0)]),_:1,__:[20]}),s.value.user?(r(),g(i(m),{key:1,class:"p-6"},{default:l(()=>[e[23]||(e[23]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Owner/Bidder Information",-1)),t("div",Ct,[t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700"},"Name",-1)),t("p",Dt,a(s.value.user.name),1)]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700"},"Email",-1)),t("p",Tt,a(s.value.user.email),1)])])]),_:1,__:[23]})):c("",!0),s.value.transaction?(r(),g(i(m),{key:2,class:"p-6"},{default:l(()=>{var n;return[e[26]||(e[26]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Transaction Information",-1)),t("div",Nt,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700"},"Transaction ID",-1)),t("p",Bt,a(s.value.transaction_id||"No transaction"),1)]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700"},"Amount",-1)),t("p",$t,a(p((n=s.value.transaction)==null?void 0:n.amount)),1)])])]}),_:1,__:[26]})):c("",!0),u(i(m),{class:"p-6"},{default:l(()=>[e[29]||(e[29]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Timestamps",-1)),t("div",It,[t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700"},"Created",-1)),t("p",Vt,a(k(s.value.created_at)),1)]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700"},"Last Updated",-1)),t("p",Mt,a(k(s.value.updated_at)),1)])])]),_:1,__:[29]})])):c("",!0)]),_:1},8,["title","subtitle","loading","error","breadcrumbs"])}}});export{Rt as default};
