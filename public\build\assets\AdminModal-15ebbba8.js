import{h as L,i as r,L as O,M as N,o,m as u,v as y,x as f,f as n,g as l,T as k,n as i,B as _,q,j as a,t as p,K as m,u as g,_ as w,H as h,y as A}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{_ as H}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";const D={class:"flex min-h-full items-center justify-center p-4"},F={class:"flex items-center justify-between"},K={class:"flex items-center space-x-3"},S={key:0,class:"text-lg font-medium text-gray-900"},I={key:1,class:"text-sm text-gray-500"},U={class:"flex justify-end space-x-3"},G=L({__name:"AdminModal",props:{modelValue:{type:Boolean},title:{},subtitle:{},icon:{},size:{default:"md"},variant:{default:"default"},closable:{type:Boolean,default:!0},closeOnBackdrop:{type:Boolean,default:!0},closeOnEscape:{type:Boolean,default:!0},showHeader:{type:Boolean,default:!0},showFooter:{type:Boolean,default:!1},showCancel:{type:Boolean,default:!0},showConfirm:{type:Boolean,default:!0},cancelText:{default:"Cancel"},confirmText:{default:"Confirm"},confirmVariant:{default:"primary"},loading:{type:Boolean,default:!1},persistent:{type:Boolean,default:!1}},emits:["update:modelValue","close","cancel","confirm"],setup(b,{emit:C}){const s=b,c=C,x=r(()=>`relative bg-white rounded-lg shadow-xl transform transition-all ${{sm:"max-w-sm w-full",md:"max-w-md w-full",lg:"max-w-lg w-full",xl:"max-w-xl w-full",full:"max-w-4xl w-full mx-4"}[s.size]}`),B=r(()=>`px-6 py-4 border-b border-gray-200 ${{default:"",danger:"bg-red-50 border-red-200",warning:"bg-yellow-50 border-yellow-200",success:"bg-green-50 border-green-200"}[s.variant]}`),M=r(()=>"px-6 py-4"),V=r(()=>"px-6 py-4 border-t border-gray-200 bg-gray-50"),j=r(()=>{const e="w-8 h-8 rounded-full flex items-center justify-center",t={info:"bg-blue-500",warning:"bg-yellow-500",error:"bg-red-500",success:"bg-green-500",question:"bg-purple-500"};return`${e} ${s.icon?t[s.icon]:""}`}),z=r(()=>s.icon?{info:{template:`
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `},warning:{template:`
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      `},error:{template:`
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `},success:{template:`
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
      `},question:{template:`
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      `}}[s.icon]:null),d=()=>{s.persistent||(c("update:modelValue",!1),c("close"))},$=()=>{c("cancel"),d()},E=()=>{c("confirm")},T=()=>{s.closeOnBackdrop&&d()},v=e=>{e.key==="Escape"&&s.closeOnEscape&&s.modelValue&&d()};return O(()=>{s.closeOnEscape&&document.addEventListener("keydown",v)}),N(()=>{s.closeOnEscape&&document.removeEventListener("keydown",v)}),(e,t)=>(o(),u(A,{to:"body"},[y(k,{"enter-active-class":"transition duration-300 ease-out","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition duration-200 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:f(()=>[e.modelValue?(o(),n("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:T},[t[2]||(t[2]=l("div",{class:"fixed inset-0 bg-black bg-opacity-50 transition-opacity"},null,-1)),l("div",D,[y(k,{"enter-active-class":"transition duration-300 ease-out","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition duration-200 ease-in","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:f(()=>[e.modelValue?(o(),n("div",{key:0,class:i(x.value),onClick:t[0]||(t[0]=_(()=>{},["stop"]))},[e.showHeader?(o(),n("div",{key:0,class:i(B.value)},[l("div",F,[l("div",K,[e.icon?(o(),n("div",{key:0,class:i(j.value)},[(o(),u(q(z.value),{class:"w-5 h-5 text-white"}))],2)):a("",!0),l("div",null,[e.title?(o(),n("h3",S,p(e.title),1)):a("",!0),e.subtitle?(o(),n("p",I,p(e.subtitle),1)):a("",!0)])]),e.closable?(o(),n("button",{key:0,onClick:d,class:"text-gray-400 hover:text-gray-600 transition-colors"},t[1]||(t[1]=[l("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):a("",!0)]),m(e.$slots,"header",{},void 0,!0)],2)):a("",!0),l("div",{class:i(M.value)},[m(e.$slots,"default",{},void 0,!0)],2),e.showFooter?(o(),n("div",{key:1,class:i(V.value)},[m(e.$slots,"footer",{},()=>[l("div",U,[e.showCancel?(o(),u(g(w),{key:0,variant:"outline",onClick:$,disabled:e.loading},{default:f(()=>[h(p(e.cancelText),1)]),_:1},8,["disabled"])):a("",!0),e.showConfirm?(o(),u(g(w),{key:1,variant:e.confirmVariant,onClick:E,loading:e.loading},{default:f(()=>[h(p(e.confirmText),1)]),_:1},8,["variant","loading"])):a("",!0)])],!0)],2)):a("",!0)],2)):a("",!0)]),_:3})])])):a("",!0)]),_:3})]))}}),R=H(G,[["__scopeId","data-v-cb6b3eab"]]);export{R as A};
