import{h as J,r as o,L as K,o as c,m as B,x as d,u,f as v,k as N,g as t,t as r,F as j,H as L,j as O,v as f,_ as k}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Q,e as U}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as V}from"./app-admin-1baa1658.js";import{_ as W}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as X}from"./useNotifications-98e2c61c.js";const Y={class:"px-6 py-4"},Z={class:"flex items-center"},ee={class:"flex-shrink-0 h-10 w-10"},te={class:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"},se={class:"text-sm font-medium text-gray-700"},ae={class:"ml-4"},oe={class:"text-sm font-medium text-gray-900"},ne=["href"],re={class:"text-sm text-gray-500"},le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ie={class:"px-6 py-4"},ce={class:"flex flex-wrap gap-1"},de={key:0,class:"text-gray-400 text-sm"},ue={class:"px-6 py-4 whitespace-nowrap"},me={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},pe={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},he={class:"flex justify-center space-x-2"},ke=J({__name:"UsersList",setup(_e){const g=Q(),{showNotification:h}=X(),y=o(!1),x=o(null),l=o([]),m=o(1),b=o(20),C=o(0),S=o(1),w=o([]),$=o({search:""}),z=[{key:"name",label:"User",sortable:!0},{key:"phone",label:"Phone",sortable:!1},{key:"roles",label:"Roles",sortable:!1},{key:"status",label:"Status",sortable:!0},{key:"last_login_at",label:"Last Login",sortable:!0}],i=async()=>{var e,a,_;y.value=!0;try{const s={page:m.value.toString(),per_page:b.value.toString(),...$.value},n=await V.get("/admin/users",s);w.value=n.data,C.value=((e=n.meta)==null?void 0:e.total)||0,S.value=((a=n.meta)==null?void 0:a.last_page)||1,m.value=((_=n.meta)==null?void 0:_.current_page)||1}catch(s){x.value="Failed to fetch users",console.error("Error fetching users:",s)}finally{y.value=!1}},A=()=>{m.value=1,i()},D=()=>{g.push("/admin-spa/users/create")},F=e=>{$.value.search=e,A()},M=(e,a)=>{console.log("Sort:",e,a)},P=e=>{m.value=e,i()},H=e=>{l.value=e?w.value.map(a=>a.id.toString()):[]},E=e=>{g.push(`/admin-spa/users/edit/${e.id}`)},I=e=>{g.push(`/admin-spa/users/view/${e.id}`)},R=async e=>{if(confirm("Are you sure you want to delete this user?"))try{await V.delete(`/admin/users/${e.id}`),h("User deleted successfully","success"),await i()}catch{h("Failed to delete user","error")}},T=async()=>{if(l.value.length!==0&&confirm(`Are you sure you want to delete ${l.value.length} users?`))try{h(`${l.value.length} users deleted successfully`,"success"),l.value=[],await i()}catch{h("Failed to delete users","error")}},q=e=>e?e.split(" ").map(a=>a[0]).join("").toUpperCase().slice(0,2):"?",G=e=>e?new Date(e).toLocaleDateString():"-";return K(()=>{i()}),(e,a)=>(c(),B(u(W),{title:"All Users",subtitle:"View and manage all users",loading:y.value,error:x.value,items:w.value,columns:z,"selected-items":l.value,"show-bulk-actions":!0,"current-page":m.value,"total-pages":S.value,"total-items":C.value,"per-page":b.value,"create-button-text":"Add User","empty-state-title":"No users found","empty-state-message":"Start by creating a new user account.",onCreate:D,onSearch:F,onSort:M,onPageChange:P,onSelectAll:H,onBulkDelete:T,onRefresh:i},{rows:d(({items:_})=>[(c(!0),v(j,null,N(_,s=>{var n;return c(),v("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",Y,[t("div",Z,[t("div",ee,[t("div",te,[t("span",se,r(q(s.name)),1)])]),t("div",ae,[t("div",oe,[t("a",{href:`/admin-spa/users/view/${s.id}`,class:"hover:text-blue-600"},r(s.name||"-"),9,ne)]),t("div",re,r(s.email||"-"),1)])])]),t("td",le,r(s.phone||"-"),1),t("td",ie,[t("div",ce,[(c(!0),v(j,null,N(s.roles,p=>(c(),B(u(U),{key:p.id,variant:"secondary",size:"sm"},{default:d(()=>[L(r(p.name),1)]),_:2},1024))),128)),(n=s.roles)!=null&&n.length?O("",!0):(c(),v("span",de,"No roles"))])]),t("td",ue,[f(u(U),{variant:s.email_verified_at?"success":"warning"},{default:d(()=>[L(r(s.email_verified_at?"Verified":"Unverified"),1)]),_:2},1032,["variant"])]),t("td",me,r(s.last_login_at?G(s.last_login_at):"Never"),1),t("td",pe,[t("div",he,[f(u(k),{size:"sm",variant:"outline",onClick:p=>E(s)},{default:d(()=>a[0]||(a[0]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[0]},1032,["onClick"]),f(u(k),{size:"sm",variant:"info",onClick:p=>I(s)},{default:d(()=>a[1]||(a[1]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[1]},1032,["onClick"]),f(u(k),{size:"sm",variant:"danger",onClick:p=>R(s)},{default:d(()=>a[2]||(a[2]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[2]},1032,["onClick"])])])])}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{ke as default};
