import{h as H,r as q,R as J,i,L as K,o as T,m as Q,x as s,u as o,v as r,g as m,t as v,f as W,j as X,H as d,_ as y}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Y,h as $}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{_ as Z}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{A as V}from"./AdminBadge-74cb3994.js";import{u as ee}from"./auctionTypes-eeea8f1c.js";import{u as te}from"./useNotifications-98e2c61c.js";import"./axios-917b1704.js";const ae={class:"min-w-0 flex-1"},se={class:"text-sm font-medium text-gray-900 truncate"},ne={key:0,class:"text-sm text-gray-500 truncate"},oe={class:"text-sm text-gray-900"},le={class:"text-sm text-gray-900"},ie={class:"text-xs text-gray-500"},re={class:"flex justify-end gap-2"},_e=H({__name:"AuctionTypesList",setup(ce){const l=ee(),f=Y(),{showNotification:p}=te(),n=q([]),u=J({type:"",is_active:"",search:""}),_=i(()=>l.auctionTypesList||[]),D=i(()=>l.loading),B=i(()=>l.error),h=i(()=>l.currentPage),N=i(()=>l.lastPage),z=i(()=>l.totalAuctionTypes),x=i(()=>{var e;return((e=l.paginatedAuctionTypes)==null?void 0:e.per_page)||20}),I=i(()=>[{key:"name",label:"Name & Description",sortable:!0},{key:"type",label:"Type",sortable:!0},{key:"status",label:"Status",sortable:!1},{key:"items_count",label:"Items",sortable:!0},{key:"auctions_count",label:"Auctions",sortable:!0},{key:"created_at",label:"Created",sortable:!0}]),P=i(()=>[{label:"All Types",value:""},{label:"Online Auction",value:"online"},{label:"Live Auction",value:"live"},{label:"Cash Sale",value:"cash"}]),L=i(()=>[{label:"All Status",value:""},{label:"Active",value:"true"},{label:"Inactive",value:"false"}]),c=async()=>{await l.fetchAuctionTypes({...u,page:h.value,per_page:x.value,is_active:u.is_active?u.is_active==="true":void 0})},A=()=>{c()},E=()=>{f.push("/admin-spa/auction-types/create")},b=e=>{f.push(`/admin-spa/auction-types/view/${e.id}`)},k=e=>{f.push(`/admin-spa/auction-types/edit/${e.id}`)},w=async e=>{if((e.items_count||0)>0){p("Cannot delete auction type with associated items","error");return}if(confirm(`Are you sure you want to delete "${e.name}"?`))try{await l.deleteAuctionType(e.id),p("Auction type deleted successfully","success"),await c()}catch{p("Failed to delete auction type","error")}},C=async()=>{if(n.value.length!==0&&confirm(`Are you sure you want to delete ${n.value.length} auction types?`))try{await l.bulkDeleteAuctionTypes(n.value),p(`${n.value.length} auction types deleted successfully`,"success"),n.value=[],await c()}catch{p("Failed to delete auction types","error")}},S=async e=>{if(n.value.length!==0)try{const t=e?"activated":"deactivated";p(`${n.value.length} auction types ${t}`,"success"),n.value=[],await c()}catch{p("Failed to update auction type status","error")}},F=e=>{u.search=e,c()},O=(e,t)=>{c()},R=e=>{c()},U=e=>{e?n.value=_.value.map(t=>t.id.toString()):n.value=[]},j=e=>{switch(e){case"live":return"success";case"online":return"primary";case"cash":return"warning";default:return"secondary"}},M=e=>{switch(e){case"live":return"Live Auction";case"online":return"Online Auction";case"cash":return"Cash Sale";default:return e}},G=e=>e?new Date(e).toLocaleDateString():"N/A";return K(async()=>{await c()}),(e,t)=>(T(),Q(o(Z),{title:"Auction Types",subtitle:"Manage auction categories and types",loading:D.value,error:B.value,items:_.value,columns:I.value,"selected-items":n.value,"show-bulk-actions":!0,"current-page":h.value,"total-pages":N.value,"total-items":z.value,"per-page":x.value,"create-button-text":"Add Auction Type","empty-state-title":"No auction types found","empty-state-message":"Get started by adding your first auction type.",onCreate:E,onSearch:F,onSort:O,onPageChange:R,onSelectAll:U,onBulkDelete:C,onView:b,onEdit:k,onDelete:w,onRefresh:c},{filters:s(()=>[r(o($),{modelValue:u.type,"onUpdate:modelValue":t[0]||(t[0]=a=>u.type=a),placeholder:"All Types",options:P.value,onChange:A},null,8,["modelValue","options"]),r(o($),{modelValue:u.is_active,"onUpdate:modelValue":t[1]||(t[1]=a=>u.is_active=a),placeholder:"All Status",options:L.value,onChange:A},null,8,["modelValue","options"])]),"cell-name":s(({item:a})=>[m("div",ae,[m("p",se,v(a.name),1),a.description?(T(),W("p",ne,v(a.description),1)):X("",!0)])]),"cell-type":s(({item:a})=>[r(o(V),{variant:j(a.type),size:"sm"},{default:s(()=>[d(v(M(a.type)),1)]),_:2},1032,["variant"])]),"cell-status":s(({item:a})=>[r(o(V),{variant:a.is_active!==!1?"success":"secondary",size:"sm"},{default:s(()=>[d(v(a.is_active!==!1?"Active":"Inactive"),1)]),_:2},1032,["variant"])]),"cell-items_count":s(({item:a})=>[m("div",oe,v(a.items_count||0)+" items ",1)]),"cell-auctions_count":s(({item:a})=>[m("div",le,v(a.auctions_count||0)+" auctions ",1)]),"cell-created_at":s(({item:a})=>[m("div",ie,v(G(a.created_at)),1)]),actions:s(({item:a})=>[m("div",re,[r(o(y),{variant:"ghost",size:"sm",onClick:g=>b(a),class:"text-blue-600 hover:text-blue-700"},{default:s(()=>t[4]||(t[4]=[d(" View ")])),_:2,__:[4]},1032,["onClick"]),r(o(y),{variant:"ghost",size:"sm",onClick:g=>k(a),class:"text-green-600 hover:text-green-700"},{default:s(()=>t[5]||(t[5]=[d(" Edit ")])),_:2,__:[5]},1032,["onClick"]),r(o(y),{variant:"ghost",size:"sm",onClick:g=>w(a),class:"text-red-600 hover:text-red-700",disabled:(a.items_count||0)>0},{default:s(()=>t[6]||(t[6]=[d(" Delete ")])),_:2,__:[6]},1032,["onClick","disabled"])])]),"bulk-actions":s(({selectedItems:a})=>[r(o(y),{variant:"outline",size:"sm",onClick:t[2]||(t[2]=g=>S(!0)),class:"text-green-600 hover:text-green-700"},{default:s(()=>t[7]||(t[7]=[d(" Activate Selected ")])),_:1,__:[7]}),r(o(y),{variant:"outline",size:"sm",onClick:t[3]||(t[3]=g=>S(!1)),class:"text-gray-600 hover:text-gray-700"},{default:s(()=>t[8]||(t[8]=[d(" Deactivate Selected ")])),_:1,__:[8]}),r(o(y),{variant:"outline",size:"sm",onClick:C,class:"text-red-600 hover:text-red-700"},{default:s(()=>t[9]||(t[9]=[d(" Delete Selected ")])),_:1,__:[9]})]),_:1},8,["loading","error","items","columns","selected-items","current-page","total-pages","total-items","per-page"]))}});export{_e as default};
