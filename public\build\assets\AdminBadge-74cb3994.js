import{h as C,i as r,o as s,f as n,m as w,n as l,q as B,j as a,K as z,t as M,g as c}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import"./app-admin-1baa1658.js";import{_ as j}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";const L={key:1},$={key:2},_=C({__name:"AdminBadge",props:{text:{},variant:{default:"default"},size:{default:"sm"},rounded:{type:Boolean,default:!1},outlined:{type:Boolean,default:!1},dot:{type:Boolean,default:!1},icon:{},closable:{type:Boolean,default:!1},pulse:{type:Boolean,default:!1}},emits:["close"],setup(g,{emit:p}){const e=g,b=p,u={check:{template:`
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    `},x:{template:`
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    `},exclamation:{template:`
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    `},info:{template:`
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    `},star:{template:`
      <svg fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
      </svg>
    `},clock:{template:`
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    `},user:{template:`
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    `}},m=r(()=>{const o="inline-flex items-center font-medium transition-all duration-200",t={xs:"px-2 py-0.5 text-xs",sm:"px-2.5 py-0.5 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-1 text-sm"},i=e.outlined?{default:"text-gray-700 bg-white border border-gray-300",primary:"text-blue-700 bg-blue-50 border border-blue-200",secondary:"text-gray-700 bg-gray-50 border border-gray-200",success:"text-green-700 bg-green-50 border border-green-200",danger:"text-red-700 bg-red-50 border border-red-200",warning:"text-yellow-700 bg-yellow-50 border border-yellow-200",info:"text-blue-700 bg-blue-50 border border-blue-200",dark:"text-gray-700 bg-gray-50 border border-gray-200"}:{default:"text-gray-800 bg-gray-100",primary:"text-blue-800 bg-blue-100",secondary:"text-gray-800 bg-gray-200",success:"text-green-800 bg-green-100",danger:"text-red-800 bg-red-100",warning:"text-yellow-800 bg-yellow-100",info:"text-blue-800 bg-blue-100",dark:"text-white bg-gray-800"},d=e.rounded?"rounded-full":"rounded-md",h=e.pulse?"animate-pulse":"";return[o,t[e.size],i[e.variant],d,h].filter(Boolean).join(" ")}),f=r(()=>e.icon&&u[e.icon]?u[e.icon]:null),y=r(()=>{const o="w-3 h-3",t=e.text||e.$slots.default?"mr-1":"";return`${o} ${t}`}),k=r(()=>{const o="w-2 h-2 rounded-full",t={default:"bg-gray-400",primary:"bg-blue-400",secondary:"bg-gray-400",success:"bg-green-400",danger:"bg-red-400",warning:"bg-yellow-400",info:"bg-blue-400",dark:"bg-gray-600"},i=e.text||e.$slots.default||e.icon?"ml-1":"",d=e.pulse?"animate-pulse":"";return[o,t[e.variant],i,d].filter(Boolean).join(" ")}),x=r(()=>{const o="ml-1 inline-flex items-center justify-center rounded-full hover:bg-black hover:bg-opacity-10 focus:outline-none focus:bg-black focus:bg-opacity-10 transition-colors duration-200",t=e.size==="xs"?"w-4 h-4":"w-5 h-5";return`${o} ${t}`}),v=()=>{b("close")};return(o,t)=>(s(),n("span",{class:l(m.value)},[o.icon?(s(),w(B(f.value),{key:0,class:l(y.value)},null,8,["class"])):a("",!0),o.$slots.default?(s(),n("span",L,[z(o.$slots,"default",{},void 0,!0)])):o.text?(s(),n("span",$,M(o.text),1)):a("",!0),o.dot?(s(),n("span",{key:3,class:l(k.value)},null,2)):a("",!0),o.closable?(s(),n("button",{key:4,onClick:v,class:l(x.value),type:"button"},t[0]||(t[0]=[c("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[c("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),2)):a("",!0)],2))}}),S=j(_,[["__scopeId","data-v-13370b05"]]);export{S as A};
