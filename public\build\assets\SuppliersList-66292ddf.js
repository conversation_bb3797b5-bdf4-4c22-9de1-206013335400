import{h as I,r as o,R,L as G,o as h,m as T,x as u,u as p,f as k,k as q,g as e,t as d,j as J,v,H as K,_ as x,F as O}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Q,e as U}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as N}from"./app-admin-1baa1658.js";import{_ as W}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as X}from"./useNotifications-98e2c61c.js";const Y={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Z={class:"px-6 py-4"},ee={class:"flex items-center"},te={class:"flex-shrink-0"},se=["src","alt"],ae={class:"ml-4"},oe={class:"text-sm font-medium text-gray-900"},re=["href"],le={key:0,class:"text-sm text-gray-500"},ne={class:"px-6 py-4 whitespace-nowrap"},ie={class:"text-sm text-gray-900"},ce={class:"px-6 py-4 whitespace-nowrap text-center"},de={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ue={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},pe={class:"flex justify-center space-x-2"},ye=I({__name:"SuppliersList",setup(me){const _=Q(),{showNotification:m}=X(),f=o(!1),w=o(null),r=o([]),l=o(1),g=o(20),b=o(0),C=o(1),y=o([]),S=R({search:""}),A=[{key:"index",label:"#",sortable:!1},{key:"name",label:"Name",sortable:!0},{key:"email",label:"Email Address",sortable:!0},{key:"items_count",label:"Items",sortable:!1},{key:"branch",label:"Branch",sortable:!0}],n=async()=>{var t,s,i;f.value=!0;try{const a={page:l.value.toString(),per_page:g.value.toString(),...S},c=await N.get("/suppliers",a);y.value=c.data,b.value=((t=c.meta)==null?void 0:t.total)||0,C.value=((s=c.meta)==null?void 0:s.last_page)||1,l.value=((i=c.meta)==null?void 0:i.current_page)||1}catch(a){w.value="Failed to fetch suppliers",console.error("Error fetching suppliers:",a)}finally{f.value=!1}},M=()=>{_.push("/admin-spa/suppliers/create")},j=t=>{S.search=t,l.value=1,n()},z=(t,s)=>{console.log("Sort:",t,s)},V=t=>{l.value=t,n()},F=t=>{r.value=t?y.value.map(s=>s.id.toString()):[]},L=t=>{_.push(`/admin-spa/suppliers/edit/${t.id}`)},P=t=>{_.push(`/admin-spa/suppliers/view/${t.id}`)},D=async t=>{if(confirm("Are you sure you want to delete this supplier?"))try{await N.delete(`/users/${t.id}`),m("Supplier deleted successfully","success"),await n()}catch{m("Failed to delete supplier","error")}},E=async()=>{if(r.value.length!==0&&confirm(`Are you sure you want to delete ${r.value.length} suppliers?`))try{m(`${r.value.length} suppliers deleted successfully`,"success"),r.value=[],await n()}catch{m("Failed to delete suppliers","error")}},H=t=>{var s;return((s=t.items)==null?void 0:s.filter(i=>!i.closed_by).length)||0};return G(()=>{n()}),(t,s)=>(h(),T(p(W),{title:"Suppliers",subtitle:"Manage suppliers and vendors",loading:f.value,error:w.value,items:y.value,columns:A,"selected-items":r.value,"show-bulk-actions":!0,"current-page":l.value,"total-pages":C.value,"total-items":b.value,"per-page":g.value,"create-button-text":"Add Supplier","empty-state-title":"No suppliers found","empty-state-message":"Get started by adding your first supplier.",onCreate:M,onSearch:j,onSort:z,onPageChange:V,onSelectAll:F,onBulkDelete:E,onRefresh:n},{rows:u(({items:i})=>[(h(!0),k(O,null,q(i,(a,c)=>{var $;return h(),k("tr",{key:a.id,class:"hover:bg-gray-50"},[e("td",Y,d((l.value-1)*g.value+c+1),1),e("td",Z,[e("div",ee,[e("div",te,[e("img",{class:"h-10 w-10 rounded-full",src:a.avatar||"/images/default-avatar.png",alt:a.name},null,8,se)]),e("div",ae,[e("div",oe,[e("a",{href:`/suppliers/${a.id}`,class:"hover:text-blue-600"},d(a.name),9,re)]),a.phone?(h(),k("div",le,d(a.phone),1)):J("",!0)])])]),e("td",ne,[e("div",ie,d(a.email),1)]),e("td",ce,[v(p(U),{variant:"primary"},{default:u(()=>[K(d(H(a)),1)]),_:2},1024)]),e("td",de,d((($=a.branch)==null?void 0:$.name)||"-"),1),e("td",ue,[e("div",pe,[v(p(x),{size:"sm",variant:"outline",onClick:B=>L(a)},{default:u(()=>s[0]||(s[0]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[0]},1032,["onClick"]),v(p(x),{size:"sm",variant:"info",onClick:B=>P(a)},{default:u(()=>s[1]||(s[1]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[1]},1032,["onClick"]),v(p(x),{size:"sm",variant:"danger",onClick:B=>D(a)},{default:u(()=>s[2]||(s[2]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[2]},1032,["onClick"])])])])}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{ye as default};
