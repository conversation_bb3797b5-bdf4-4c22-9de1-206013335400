import{h as X,r as o,i as M,L as Y,o as i,m as Z,x as c,u as n,v as d,f as m,k as ee,n as te,g as a,t as v,j as P,H as _,_ as y,F as se}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as ae,h as L,e as oe}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as g}from"./app-admin-1baa1658.js";import{_ as le}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as ne}from"./useNotifications-98e2c61c.js";const re={class:"px-6 py-4"},ie={key:0,class:"flex items-center"},ce={class:"flex-shrink-0 h-16 w-16"},de=["src","alt"],ue={class:"ml-4"},me={class:"text-sm font-medium text-gray-900"},pe=["href"],he={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},fe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ve={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"},_e={class:"px-6 py-4 text-sm text-gray-900"},ye={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},ge={key:0,class:"space-y-2"},ke={key:0,class:"flex justify-center"},we={key:1,class:"flex justify-center space-x-2"},Ve=X({__name:"AuctionBidsList",setup(be){const C=ae(),{showNotification:p}=ne(),k=o(!1),A=o(null),B=o([]),h=o(1),S=o(20),V=o(0),$=o(1),w=o([]),j=o([]),u=o({item_id:"",status:"",search:""}),I=[{key:"item",label:"Item",sortable:!1},{key:"user",label:"Name",sortable:!0},{key:"auction_type",label:"Auction Listing",sortable:!1},{key:"bid_amount",label:"Bid Amount",sortable:!0},{key:"description",label:"Description",sortable:!1}],U=M(()=>[{label:"All Items",value:""},...j.value.map(e=>({label:e.name||"",value:e.id.toString()}))]),D=M(()=>[{label:"All Status",value:""},{label:"Open",value:"open"},{label:"Closed",value:"closed"}]),f=async()=>{var e,t,l;k.value=!0;try{const s={page:h.value.toString(),per_page:S.value.toString(),...u.value},r=await g.get("/auctions",s);w.value=r.data,V.value=((e=r.meta)==null?void 0:e.total)||0,$.value=((t=r.meta)==null?void 0:t.last_page)||1,h.value=((l=r.meta)==null?void 0:l.current_page)||1}catch(s){A.value="Failed to fetch auctions",console.error("Error fetching auctions:",s)}finally{k.value=!1}},E=async()=>{try{const e=await g.get("/items");j.value=e.data||[]}catch(e){console.error("Error fetching items:",e)}},b=()=>{h.value=1,f()},O=()=>{C.push("/auctions/create")},H=e=>{u.value.search=e,b()},T=(e,t)=>{console.log("Sort:",e,t)},R=e=>{h.value=e,f()},q=e=>{B.value=e?w.value.map(t=>t.id):[]},G=async e=>{if(confirm("Are you sure you want to accept this bid as a winner?"))try{await g.get(`/accept-bid/${e.id}`),p("Bid accepted successfully","success"),await f()}catch{p("Failed to accept bid","error")}},J=e=>{C.push(`/auctions/${e.id}`)},K=async e=>{if(confirm("Are you sure you want to cancel this bid?"))try{await g.delete(`/auctions/${e.id}`),p("Bid cancelled successfully","success"),await f()}catch{p("Failed to cancel bid","error")}},Q=e=>{console.log("Add payment for auction:",e.id),p("Payment functionality not yet implemented","info")},W=e=>{if(!e)return"-";const t=typeof e=="string"?parseFloat(e):e;return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t)};return Y(()=>{f(),E()}),(e,t)=>(i(),Z(n(le),{title:"Bid List",subtitle:"View and manage all auction bids",loading:k.value,error:A.value,items:w.value,columns:I,"selected-items":B.value,"show-bulk-actions":!1,"current-page":h.value,"total-pages":$.value,"total-items":V.value,"per-page":S.value,"create-button-text":"Add Bid","empty-state-title":"No bids found","empty-state-message":"Bids will appear here when users place them.",onCreate:O,onSearch:H,onSort:T,onPageChange:R,onSelectAll:q},{filters:c(()=>[d(n(L),{modelValue:u.value.item_id,"onUpdate:modelValue":t[0]||(t[0]=l=>u.value.item_id=l),placeholder:"All Items",options:U.value,onChange:b},null,8,["modelValue","options"]),d(n(L),{modelValue:u.value.status,"onUpdate:modelValue":t[1]||(t[1]=l=>u.value.status=l),placeholder:"All Status",options:D.value,onChange:b},null,8,["modelValue","options"])]),rows:c(({items:l})=>[(i(!0),m(se,null,ee(l,s=>{var r,N,z,F;return i(),m("tr",{key:s.id,class:te({"bg-yellow-50":(r=s.item)==null?void 0:r.closed_by})},[a("td",re,[s.item?(i(),m("div",ie,[a("div",ce,[a("img",{src:s.item.image||"/placeholder-image.jpg",alt:s.item.name,class:"h-16 w-16 rounded-lg object-cover"},null,8,de)]),a("div",ue,[a("div",me,[a("a",{href:`/auctions/${s.id}`,class:"hover:text-blue-600"},v(s.item.name||"-"),9,pe)])])])):P("",!0)]),a("td",he,v(((N=s.user)==null?void 0:N.name)||"-"),1),a("td",fe,v(((z=s.auctionType)==null?void 0:z.name)||"-"),1),a("td",ve,v(W(s.bid_amount)),1),a("td",_e,v(s.description||"-"),1),a("td",ye,[(F=s.item)!=null&&F.closed_by?(i(),m("div",ge,[s.closed_by&&!s.tagged_by?(i(),m("div",ke,[d(n(y),{size:"sm",variant:"primary",onClick:x=>Q(s)},{default:c(()=>t[2]||(t[2]=[a("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),_(" Add Payment ")])),_:2,__:[2]},1032,["onClick"])])):P("",!0),d(n(oe),{variant:"success"},{default:c(()=>t[3]||(t[3]=[_("Accepted")])),_:1,__:[3]})])):(i(),m("div",we,[d(n(y),{size:"sm",variant:"success",onClick:x=>G(s)},{default:c(()=>t[4]||(t[4]=[a("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),_(" Accept ")])),_:2,__:[4]},1032,["onClick"]),d(n(y),{size:"sm",variant:"info",onClick:x=>J(s)},{default:c(()=>t[5]||(t[5]=[a("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1),_(" View ")])),_:2,__:[5]},1032,["onClick"]),d(n(y),{size:"sm",variant:"danger",onClick:x=>K(s)},{default:c(()=>t[6]||(t[6]=[a("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),_(" Cancel ")])),_:2,__:[6]},1032,["onClick"])]))])],2)}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{Ve as default};
