import{h as q,r as o,L as G,o as k,m as J,x as r,u,v as m,g as t,H as S,_ as v,f as $,k as K,t as p,F as O}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Q,e as W}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as B}from"./app-admin-1baa1658.js";import{_ as X}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as Y}from"./useNotifications-98e2c61c.js";const Z={class:"px-6 py-4"},L={class:"flex items-center"},ee={class:"flex-shrink-0 h-16 w-16"},te=["src","alt"],se={class:"ml-4"},ae={class:"text-sm font-medium text-gray-900"},oe=["href"],ne={class:"px-6 py-4 whitespace-nowrap text-center"},re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ie={class:"px-6 py-4 text-sm text-gray-900"},ce=["title"],ue={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},de={class:"flex justify-center space-x-2"},fe=q({__name:"AuctionListingIndex",setup(me){const h=Q(),{showNotification:g}=Y(),_=o(!1),y=o(null),l=o([]),d=o(1),w=o(20),x=o(0),b=o(1),f=o([]),C=o({search:""}),D=[{key:"name",label:"Name",sortable:!0},{key:"items_count",label:"Available Items",sortable:!1},{key:"date_from",label:"Start Date",sortable:!0},{key:"date_to",label:"End Date",sortable:!0},{key:"description",label:"Description",sortable:!1}],i=async()=>{var e,s,c;_.value=!0;try{const a={page:d.value.toString(),per_page:w.value.toString(),...C.value},n=await B.get("/auction-listings",a);f.value=n.data,x.value=((e=n.meta)==null?void 0:e.total)||0,b.value=((s=n.meta)==null?void 0:s.last_page)||1,d.value=((c=n.meta)==null?void 0:c.current_page)||1}catch(a){y.value="Failed to fetch auction listings",console.error("Error fetching auction listings:",a)}finally{_.value=!1}},j=()=>{d.value=1,i()},M=()=>{h.push("/admin-spa/auction-listings/create")},I=()=>{h.push("/admin-spa/items/create")},N=e=>{C.value.search=e,j()},z=(e,s)=>{console.log("Sort:",e,s)},F=e=>{d.value=e,i()},H=e=>{l.value=e?f.value.map(s=>s.id.toString()):[]},P=e=>{h.push(`/admin-spa/auction-listings/edit/${e.id}`)},V=e=>{h.push(`/admin-spa/auction-listings/view/${e.id}`)},E=async e=>{if(confirm("Are you sure you want to delete this auction listing?"))try{await B.delete(`/auction-types/${e.id}`),g("Auction listing deleted successfully","success"),await i()}catch{g("Failed to delete auction listing","error")}},R=async()=>{if(l.value.length!==0&&confirm(`Are you sure you want to delete ${l.value.length} auction listings?`))try{g(`${l.value.length} auction listings deleted successfully`,"success"),l.value=[],await i()}catch{g("Failed to delete auction listings","error")}},T=e=>{var s;return((s=e.items)==null?void 0:s.filter(c=>!c.closed_by).length)||0},A=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"-",U=(e,s)=>e?e.length>s?e.substring(0,s)+"...":e:"-";return G(()=>{i()}),(e,s)=>(k(),J(u(X),{title:"Auction Listings",subtitle:"Manage auction listings and bidding sessions",loading:_.value,error:y.value,items:f.value,columns:D,"selected-items":l.value,"show-bulk-actions":!0,"current-page":d.value,"total-pages":b.value,"total-items":x.value,"per-page":w.value,"create-button-text":"Add Auction Listing","empty-state-title":"No auction listings found","empty-state-message":"Start by creating a new auction listing.",onCreate:M,onSearch:N,onSort:z,onPageChange:F,onSelectAll:H,onBulkDelete:R,onRefresh:i},{actions:r(()=>[m(u(v),{variant:"outline",onClick:I,class:"mr-2"},{default:r(()=>s[0]||(s[0]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),S(" Add Item ")])),_:1,__:[0]})]),rows:r(({items:c})=>[(k(!0),$(O,null,K(c,a=>(k(),$("tr",{key:a.id,class:"hover:bg-gray-50"},[t("td",Z,[t("div",L,[t("div",ee,[t("img",{src:a.image||"/assets/img/160x160/img2.jpg",alt:a.name,class:"h-16 w-16 rounded-lg object-cover"},null,8,te)]),t("div",se,[t("div",ae,[t("a",{href:`/auction-listing/${a.id}`,class:"hover:text-blue-600"},p(a.name||"-"),9,oe)])])])]),t("td",ne,[m(u(W),{variant:"primary"},{default:r(()=>[S(p(T(a)),1)]),_:2},1024)]),t("td",re,p(A(a.date_from)),1),t("td",le,p(A(a.date_to)),1),t("td",ie,[t("span",{class:"truncate block max-w-xs",title:a.description},p(U(a.description,50)),9,ce)]),t("td",ue,[t("div",de,[m(u(v),{size:"sm",variant:"outline",onClick:n=>P(a)},{default:r(()=>s[1]||(s[1]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[1]},1032,["onClick"]),m(u(v),{size:"sm",variant:"info",onClick:n=>V(a)},{default:r(()=>s[2]||(s[2]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[2]},1032,["onClick"]),m(u(v),{size:"sm",variant:"danger",onClick:n=>E(a)},{default:r(()=>s[3]||(s[3]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[3]},1032,["onClick"])])])]))),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{fe as default};
