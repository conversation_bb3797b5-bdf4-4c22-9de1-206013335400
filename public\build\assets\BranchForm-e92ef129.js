import{h as M,r as y,R as $,i as k,L as A,o as d,f as n,g as s,t as o,B as D,C as m,D as f,n as c,j as b,Q as R,F as L,k as O,v as B,x as C,u as S,H as V,_ as N}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as P,f as T}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{u as z}from"./useNotifications-98e2c61c.js";import{b as v}from"./app-admin-1baa1658.js";const H={class:"max-w-4xl mx-auto p-6"},I={class:"bg-white shadow rounded-lg"},Q={class:"px-6 py-4 border-b border-gray-200"},G={class:"text-xl font-semibold text-gray-900"},J={class:"mt-1 text-sm text-gray-600"},K={key:0,class:"mt-1 text-sm text-red-600"},W={key:0,class:"mt-1 text-sm text-red-600"},X={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Y={key:0,class:"mt-1 text-sm text-red-600"},Z={key:0,class:"mt-1 text-sm text-red-600"},ee=["value"],se={key:0,class:"mt-1 text-sm text-red-600"},ae={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},ne=M({__name:"BranchForm",setup(te){const h=P(),x=T(),{showNotification:p}=z(),u=y(!1),g=y({}),a=y({}),r=$({name:"",address:"",phone:"",email:"",status_id:""}),i=k(()=>!!x.params.id),_=k(()=>x.params.id),F=async()=>{try{const l=await v.get("/admin/branches/statuses");g.value=l}catch(l){console.error("Error fetching statuses:",l)}},U=async()=>{if(i.value){u.value=!0;try{const l=await v.get(`/admin/branches/${_.value}`);Object.assign(r,{name:l.name||"",address:l.address||"",phone:l.phone||"",email:l.email||"",status_id:l.status_id||""})}catch{p("Failed to fetch branch details","error"),h.push("/admin-spa/branches/list")}finally{u.value=!1}}},E=()=>(a.value={},r.name.trim()||(a.value.name="Branch name is required"),r.status_id||(a.value.status_id="Status is required"),r.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)&&(a.value.email="Please enter a valid email address"),Object.keys(a.value).length===0),j=async()=>{var l,e;if(E()){u.value=!0;try{const t={...r};i.value?(await v.put(`/admin/branches/${_.value}`,t),p("Branch updated successfully","success")):(await v.post("/admin/branches",t),p("Branch created successfully","success")),h.push("/admin-spa/branches/list")}catch(t){(e=(l=t.response)==null?void 0:l.data)!=null&&e.errors?a.value=t.response.data.errors:p(i.value?"Failed to update branch":"Failed to create branch","error")}finally{u.value=!1}}},q=()=>{h.push("/admin-spa/branches/list")};return A(async()=>{await F(),await U()}),(l,e)=>(d(),n("div",H,[s("div",I,[s("div",Q,[s("h1",G,o(i.value?"Edit Branch":"Create Branch"),1),s("p",J,o(i.value?"Update branch information":"Add a new branch location"),1)]),s("form",{onSubmit:D(j,["prevent"]),class:"p-6 space-y-6"},[s("div",null,[e[5]||(e[5]=s("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Branch Name * ",-1)),m(s("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=t=>r.name=t),type:"text",required:"",class:c(["mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",{"border-red-300":a.value.name}])},null,2),[[f,r.name]]),a.value.name?(d(),n("p",K,o(a.value.name),1)):b("",!0)]),s("div",null,[e[6]||(e[6]=s("label",{for:"address",class:"block text-sm font-medium text-gray-700"}," Address ",-1)),m(s("textarea",{id:"address","onUpdate:modelValue":e[1]||(e[1]=t=>r.address=t),rows:"3",class:c(["mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",{"border-red-300":a.value.address}])},null,2),[[f,r.address]]),a.value.address?(d(),n("p",W,o(a.value.address),1)):b("",!0)]),s("div",X,[s("div",null,[e[7]||(e[7]=s("label",{for:"phone",class:"block text-sm font-medium text-gray-700"}," Phone Number ",-1)),m(s("input",{id:"phone","onUpdate:modelValue":e[2]||(e[2]=t=>r.phone=t),type:"tel",class:c(["mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",{"border-red-300":a.value.phone}])},null,2),[[f,r.phone]]),a.value.phone?(d(),n("p",Y,o(a.value.phone),1)):b("",!0)]),s("div",null,[e[8]||(e[8]=s("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Email Address ",-1)),m(s("input",{id:"email","onUpdate:modelValue":e[3]||(e[3]=t=>r.email=t),type:"email",class:c(["mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",{"border-red-300":a.value.email}])},null,2),[[f,r.email]]),a.value.email?(d(),n("p",Z,o(a.value.email),1)):b("",!0)])]),s("div",null,[e[10]||(e[10]=s("label",{for:"status_id",class:"block text-sm font-medium text-gray-700"}," Status * ",-1)),m(s("select",{id:"status_id","onUpdate:modelValue":e[4]||(e[4]=t=>r.status_id=t),required:"",class:c(["mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",{"border-red-300":a.value.status_id}])},[e[9]||(e[9]=s("option",{value:""},"Select Status",-1)),(d(!0),n(L,null,O(g.value,(t,w)=>(d(),n("option",{key:w,value:w},o(t),9,ee))),128))],2),[[R,r.status_id]]),a.value.status_id?(d(),n("p",se,o(a.value.status_id),1)):b("",!0)]),s("div",ae,[B(S(N),{type:"button",variant:"outline",onClick:q},{default:C(()=>e[11]||(e[11]=[V(" Cancel ")])),_:1,__:[11]}),B(S(N),{type:"submit",loading:u.value,disabled:u.value},{default:C(()=>[V(o(i.value?"Update Branch":"Create Branch"),1)]),_:1},8,["loading","disabled"])])],32)])]))}});export{ne as default};
