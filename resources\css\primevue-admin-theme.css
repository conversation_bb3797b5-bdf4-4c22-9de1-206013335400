/* PrimeVue Admin Theme - Custom styling to match existing admin design */

/* Override PrimeVue CSS variables to match admin theme */
:root {
  /* Primary colors - matching admin brand */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #0068ff; /* Main brand color */
  --primary-600: #0052cc;
  --primary-700: #003d99;
  --primary-800: #002966;
  --primary-900: #001433;

  /* Surface colors - matching admin layout */
  --surface-0: #ffffff;
  --surface-50: #f9fafb;
  --surface-100: #f3f4f6;
  --surface-200: #e5e7eb;
  --surface-300: #d1d5db;
  --surface-400: #9ca3af;
  --surface-500: #6b7280;
  --surface-600: #4b5563;
  --surface-700: #374151;
  --surface-800: #1f2937;
  --surface-900: #111827;

  /* Text colors */
  --text-color: #111827;
  --text-color-secondary: #6b7280;
  --primary-color-text: #ffffff;

  /* Border radius */
  --border-radius: 0.5rem;
  --content-padding: 1rem;
  --inline-spacing: 0.5rem;
  --surface-border: #e5e7eb;
  --surface-hover: #f9fafb;
  --surface-ground: #ffffff;
  --surface-section: #ffffff;
  --surface-card: #ffffff;
  --surface-overlay: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

/* DataTable Styling */
.p-datatable {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.p-datatable .p-datatable-header {
  background: var(--surface-50);
  border-bottom: 1px solid var(--surface-border);
  padding: 1rem;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.p-datatable .p-datatable-thead > tr > th {
  background: var(--surface-50);
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--surface-border);
  border-right: none;
  text-align: left;
}

.p-datatable .p-datatable-thead > tr > th:first-child {
  border-radius: 0;
}

.p-datatable .p-datatable-thead > tr > th:last-child {
  border-radius: 0;
}

.p-datatable .p-datatable-tbody > tr {
  background: var(--surface-0);
  transition: background-color 0.2s;
}

.p-datatable .p-datatable-tbody > tr:hover {
  background: var(--surface-hover);
}

.p-datatable .p-datatable-tbody > tr.p-highlight {
  background: var(--primary-50);
  color: var(--primary-700);
}

.p-datatable .p-datatable-tbody > tr > td {
  color: var(--text-color);
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--gray-100);
  border-right: none;
}

.p-datatable .p-datatable-tbody > tr:last-child > td {
  border-bottom: none;
}

.p-datatable .p-datatable-footer {
  background: var(--surface-50);
  border-top: 1px solid var(--surface-border);
  padding: 1rem;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Toolbar Styling */
.p-toolbar {
  background: var(--surface-0);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
}

/* Button Styling */
.p-button {
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: all 0.2s;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.p-button.p-button-sm {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.p-button:not(.p-button-outlined):not(.p-button-text) {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: var(--primary-color-text);
}

.p-button:not(.p-button-outlined):not(.p-button-text):hover {
  background: var(--primary-600);
  border-color: var(--primary-600);
}

.p-button.p-button-outlined {
  background: transparent;
  border: 1px solid var(--gray-300);
  color: var(--gray-700);
}

.p-button.p-button-outlined:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.p-button.p-button-outlined.p-button-info {
  border-color: var(--primary-500);
  color: var(--primary-500);
}

.p-button.p-button-outlined.p-button-info:hover {
  background: var(--primary-50);
  border-color: var(--primary-600);
  color: var(--primary-600);
}

.p-button.p-button-outlined.p-button-success {
  border-color: #10b981;
  color: #10b981;
}

.p-button.p-button-outlined.p-button-success:hover {
  background: #ecfdf5;
  border-color: #059669;
  color: #059669;
}

.p-button.p-button-outlined.p-button-danger {
  border-color: #ef4444;
  color: #ef4444;
}

.p-button.p-button-outlined.p-button-danger:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* Input Styling */
.p-inputtext {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  background: var(--surface-0);
  color: var(--text-color);
  transition: all 0.2s;
}

.p-inputtext:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(0, 104, 255, 0.1);
  outline: none;
}

.p-inputtext:hover:not(:disabled) {
  border-color: var(--gray-400);
}

/* Dropdown Styling */
.p-dropdown {
  background: var(--surface-0);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  transition: all 0.2s;
}

.p-dropdown:not(.p-disabled):hover {
  border-color: var(--gray-400);
}

.p-dropdown:not(.p-disabled).p-focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(0, 104, 255, 0.1);
}

.p-dropdown .p-dropdown-label {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: var(--text-color);
}

.p-dropdown .p-dropdown-trigger {
  color: var(--gray-500);
  width: 2.5rem;
}

/* Dropdown Panel */
.p-dropdown-panel {
  background: var(--surface-overlay);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.p-dropdown-items .p-dropdown-item {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: var(--text-color);
  transition: background-color 0.2s;
}

.p-dropdown-items .p-dropdown-item:hover {
  background: var(--surface-hover);
}

.p-dropdown-items .p-dropdown-item.p-highlight {
  background: var(--primary-500);
  color: var(--primary-color-text);
}

/* Paginator Styling */
.p-paginator {
  background: var(--surface-0);
  border: 1px solid var(--surface-border);
  border-top: none;
  padding: 1rem;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.p-paginator .p-paginator-pages .p-paginator-page {
  color: var(--text-color);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  transition: all 0.2s;
  min-width: 2.5rem;
  height: 2.5rem;
  margin: 0 0.125rem;
}

.p-paginator .p-paginator-pages .p-paginator-page:hover {
  background: var(--surface-hover);
  border-color: var(--surface-border);
}

.p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background: var(--primary-500);
  color: var(--primary-color-text);
  border-color: var(--primary-500);
}

/* Progress Bar */
.p-progressbar {
  border-radius: var(--border-radius);
  background: var(--surface-200);
  height: 0.375rem;
}

.p-progressbar .p-progressbar-value {
  background: var(--primary-500);
  border-radius: var(--border-radius);
}

/* Toast Styling */
.p-toast .p-toast-message {
  border-radius: var(--border-radius);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dialog Styling */
.p-dialog {
  border-radius: var(--border-radius);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.p-dialog .p-dialog-header {
  background: var(--surface-0);
  border-bottom: 1px solid var(--surface-border);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  padding: 1.5rem;
}

.p-dialog .p-dialog-content {
  background: var(--surface-0);
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  background: var(--surface-0);
  border-top: 1px solid var(--surface-border);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  padding: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .p-datatable .p-datatable-thead > tr > th,
  .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.5rem;
    font-size: 0.8125rem;
  }
  
  .p-toolbar {
    padding: 0.75rem;
  }
  
  .p-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}
