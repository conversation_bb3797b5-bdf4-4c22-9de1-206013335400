import{h as V,r as i,L as S,o as r,f as l,g as e,n as h,H as w,t as n,C as x,Q as v,F as g,k as f,p as F}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as L}from"./export-37598506.js";const R={class:"space-y-6"},T={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},E={class:"flex items-center justify-between mb-6"},H={class:"flex items-center space-x-3"},N=["disabled"],z={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},D={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},$={class:"flex items-center"},q={class:"ml-4"},U={class:"text-2xl font-semibold text-blue-900"},X={class:"bg-green-50 border border-green-200 rounded-lg p-4"},G={class:"flex items-center"},P={class:"ml-4"},W={class:"text-2xl font-semibold text-green-900"},Q={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},J={class:"flex items-center"},K={class:"ml-4"},O={class:"text-2xl font-semibold text-yellow-900"},Y={class:"bg-red-50 border border-red-200 rounded-lg p-4"},Z={class:"flex items-center"},ee={class:"ml-4"},te={class:"text-2xl font-semibold text-red-900"},se={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},oe=["value"],re=["value"],le={key:0,class:"flex items-center justify-center py-12"},ne={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-6"},ie={class:"flex items-center"},ae={class:"text-red-800"},de={key:2,class:"overflow-x-auto"},ue={class:"min-w-full divide-y divide-gray-200"},ce={class:"bg-white divide-y divide-gray-200"},me={class:"px-6 py-4 whitespace-nowrap"},pe={class:"flex items-center"},xe={class:"flex-shrink-0 h-10 w-10"},ve=["src","alt"],ge={key:1,class:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"},fe={class:"ml-4"},ye={class:"text-sm font-medium text-gray-900"},be={class:"text-sm text-gray-500"},he={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},we={class:"px-6 py-4 whitespace-nowrap"},ke={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},_e={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ce={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ie={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Me=["onClick"],je=["onClick"],Be={key:3,class:"text-center py-12"},Fe=V({__name:"InventoryReport",setup(Ae){const d=i(!1),m=i(""),u=i([]),k=i([]),y=i([]),c=i({totalItems:0,availableItems:0,inAuction:0,soldItems:0}),a=i({category:"",status:"",branch:""}),p=async()=>{d.value=!0,m.value="";try{const o=await fetch("/api/admin/reports/inventory",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);const t=await o.json();if(t.success)u.value=t.items||[],c.value=t.summary||{totalItems:0,availableItems:0,inAuction:0,soldItems:0};else throw new Error(t.error||"Failed to fetch inventory data")}catch(o){m.value=o.message||"Failed to load inventory data",console.error("Inventory report error:",o)}finally{d.value=!1}},_=async()=>{try{const o=await fetch("/api/admin/reports/branches",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(o.ok){const t=await o.json();t.success&&(y.value=t.branches||[])}}catch(o){console.error("Failed to load branches:",o)}},C=()=>{p()},I=()=>{if(u.value.length===0){alert("No data to export");return}L(u.value,"csv")},M=o=>{console.log("Viewing item:",o)},j=o=>{console.log("Editing item:",o)},b=o=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(o),B=o=>({available:"bg-green-100 text-green-800",in_auction:"bg-yellow-100 text-yellow-800",sold:"bg-red-100 text-red-800",reserved:"bg-blue-100 text-blue-800"})[o]||"bg-gray-100 text-gray-800";return S(()=>{p(),_()}),(o,t)=>(r(),l("div",R,[e("div",T,[e("div",E,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Inventory Report"),e("p",{class:"text-gray-600 mt-1"},"Track inventory levels and status")],-1)),e("div",H,[e("button",{onClick:p,disabled:d.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[(r(),l("svg",{class:h(["w-4 h-4 mr-2",{"animate-spin":d.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[3]||(t[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),t[4]||(t[4]=w(" Refresh "))],8,N),e("button",{onClick:I,class:"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),w(" Export ")]))])]),e("div",z,[e("div",D,[e("div",$,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})])],-1)),e("div",q,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-blue-600"},"Total Items",-1)),e("p",U,n(c.value.totalItems),1)])])]),e("div",X,[e("div",G,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",P,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-green-600"},"Available",-1)),e("p",W,n(c.value.availableItems),1)])])]),e("div",Q,[e("div",J,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",K,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-yellow-600"},"In Auction",-1)),e("p",O,n(c.value.inAuction),1)])])]),e("div",Y,[e("div",Z,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})])],-1)),e("div",ee,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-red-600"},"Sold",-1)),e("p",te,n(c.value.soldItems),1)])])])]),e("div",se,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Category",-1)),x(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>a.value.category=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[t[15]||(t[15]=e("option",{value:""},"All Categories",-1)),(r(!0),l(g,null,f(k.value,s=>(r(),l("option",{key:s.id,value:s.id},n(s.name),9,oe))),128))],512),[[v,a.value.category]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),x(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>a.value.status=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},t[17]||(t[17]=[F('<option value="">All Statuses</option><option value="available">Available</option><option value="in_auction">In Auction</option><option value="sold">Sold</option><option value="reserved">Reserved</option>',5)]),512),[[v,a.value.status]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Branch",-1)),x(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>a.value.branch=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[t[19]||(t[19]=e("option",{value:""},"All Branches",-1)),(r(!0),l(g,null,f(y.value,s=>(r(),l("option",{key:s.id,value:s.id},n(s.name),9,re))),128))],512),[[v,a.value.branch]])]),e("div",{class:"flex items-end"},[e("button",{onClick:C,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Apply Filters ")])]),d.value?(r(),l("div",le,t[21]||(t[21]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"ml-2 text-gray-600"},"Loading inventory data...",-1)]))):m.value?(r(),l("div",ne,[e("div",ie,[t[22]||(t[22]=e("svg",{class:"h-5 w-5 text-red-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("span",ae,n(m.value),1)])])):u.value.length>0?(r(),l("div",de,[e("table",ue,[t[24]||(t[24]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Item"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Category"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Starting Price"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Current Value"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Branch"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",ce,[(r(!0),l(g,null,f(u.value,s=>(r(),l("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",me,[e("div",pe,[e("div",xe,[s.image?(r(),l("img",{key:0,src:s.image,alt:s.title,class:"h-10 w-10 rounded-full object-cover"},null,8,ve)):(r(),l("div",ge,t[23]||(t[23]=[e("svg",{class:"h-6 w-6 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)])))]),e("div",fe,[e("div",ye,n(s.title),1),e("div",be,"#"+n(s.id),1)])])]),e("td",he,n(s.category),1),e("td",we,[e("span",{class:h([B(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(s.status),3)]),e("td",ke," $"+n(b(s.starting_price)),1),e("td",_e," $"+n(b(s.current_value)),1),e("td",Ce,n(s.branch),1),e("td",Ie,[e("button",{onClick:A=>M(s),class:"text-blue-600 hover:text-blue-900 mr-3"}," View ",8,Me),e("button",{onClick:A=>j(s),class:"text-green-600 hover:text-green-900"}," Edit ",8,je)])]))),128))])])])):(r(),l("div",Be,t[25]||(t[25]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No items found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No inventory items match the current filters.",-1)])))])]))}});export{Fe as default};
