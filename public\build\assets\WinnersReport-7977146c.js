import{h as R,r as a,L as T,o as r,f as l,g as e,n as h,H as b,t as n,C as g,Q as v,p as V,F as y,k as w}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{a as D}from"./export-37598506.js";const F={class:"space-y-6"},H={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},S={class:"flex items-center justify-between mb-6"},z={class:"flex items-center space-x-3"},E=["disabled"],N={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},L={class:"bg-green-50 border border-green-200 rounded-lg p-4"},$={class:"flex items-center"},q={class:"ml-4"},U={class:"text-2xl font-semibold text-green-900"},X={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},I={class:"flex items-center"},G={class:"ml-4"},P={class:"text-2xl font-semibold text-blue-900"},Q={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},Y={class:"flex items-center"},J={class:"ml-4"},K={class:"text-2xl font-semibold text-purple-900"},O={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},Z={class:"flex items-center"},ee={class:"ml-4"},te={class:"text-2xl font-semibold text-yellow-900"},se={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},oe=["value"],ne={key:0,class:"flex items-center justify-center py-12"},re={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-6"},le={class:"flex items-center"},ie={class:"text-red-800"},ae={key:2,class:"overflow-x-auto"},de={class:"min-w-full divide-y divide-gray-200"},ue={class:"bg-white divide-y divide-gray-200"},ce={class:"px-6 py-4 whitespace-nowrap"},pe={class:"text-sm font-medium text-gray-900"},me={class:"text-sm text-gray-500"},xe={class:"px-6 py-4 whitespace-nowrap"},ge={class:"text-sm font-medium text-gray-900"},ve={class:"text-sm text-gray-500"},fe={class:"px-6 py-4 whitespace-nowrap"},he={class:"text-sm font-medium text-gray-900"},be={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ye={class:"px-6 py-4 whitespace-nowrap"},we={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},ke=["onClick"],_e=["onClick"],Ce={key:3,class:"text-center py-12"},Ae=R({__name:"WinnersReport",setup(Me){const d=a(!1),p=a(""),u=a([]),f=a([]),i=a({dateRange:"month",status:"",branch:""}),c=a({totalWinners:0,totalAmount:0,avgWinningBid:0,completedAuctions:0}),m=async()=>{d.value=!0,p.value="";try{const s=await fetch("/api/admin/reports/winners",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const t=await s.json();if(t.success)u.value=t.winners||[],c.value=t.summary||{totalWinners:0,totalAmount:0,avgWinningBid:0,completedAuctions:0};else throw new Error(t.error||"Failed to fetch winners data")}catch(s){p.value=s.message||"Failed to load winners data",console.error("Winners report error:",s)}finally{d.value=!1}},k=async()=>{try{const s=await fetch("/api/admin/reports/branches",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(s.ok){const t=await s.json();t.success&&(f.value=t.branches||[])}}catch(s){console.error("Failed to load branches:",s)}},_=()=>{m()},C=()=>{if(u.value.length===0){alert("No data to export");return}D(u.value,"csv")},M=s=>{console.log("Viewing winner details:",s)},B=s=>{console.log("Downloading invoice for:",s)},x=s=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(s),W=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),A=s=>({completed:"bg-blue-100 text-blue-800",paid:"bg-green-100 text-green-800",delivered:"bg-purple-100 text-purple-800",pending:"bg-yellow-100 text-yellow-800"})[s]||"bg-gray-100 text-gray-800";return T(()=>{m(),k()}),(s,t)=>(r(),l("div",F,[e("div",H,[e("div",S,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Winners Report"),e("p",{class:"text-gray-600 mt-1"},"View auction winners and results")],-1)),e("div",z,[e("button",{onClick:m,disabled:d.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[(r(),l("svg",{class:h(["w-4 h-4 mr-2",{"animate-spin":d.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[3]||(t[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),t[4]||(t[4]=b(" Refresh "))],8,E),e("button",{onClick:C,class:"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),b(" Export ")]))])]),e("div",N,[e("div",L,[e("div",$,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",q,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-green-600"},"Total Winners",-1)),e("p",U,n(c.value.totalWinners),1)])])]),e("div",X,[e("div",I,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",G,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-blue-600"},"Total Amount",-1)),e("p",P,"$"+n(x(c.value.totalAmount)),1)])])]),e("div",Q,[e("div",Y,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",J,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-purple-600"},"Avg Winning Bid",-1)),e("p",K,"$"+n(x(c.value.avgWinningBid)),1)])])]),e("div",O,[e("div",Z,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])],-1)),e("div",ee,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-yellow-600"},"Completed",-1)),e("p",te,n(c.value.completedAuctions),1)])])])]),e("div",se,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),g(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>i.value.dateRange=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},t[15]||(t[15]=[V('<option value="today">Today</option><option value="week">This Week</option><option value="month">This Month</option><option value="quarter">This Quarter</option><option value="year">This Year</option><option value="custom">Custom Range</option>',6)]),512),[[v,i.value.dateRange]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Auction Status",-1)),g(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>i.value.status=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},t[17]||(t[17]=[e("option",{value:""},"All Statuses",-1),e("option",{value:"completed"},"Completed",-1),e("option",{value:"paid"},"Paid",-1),e("option",{value:"delivered"},"Delivered",-1)]),512),[[v,i.value.status]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Branch",-1)),g(e("select",{"onUpdate:modelValue":t[2]||(t[2]=o=>i.value.branch=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[t[19]||(t[19]=e("option",{value:""},"All Branches",-1)),(r(!0),l(y,null,w(f.value,o=>(r(),l("option",{key:o.id,value:o.id},n(o.name),9,oe))),128))],512),[[v,i.value.branch]])]),e("div",{class:"flex items-end"},[e("button",{onClick:_,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Apply Filters ")])]),d.value?(r(),l("div",ne,t[21]||(t[21]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"ml-2 text-gray-600"},"Loading winners data...",-1)]))):p.value?(r(),l("div",re,[e("div",le,[t[22]||(t[22]=e("svg",{class:"h-5 w-5 text-red-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("span",ie,n(p.value),1)])])):u.value.length>0?(r(),l("div",ae,[e("table",de,[t[23]||(t[23]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Auction"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Winner"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Winning Bid"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"End Date"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",ue,[(r(!0),l(y,null,w(u.value,o=>(r(),l("tr",{key:o.id,class:"hover:bg-gray-50"},[e("td",ce,[e("div",pe,n(o.auction_title),1),e("div",me,"#"+n(o.auction_id),1)]),e("td",xe,[e("div",ge,n(o.winner_name),1),e("div",ve,n(o.winner_email),1)]),e("td",fe,[e("div",he,"$"+n(x(o.winning_amount)),1)]),e("td",be,n(W(o.end_date)),1),e("td",ye,[e("span",{class:h([A(o.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(o.status),3)]),e("td",we,[e("button",{onClick:j=>M(o),class:"text-blue-600 hover:text-blue-900 mr-3"}," View ",8,ke),e("button",{onClick:j=>B(o),class:"text-green-600 hover:text-green-900"}," Invoice ",8,_e)])]))),128))])])])):(r(),l("div",Ce,t[24]||(t[24]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No winners found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No auction winners match the current filters.",-1)])))])]))}});export{Ae as default};
