function c(n,o="export.csv"){if(!n||n.length===0){alert("No data to export");return}const e=Object.keys(n[0]),s=[e.join(","),...n.map(t=>e.map(i=>{const r=t[i];return typeof r=="string"&&(r.includes(",")||r.includes('"'))?`"${r.replace(/"/g,'""')}"`:r||""}).join(","))].join(`
`);l(s,o,"text/csv")}function a(n,o="export.json"){if(!n||n.length===0){alert("No data to export");return}const e=JSON.stringify(n,null,2);l(e,o,"application/json")}function l(n,o,e){const s=new Blob([n],{type:e}),t=window.URL.createObjectURL(s),i=document.createElement("a");i.href=t,i.download=o,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(t)}function p(n){return n.map(o=>{const e={};for(const[s,t]of Object.entries(o))typeof t=="object"&&t!==null&&!(t instanceof Date)||(t instanceof Date?e[s]=t.toISOString().split("T")[0]:typeof t=="number"&&(s.includes("amount")||s.includes("price")||s.includes("total"))?e[s]=t.toFixed(2):e[s]=t);return e})}function f(n,o="csv"){const e=p(n),t=`winners-report-${new Date().toISOString().split("T")[0]}.${o}`;o==="csv"?c(e,t):a(e,t)}function m(n,o="csv"){const e=p(n),t=`sales-report-${new Date().toISOString().split("T")[0]}.${o}`;o==="csv"?c(e,t):a(e,t)}function u(n,o="csv"){const e=p(n),t=`inventory-report-${new Date().toISOString().split("T")[0]}.${o}`;o==="csv"?c(e,t):a(e,t)}function d(n,o="csv"){const e=p(n),t=`refunds-report-${new Date().toISOString().split("T")[0]}.${o}`;o==="csv"?c(e,t):a(e,t)}function v(n,o="csv"){const e=p(n),t=`deposits-report-${new Date().toISOString().split("T")[0]}.${o}`;o==="csv"?c(e,t):a(e,t)}export{f as a,u as b,d as c,v as d,m as e};
