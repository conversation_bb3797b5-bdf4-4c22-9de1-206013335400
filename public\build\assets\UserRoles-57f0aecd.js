import{h as U,r,L as I,o as g,m as G,x as n,u as l,f as A,k as T,g as t,t as m,v as u,H as y,_ as w,F as q}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as J,e as k}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as B}from"./app-admin-1baa1658.js";import{_ as K}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as O}from"./useNotifications-98e2c61c.js";const Q={class:"px-6 py-4"},W={class:"text-sm font-medium text-gray-900"},X=["href"],Y={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Z={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ae={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},se={class:"flex justify-center space-x-2"},ue=U({__name:"UserRoles",setup(oe){const v=J(),{showNotification:d}=O(),f=r(!1),x=r(null),i=r([]),p=r(1),b=r(20),C=r(0),S=r(1),_=r([]),$=r({search:""}),R=[{key:"name",label:"Role Name",sortable:!0},{key:"guard_name",label:"Guard",sortable:!0},{key:"permissions",label:"Permissions",sortable:!1},{key:"users_count",label:"Users",sortable:!0},{key:"created_at",label:"Created",sortable:!0}],c=async()=>{var e,s,h;f.value=!0;try{const a={page:p.value.toString(),per_page:b.value.toString(),...$.value},o=await B.get("/admin/roles",a);_.value=o.data,C.value=((e=o.meta)==null?void 0:e.total)||0,S.value=((s=o.meta)==null?void 0:s.last_page)||1,p.value=((h=o.meta)==null?void 0:h.current_page)||1}catch(a){x.value="Failed to fetch roles",console.error("Error fetching roles:",a)}finally{f.value=!1}},D=()=>{p.value=1,c()},M=()=>{v.push("/admin-spa/roles/create")},N=e=>{$.value.search=e,D()},z=(e,s)=>{console.log("Sort:",e,s)},F=e=>{p.value=e,c()},P=e=>{i.value=e?_.value.map(s=>s.id.toString()):[]},j=e=>{v.push(`/admin-spa/roles/edit/${e.id}`)},L=e=>{v.push(`/admin-spa/roles/view/${e.id}`)},V=async e=>{if(e.name==="Super Admin"){d("Cannot delete Super Admin role","error");return}if(confirm("Are you sure you want to delete this role?"))try{await B.delete(`/admin/roles/${e.id}`),d("Role deleted successfully","success"),await c()}catch{d("Failed to delete role","error")}},H=async()=>{if(i.value.length!==0&&confirm(`Are you sure you want to delete ${i.value.length} roles?`))try{d(`${i.value.length} roles deleted successfully`,"success"),i.value=[],await c()}catch{d("Failed to delete roles","error")}},E=e=>e?new Date(e).toLocaleDateString():"-";return I(()=>{c()}),(e,s)=>(g(),G(l(K),{title:"User Roles",subtitle:"Manage user roles and permissions",loading:f.value,error:x.value,items:_.value,columns:R,"selected-items":i.value,"show-bulk-actions":!0,"current-page":p.value,"total-pages":S.value,"total-items":C.value,"per-page":b.value,"create-button-text":"Add Role","empty-state-title":"No roles found","empty-state-message":"Start by creating a new user role.",onCreate:M,onSearch:N,onSort:z,onPageChange:F,onSelectAll:P,onBulkDelete:H,onRefresh:c},{rows:n(({items:h})=>[(g(!0),A(q,null,T(h,a=>(g(),A("tr",{key:a.id,class:"hover:bg-gray-50"},[t("td",Q,[t("div",W,[t("a",{href:`/admin-spa/roles/view/${a.id}`,class:"hover:text-blue-600"},m(a.name||"-"),9,X)])]),t("td",Y,[u(l(k),{variant:"secondary"},{default:n(()=>[y(m(a.guard_name||"web"),1)]),_:2},1024)]),t("td",Z,[u(l(k),{variant:"info"},{default:n(()=>{var o;return[y(m(((o=a.permissions)==null?void 0:o.length)||0)+" permissions ",1)]}),_:2},1024)]),t("td",ee,[u(l(k),{variant:"success"},{default:n(()=>[y(m(a.users_count||0)+" users ",1)]),_:2},1024)]),t("td",te,m(E(a.created_at)),1),t("td",ae,[t("div",se,[u(l(w),{size:"sm",variant:"outline",onClick:o=>j(a)},{default:n(()=>s[0]||(s[0]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[0]},1032,["onClick"]),u(l(w),{size:"sm",variant:"info",onClick:o=>L(a)},{default:n(()=>s[1]||(s[1]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[1]},1032,["onClick"]),u(l(w),{size:"sm",variant:"danger",onClick:o=>V(a),disabled:a.name==="Super Admin"},{default:n(()=>s[2]||(s[2]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[2]},1032,["onClick","disabled"])])])]))),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{ue as default};
