import{s as x,r as p,i as h}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{a as g}from"./axios-917b1704.js";const q=x("adminAuctionTypes",()=>{const i=p([]),n=p(null),d=p(null),v=p(!1),s=p(null),m=p(!1),A=p(!1),y=p(!1),b=h(()=>{var e;return((e=n.value)==null?void 0:e.total)||0}),S=h(()=>{var e;return((e=n.value)==null?void 0:e.current_page)||1}),E=h(()=>{var e;return((e=n.value)==null?void 0:e.last_page)||1}),F=h(()=>S.value<E.value),$=h(()=>{var e;return((e=n.value)==null?void 0:e.data)||i.value});return{auctionTypes:i,paginatedAuctionTypes:n,currentAuctionType:d,loading:v,error:s,creating:m,updating:A,deleting:y,totalAuctionTypes:b,currentPage:S,lastPage:E,hasMorePages:F,auctionTypesList:$,fetchAuctionTypes:async(e={})=>{var o,r;v.value=!0,s.value=null;try{const a=new URLSearchParams;e.search&&a.append("search",e.search),e.type&&a.append("type",e.type),e.is_active!==void 0&&a.append("is_active",e.is_active.toString()),e.page&&a.append("page",e.page.toString()),e.per_page&&a.append("per_page",e.per_page.toString()),e.sort_by&&a.append("sort_by",e.sort_by),e.sort_order&&a.append("sort_order",e.sort_order);const t=await g.get(`/api/auction-types?${a.toString()}`);e.page||e.per_page?n.value=t.data:i.value=t.data.data||t.data}catch(a){s.value=((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to fetch auction types",console.error("Error fetching auction types:",a)}finally{v.value=!1}},fetchAuctionType:async e=>{var o,r;v.value=!0,s.value=null;try{const a=await g.get(`/api/auction-types/${e}`);return d.value=a.data.data,a.data.data}catch(a){throw s.value=((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to fetch auction type",console.error("Error fetching auction type:",a),a}finally{v.value=!1}},createAuctionType:async e=>{var o,r;m.value=!0,s.value=null;try{const a=new FormData;Object.entries(e).forEach(([T,u])=>{T==="media"&&Array.isArray(u)?u.forEach((f,l)=>{a.append(`media[${l}]`,f)}):T==="items"&&Array.isArray(u)?u.forEach((f,l)=>{a.append(`items[${l}]`,f.toString())}):u!=null&&a.append(T,u.toString())});const t=await g.post("/api/auction-types",a,{headers:{"Content-Type":"multipart/form-data"}});return i.value.unshift(t.data.data),n.value&&(n.value.data.unshift(t.data.data),n.value.total+=1),t.data.data}catch(a){throw s.value=((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to create auction type",console.error("Error creating auction type:",a),a}finally{m.value=!1}},updateAuctionType:async e=>{var o,r,a;A.value=!0,s.value=null;try{const t=new FormData;t.append("_method","PUT"),Object.entries(e).forEach(([l,c])=>{l==="media"&&Array.isArray(c)?c.forEach((_,w)=>{t.append(`media[${w}]`,_)}):l==="items"&&Array.isArray(c)?c.forEach((_,w)=>{t.append(`items[${w}]`,_.toString())}):c!=null&&l!=="id"&&t.append(l,c.toString())});const u=(await g.post(`/api/auction-types/${e.id}`,t,{headers:{"Content-Type":"multipart/form-data"}})).data.data,f=i.value.findIndex(l=>l.id===e.id);if(f!==-1&&(i.value[f]=u),n.value){const l=n.value.data.findIndex(c=>c.id===e.id);l!==-1&&(n.value.data[l]=u)}return((o=d.value)==null?void 0:o.id)===e.id&&(d.value=u),u}catch(t){throw s.value=((a=(r=t.response)==null?void 0:r.data)==null?void 0:a.message)||"Failed to update auction type",console.error("Error updating auction type:",t),t}finally{A.value=!1}},deleteAuctionType:async e=>{var o,r,a;y.value=!0,s.value=null;try{await g.delete(`/api/auction-types/${e}`),i.value=i.value.filter(t=>t.id!==e),n.value&&(n.value.data=n.value.data.filter(t=>t.id!==e),n.value.total-=1),((o=d.value)==null?void 0:o.id)===e&&(d.value=null)}catch(t){throw s.value=((a=(r=t.response)==null?void 0:r.data)==null?void 0:a.message)||"Failed to delete auction type",console.error("Error deleting auction type:",t),t}finally{y.value=!1}},bulkDeleteAuctionTypes:async e=>{var o,r;y.value=!0,s.value=null;try{await g.post("/api/auction-types/bulk-delete",{auction_type_ids:e.map(t=>parseInt(t))});const a=e.map(t=>parseInt(t));i.value=i.value.filter(t=>!a.includes(t.id)),n.value&&(n.value.data=n.value.data.filter(t=>!a.includes(t.id)),n.value.total-=e.length)}catch(a){throw s.value=((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to delete auction types",console.error("Error bulk deleting auction types:",a),a}finally{y.value=!1}},getAuctionTypeOptions:()=>i.value.map(e=>({label:e.name,value:e.id.toString()})),resetState:()=>{i.value=[],n.value=null,d.value=null,s.value=null,v.value=!1,m.value=!1,A.value=!1,y.value=!1}}});export{q as u};
