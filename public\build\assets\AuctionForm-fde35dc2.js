import{h as C,r as u,i as w,L as q,o as k,m as R,x,u as o,g as l,v as s,b as i,C as d,D as U,G as b,f as F,j as I}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as L,f as M,h as P}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{A as N}from"./AdminFormTemplate-3cd16ecb.js";const O={class:"space-y-6"},$={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},j={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},G={class:"space-y-6"},z={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},H={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},J={class:"flex items-center"},K={class:"flex items-center"},Q={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},W={class:"space-y-6"},X={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Z={class:"flex items-center"},h={class:"flex items-center"},oe=C({__name:"AuctionForm",setup(_){const y=L(),V=M(),g=u(!1),c=u(!1),f=u(!1),m=u(null),v=u([]),t=u({title:"",description:"",type:"",status:"draft",startDate:"",endDate:"",previewStartDate:"",registrationDeadline:"",startingBid:0,bidIncrement:1,reservePrice:0,allowProxyBids:!0,autoExtend:!1,extensionTime:5,extensionTrigger:2,requireRegistration:!1,isPublic:!0,terms:""}),S=[{value:"live",label:"Live Auction"},{value:"timed",label:"Timed Auction"},{value:"silent",label:"Silent Auction"},{value:"sealed",label:"Sealed Bid"}],T=[{value:"draft",label:"Draft"},{value:"scheduled",label:"Scheduled"},{value:"active",label:"Active"},{value:"paused",label:"Paused"},{value:"ended",label:"Ended"},{value:"cancelled",label:"Cancelled"}],p=w(()=>!!V.params.id),D=w(()=>t.value.title&&t.value.type&&t.value.startDate&&t.value.endDate),n=r=>v.value.find(e=>e.includes(r)),A=async()=>{c.value=!0,v.value=[];try{if(!D.value){v.value.push("Please fill in all required fields");return}console.log("Saving auction:",t.value),await new Promise(r=>setTimeout(r,1e3)),y.push("/admin-spa/auctions")}catch{m.value="Failed to save auction"}finally{c.value=!1}},B=async()=>{f.value=!0;try{console.log("Saving draft:",t.value),await new Promise(r=>setTimeout(r,500))}catch{m.value="Failed to save draft"}finally{f.value=!1}},E=()=>{y.push("/admin-spa/auctions")};return q(async()=>{if(p.value){g.value=!0;try{const r=V.params.id;console.log("Loading auction:",r),await new Promise(e=>setTimeout(e,500))}catch{m.value="Failed to load auction"}finally{g.value=!1}}}),(r,e)=>(k(),R(o(N),{title:p.value?"Edit Auction":"Create Auction",subtitle:p.value?"Update auction details and settings":"Set up a new auction with items and bidding rules",loading:g.value,error:m.value,saving:c.value,"is-valid":D.value,"validation-errors":v.value,"save-button-text":p.value?"Update Auction":"Create Auction","show-draft":!0,"saving-draft":f.value,"cancel-route":"/admin-spa/auctions",onSave:A,onSaveDraft:B,onCancel:E},{"section-basic":x(()=>[l("div",O,[l("div",$,[l("div",null,[e[18]||(e[18]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Auction Title * ",-1)),s(o(i),{modelValue:t.value.title,"onUpdate:modelValue":e[0]||(e[0]=a=>t.value.title=a),placeholder:"Enter auction title",error:n("title"),required:""},null,8,["modelValue","error"])]),l("div",null,[e[19]||(e[19]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Auction Type * ",-1)),s(o(P),{modelValue:t.value.type,"onUpdate:modelValue":e[1]||(e[1]=a=>t.value.type=a),options:S,placeholder:"Select auction type",error:n("type"),required:""},null,8,["modelValue","error"])])]),l("div",null,[e[20]||(e[20]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Description ",-1)),d(l("textarea",{"onUpdate:modelValue":e[2]||(e[2]=a=>t.value.description=a),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Describe the auction..."},null,512),[[U,t.value.description]])]),l("div",j,[l("div",null,[e[21]||(e[21]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Start Date * ",-1)),s(o(i),{modelValue:t.value.startDate,"onUpdate:modelValue":e[3]||(e[3]=a=>t.value.startDate=a),type:"datetime-local",error:n("startDate"),required:""},null,8,["modelValue","error"])]),l("div",null,[e[22]||(e[22]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," End Date * ",-1)),s(o(i),{modelValue:t.value.endDate,"onUpdate:modelValue":e[4]||(e[4]=a=>t.value.endDate=a),type:"datetime-local",error:n("endDate"),required:""},null,8,["modelValue","error"])]),l("div",null,[e[23]||(e[23]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Status ",-1)),s(o(P),{modelValue:t.value.status,"onUpdate:modelValue":e[5]||(e[5]=a=>t.value.status=a),options:T,placeholder:"Select status"},null,8,["modelValue"])])])])]),"section-bidding":x(()=>[l("div",G,[l("div",z,[l("div",null,[e[24]||(e[24]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Starting Bid ",-1)),s(o(i),{modelValue:t.value.startingBid,"onUpdate:modelValue":e[6]||(e[6]=a=>t.value.startingBid=a),type:"number",step:"0.01",placeholder:"0.00",error:n("startingBid")},null,8,["modelValue","error"])]),l("div",null,[e[25]||(e[25]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Bid Increment ",-1)),s(o(i),{modelValue:t.value.bidIncrement,"onUpdate:modelValue":e[7]||(e[7]=a=>t.value.bidIncrement=a),type:"number",step:"0.01",placeholder:"1.00",error:n("bidIncrement")},null,8,["modelValue","error"])]),l("div",null,[e[26]||(e[26]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Reserve Price ",-1)),s(o(i),{modelValue:t.value.reservePrice,"onUpdate:modelValue":e[8]||(e[8]=a=>t.value.reservePrice=a),type:"number",step:"0.01",placeholder:"0.00",error:n("reservePrice")},null,8,["modelValue","error"])])]),l("div",H,[l("div",null,[l("label",J,[d(l("input",{"onUpdate:modelValue":e[9]||(e[9]=a=>t.value.allowProxyBids=a),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[b,t.value.allowProxyBids]]),e[27]||(e[27]=l("span",{class:"ml-2 text-sm text-gray-700"},"Allow Proxy Bidding",-1))])]),l("div",null,[l("label",K,[d(l("input",{"onUpdate:modelValue":e[10]||(e[10]=a=>t.value.autoExtend=a),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[b,t.value.autoExtend]]),e[28]||(e[28]=l("span",{class:"ml-2 text-sm text-gray-700"},"Auto-extend on Late Bids",-1))])])]),t.value.autoExtend?(k(),F("div",Q,[l("div",null,[e[29]||(e[29]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Extension Time (minutes) ",-1)),s(o(i),{modelValue:t.value.extensionTime,"onUpdate:modelValue":e[11]||(e[11]=a=>t.value.extensionTime=a),type:"number",placeholder:"5"},null,8,["modelValue"])]),l("div",null,[e[30]||(e[30]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Extension Trigger (minutes before end) ",-1)),s(o(i),{modelValue:t.value.extensionTrigger,"onUpdate:modelValue":e[12]||(e[12]=a=>t.value.extensionTrigger=a),type:"number",placeholder:"2"},null,8,["modelValue"])])])):I("",!0)])]),"section-settings":x(()=>[l("div",W,[l("div",X,[l("div",null,[e[31]||(e[31]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Preview Start Date ",-1)),s(o(i),{modelValue:t.value.previewStartDate,"onUpdate:modelValue":e[13]||(e[13]=a=>t.value.previewStartDate=a),type:"datetime-local"},null,8,["modelValue"])]),l("div",null,[e[32]||(e[32]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Registration Deadline ",-1)),s(o(i),{modelValue:t.value.registrationDeadline,"onUpdate:modelValue":e[14]||(e[14]=a=>t.value.registrationDeadline=a),type:"datetime-local"},null,8,["modelValue"])])]),l("div",Y,[l("div",null,[l("label",Z,[d(l("input",{"onUpdate:modelValue":e[15]||(e[15]=a=>t.value.requireRegistration=a),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[b,t.value.requireRegistration]]),e[33]||(e[33]=l("span",{class:"ml-2 text-sm text-gray-700"},"Require Registration",-1))])]),l("div",null,[l("label",h,[d(l("input",{"onUpdate:modelValue":e[16]||(e[16]=a=>t.value.isPublic=a),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[b,t.value.isPublic]]),e[34]||(e[34]=l("span",{class:"ml-2 text-sm text-gray-700"},"Public Auction",-1))])])]),l("div",null,[e[35]||(e[35]=l("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Terms and Conditions ",-1)),d(l("textarea",{"onUpdate:modelValue":e[17]||(e[17]=a=>t.value.terms=a),rows:"6",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter auction terms and conditions..."},null,512),[[U,t.value.terms]])])])]),_:1},8,["title","subtitle","loading","error","saving","is-valid","validation-errors","save-button-text","saving-draft"]))}});export{oe as default};
