import{h as re,r as S,R as ie,i as p,L as ue,o as d,f as v,v as u,x as n,u as r,m as L,j as b,F as M,k as ce,g as a,t as i,H as c,_ as f}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as de,h as I}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{_ as me}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{A as pe}from"./AdminModal-15ebbba8.js";import{A as G}from"./AdminBadge-74cb3994.js";import{u as _e}from"./items-578e557a.js";import{u as ve}from"./auctionTypes-eeea8f1c.js";import{u as ge}from"./branches-1476f76c.js";import{u as ye}from"./useNotifications-98e2c61c.js";import"./axios-917b1704.js";const fe={class:"px-6 py-4"},he=["checked","onChange"],xe={class:"px-6 py-4"},be={class:"flex-shrink-0"},ke=["src","alt","onClick"],we={class:"px-6 py-4"},Ae={class:"min-w-0 flex-1"},Ce={class:"text-sm font-medium text-gray-900 truncate"},Se={class:"text-sm text-gray-500 truncate"},Ie={class:"text-xs text-gray-400 truncate"},$e={class:"px-6 py-4"},Ve={key:0,class:"text-sm"},Ne={class:"font-medium text-gray-900"},De={class:"text-gray-500"},Be={key:1,class:"text-sm text-gray-400"},Te={class:"px-6 py-4"},Pe={key:1,class:"text-sm text-gray-400"},ze={class:"px-6 py-4"},Fe={class:"text-sm"},je={class:"font-medium text-gray-900"},Ue={class:"text-gray-600"},Ee={class:"px-6 py-4"},Oe={class:"px-6 py-4"},Re={class:"text-xs text-gray-500"},Le={key:0},Me={key:1},Ge={key:2},He={class:"px-6 py-4"},qe={class:"flex justify-end gap-2"},Je={class:"text-lg font-medium"},Ke={class:"space-y-4"},Qe=["src","alt"],We={class:"text-sm text-gray-600"},Xe={key:0},Ye={class:"flex justify-end space-x-3"},dt=re({__name:"ItemsList",setup(Ze){const m=_e(),$=ve(),V=ge(),k=de(),{showNotification:x}=ye(),l=S([]),w=S(!1),_=S(null),y=ie({status:"",auction_type_id:"",branch_id:"",search:""}),N=p(()=>{var e;return((e=m.items)==null?void 0:e.data)||[]}),H=p(()=>m.loading),q=p(()=>m.error),D=p(()=>{var e;return((e=m.items)==null?void 0:e.current_page)||1}),J=p(()=>{var e;return((e=m.items)==null?void 0:e.last_page)||1}),K=p(()=>{var e;return((e=m.items)==null?void 0:e.total)||0}),B=p(()=>{var e;return((e=m.items)==null?void 0:e.per_page)||20}),Q=p(()=>[{key:"image",label:"Image",sortable:!1},{key:"name",label:"Item Details",sortable:!0},{key:"owner",label:"Owner",sortable:!1},{key:"auction_type",label:"Auction Type",sortable:!1},{key:"amounts",label:"Amounts",sortable:!1},{key:"status",label:"Status",sortable:!1},{key:"dates",label:"Dates",sortable:!1}]),W=p(()=>[{label:"All Statuses",value:""},{label:"Available",value:"available"},{label:"Sold",value:"sold"}]),X=p(()=>[{label:"All Types",value:""},...$.auctionTypes.map(e=>({label:e.name,value:e.id.toString()}))]),Y=p(()=>[{label:"All Branches",value:""},...V.branches.map(e=>({label:e.name,value:e.id.toString()}))]),g=async()=>{await m.fetchItems({...y,page:D.value,per_page:B.value})},A=()=>{g()},Z=()=>{k.push("/admin-spa/items/create")},T=e=>{k.push(`/admin-spa/items/view/${e.id}`)},C=e=>{k.push(`/admin-spa/items/edit/${e.id}`)},P=async e=>{if(confirm(`Are you sure you want to delete "${e.name}"?`))try{await m.deleteItem(e.id),x("Item deleted successfully","success"),await g()}catch{x("Failed to delete item","error")}},z=async()=>{if(l.value.length!==0&&confirm(`Are you sure you want to delete ${l.value.length} items?`))try{await m.bulkDeleteItems(l.value),x(`${l.value.length} items deleted successfully`,"success"),l.value=[],await g()}catch{x("Failed to delete items","error")}},F=async e=>{if(l.value.length!==0)try{await m.bulkUpdateItemStatus(l.value,e);const t=e==="close"?"sold":"available";x(`${l.value.length} items marked as ${t}`,"success"),l.value=[],await g()}catch{x("Failed to update item status","error")}},ee=e=>{y.search=e,g()},te=(e,t)=>{g()},se=e=>{g()},ae=e=>{e?l.value=N.value.map(t=>t.id.toString()):l.value=[]},oe=e=>{const t=e.toString(),o=l.value.indexOf(t);o>-1?l.value.splice(o,1):l.value.push(t)},le=e=>{_.value=e,w.value=!0},j=()=>{w.value=!1,_.value=null},ne=e=>{switch(e){case"live":return"success";case"online":return"primary";case"cash":return"warning";default:return"secondary"}},U=e=>e?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e):"$0.00",E=e=>e?new Date(e).toLocaleDateString():"N/A";return ue(async()=>{await Promise.all([g(),$.fetchAuctionTypes(),V.fetchBranches()])}),(e,t)=>(d(),v(M,null,[u(r(me),{title:"All Items",subtitle:"View and manage all auction items",loading:H.value,error:q.value,items:N.value,columns:Q.value,"selected-items":l.value,"show-bulk-actions":!0,"current-page":D.value,"total-pages":J.value,"total-items":K.value,"per-page":B.value,"create-button-text":"Add Item","empty-state-title":"No items found","empty-state-message":"Get started by adding your first auction item.",onCreate:Z,onSearch:ee,onSort:te,onPageChange:se,onSelectAll:ae,onBulkDelete:z,onView:T,onEdit:C,onDelete:P,onRefresh:g},{filters:n(()=>[u(r(I),{modelValue:y.status,"onUpdate:modelValue":t[0]||(t[0]=o=>y.status=o),placeholder:"All Statuses",options:W.value,onChange:A},null,8,["modelValue","options"]),u(r(I),{modelValue:y.auction_type_id,"onUpdate:modelValue":t[1]||(t[1]=o=>y.auction_type_id=o),placeholder:"All Auction Types",options:X.value,onChange:A},null,8,["modelValue","options"]),u(r(I),{modelValue:y.branch_id,"onUpdate:modelValue":t[2]||(t[2]=o=>y.branch_id=o),placeholder:"All Branches",options:Y.value,onChange:A},null,8,["modelValue","options"])]),rows:n(({items:o})=>[(d(!0),v(M,null,ce(o,s=>(d(),v("tr",{key:s.id,class:"hover:bg-gray-50"},[a("td",fe,[a("input",{type:"checkbox",checked:l.value.includes(s.id.toString()),onChange:h=>oe(s.id),class:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,40,he)]),a("td",xe,[a("div",be,[a("img",{src:s.image||"/img/product.jpeg",alt:s.name,class:"w-12 h-12 rounded-lg object-cover cursor-pointer hover:opacity-75 transition-opacity",onClick:h=>le(s)},null,8,ke)])]),a("td",we,[a("div",Ae,[a("p",Ce,i(s.name),1),a("p",Se," Ref: "+i(s.reference_number||"N/A"),1),a("p",Ie," Code: "+i(s.code||"N/A"),1)])]),a("td",$e,[s.user?(d(),v("div",Ve,[a("p",Ne,i(s.user.name),1),a("p",De,i(s.user.email),1)])):(d(),v("span",Be,"No owner"))]),a("td",Te,[s.auction_type?(d(),L(r(G),{key:0,variant:ne(s.auction_type.type),size:"sm"},{default:n(()=>[c(i(s.auction_type.name),1)]),_:2},1032,["variant"])):(d(),v("span",Pe,"No type"))]),a("td",ze,[a("div",Fe,[a("p",je," Target: "+i(U(s.target_amount)),1),a("p",Ue," Current: "+i(U(s.bid_amount)),1)])]),a("td",Ee,[u(r(G),{variant:s.closed_by?"success":"warning",size:"sm"},{default:n(()=>[c(i(s.closed_by?"Sold":"Available"),1)]),_:2},1032,["variant"])]),a("td",Oe,[a("div",Re,[s.date_from?(d(),v("p",Le," From: "+i(E(s.date_from)),1)):b("",!0),s.date_to?(d(),v("p",Me," To: "+i(E(s.date_to)),1)):b("",!0),!s.date_from&&!s.date_to?(d(),v("p",Ge," No dates set ")):b("",!0)])]),a("td",He,[a("div",qe,[u(r(f),{variant:"ghost",size:"sm",onClick:h=>T(s),class:"text-blue-600 hover:text-blue-700"},{default:n(()=>t[6]||(t[6]=[c(" View ")])),_:2,__:[6]},1032,["onClick"]),u(r(f),{variant:"ghost",size:"sm",onClick:h=>C(s),class:"text-green-600 hover:text-green-700"},{default:n(()=>t[7]||(t[7]=[c(" Edit ")])),_:2,__:[7]},1032,["onClick"]),u(r(f),{variant:"ghost",size:"sm",onClick:h=>P(s),class:"text-red-600 hover:text-red-700",disabled:s==null?void 0:s.closed_by},{default:n(()=>t[8]||(t[8]=[c(" Delete ")])),_:2,__:[8]},1032,["onClick","disabled"])])])]))),128))]),"bulk-actions":n(({selectedItems:o})=>[u(r(f),{variant:"outline",size:"sm",onClick:t[3]||(t[3]=s=>F("close")),class:"text-green-600 hover:text-green-700"},{default:n(()=>t[9]||(t[9]=[c(" Mark as Sold ")])),_:1,__:[9]}),u(r(f),{variant:"outline",size:"sm",onClick:t[4]||(t[4]=s=>F("open")),class:"text-blue-600 hover:text-blue-700"},{default:n(()=>t[10]||(t[10]=[c(" Mark as Available ")])),_:1,__:[10]}),u(r(f),{variant:"outline",size:"sm",onClick:z,class:"text-red-600 hover:text-red-700"},{default:n(()=>t[11]||(t[11]=[c(" Delete Selected ")])),_:1,__:[11]})]),_:1},8,["loading","error","items","columns","selected-items","current-page","total-pages","total-items","per-page"]),w.value?(d(),L(r(pe),{key:0,onClose:j},{header:n(()=>{var o;return[a("h3",Je,i((o=_.value)==null?void 0:o.name),1)]}),footer:n(()=>[a("div",Ye,[u(r(f),{variant:"outline",onClick:j},{default:n(()=>t[15]||(t[15]=[c("Close")])),_:1,__:[15]}),u(r(f),{variant:"primary",onClick:t[5]||(t[5]=o=>C(_.value))},{default:n(()=>t[16]||(t[16]=[c("Edit Item")])),_:1,__:[16]})])]),default:n(()=>{var o,s,h,O,R;return[a("div",Ke,[a("img",{src:((o=_.value)==null?void 0:o.image)||"/img/product.jpeg",alt:(s=_.value)==null?void 0:s.name,class:"w-full max-h-96 object-contain rounded-lg"},null,8,Qe),a("div",We,[a("p",null,[t[12]||(t[12]=a("strong",null,"Reference:",-1)),c(" "+i(((h=_.value)==null?void 0:h.reference_number)||"N/A"),1)]),a("p",null,[t[13]||(t[13]=a("strong",null,"Code:",-1)),c(" "+i(((O=_.value)==null?void 0:O.code)||"N/A"),1)]),(R=_.value)!=null&&R.description?(d(),v("p",Xe,[t[14]||(t[14]=a("strong",null,"Description:",-1)),c(" "+i(_.value.description),1)])):b("",!0)])])]}),_:1})):b("",!0)],64))}});export{dt as default};
