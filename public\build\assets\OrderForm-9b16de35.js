import{h as Y,i as S,r as m,w as Z,L as tt,o as n,f as l,g as t,t as i,u as p,v as R,x as T,j as F,F as M,k as j,C as E,Q as et,D as H,H as y,B as st,_ as U}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{f as ot,b as at}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{a as _}from"./axios-917b1704.js";import{u as rt}from"./useApi-951689df.js";import"./app-admin-1baa1658.js";const nt={class:"space-y-6"},lt={class:"bg-white shadow rounded-lg"},it={class:"px-4 py-5 sm:p-6"},dt={class:"flex items-center justify-between"},ut={class:"text-2xl font-bold text-gray-900"},ct={class:"mt-1 text-sm text-gray-500"},mt={class:"flex items-center space-x-3"},pt={key:0,class:"flex justify-center py-12"},gt={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},vt={class:"flex"},ft={class:"ml-3"},_t={class:"mt-2 text-sm text-red-700"},xt={class:"bg-white shadow rounded-lg"},bt={class:"px-4 py-5 sm:p-6"},yt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},ht={class:"lg:col-span-2"},wt={class:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6"},kt=["value"],Ct=["value"],St={class:"overflow-x-auto",style:{"min-height":"300px"}},Mt={class:"min-w-full divide-y divide-gray-200"},jt={class:"bg-white divide-y divide-gray-200"},Et={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"text-lg font-semibold text-gray-900"},Nt={class:"px-6 py-4 whitespace-nowrap"},qt=["onUpdate:modelValue","max"],It={class:"px-6 py-4 whitespace-nowrap"},$t={class:"text-lg font-semibold text-gray-900"},Bt={class:"px-6 py-4 whitespace-nowrap text-right"},At={class:"text-lg font-semibold text-gray-900"},Dt={class:"px-6 py-4 whitespace-nowrap text-center"},Rt=["onClick"],Tt={class:"lg:col-span-1"},Ft={class:"border rounded-lg p-4 bg-gray-50"},Ht={class:"space-y-3"},Ut={class:"flex justify-between"},Lt={class:"text-lg font-semibold text-gray-900"},Pt={class:"flex justify-between"},zt={class:"text-lg font-semibold text-gray-900"},Wt={class:"flex justify-between"},Xt={class:"text-lg font-semibold text-gray-900"},Gt={class:"space-y-2"},Jt={class:"mt-6 space-y-3"},Ot=["disabled"],Qt={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Kt={key:1,class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ae=Y({__name:"OrderForm",setup(Yt){const L=ot(),V=at(),h=S(()=>L.params.id),u=S(()=>!!h.value),{data:N,isLoading:w,error:P,execute:z}=rt(`/admin/orders/${h.value}`),q=S(()=>{var s;return((s=P.value)==null?void 0:s.message)||null}),o=m({user_id:"2",branch_id:"",description:"",discount:0,vat:0,sub_total:0,amount_total:0,amount_paid:0,sales:[]}),x=m(!1),k=m([]),I=m([]),b=m([]),g=m(!1),W=()=>{V.push("/admin-spa/sales/orders")},X=async()=>{try{const s=await _.get("/api/admin/users");k.value=[{id:2,name:"Walk-in Customer",email:"<EMAIL>"},...s.data.data]}catch(s){console.error("Error loading customers:",s),k.value=[{id:2,name:"Walk-in Customer",email:"<EMAIL>"}]}},G=async()=>{try{const s=await _.get("/ajax-branches");I.value=s.data}catch(s){console.error("Error loading branches:",s),I.value=[]}},J=async()=>{try{const s=await _.get("/ajax-items");b.value=s.data}catch(s){console.error("Error loading items:",s),b.value=[]}},O=s=>{var c,D;const e=s.target,d=e.value;if(!d)return;if(o.value.sales.find(f=>f.item_id==d)){e.value="";return}const r=b.value.find(f=>f.id==d);if(r){const f=((D=(c=window.user)==null?void 0:c.getGlobal)==null?void 0:D.call(c,"vat"))||0,K={item:r,item_id:r.id,quantity:1,selling_price:r.target_amount||0,discount:0,vat:r.vat_applied?f:0};o.value.sales.push(K),e.value="",C()}},Q=s=>{o.value.sales.splice(s,1),C()},C=()=>{x.value=!1},$=()=>{o.value.sub_total=0,o.value.amount_total=0,o.value.discount=0;for(const s of o.value.sales){const e=s.quantity*s.selling_price;o.value.discount+=Number(s.discount||0),o.value.sub_total+=e}o.value.amount_total=Number((o.value.sub_total-o.value.discount).toFixed(2)),o.value.sub_total=Number(o.value.sub_total.toFixed(2)),o.value.discount=Number(o.value.discount.toFixed(2)),o.value.amount_paid=o.value.amount_total,x.value=!0},B=async()=>{var s;if(!x.value){alert("Please calculate the order totals first");return}g.value=!0;try{const e=new FormData;e.append("order",JSON.stringify({...o.value,sales:o.value.sales})),e.append("sub_total",o.value.sub_total.toString()),e.append("amount_total",o.value.amount_total.toString()),e.append("amount_paid",o.value.amount_paid.toString()),e.append("discount",o.value.discount.toString()),e.append("vat",o.value.vat.toString()),e.append("user_id",o.value.user_id.toString()),o.value.sales.forEach((a,r)=>{e.append(`discounts[${r}]`,(a.discount||0).toString())});const d=(s=document.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content");d&&e.append("_token",d),u.value?(e.append("_method","PUT"),await _.post(`/orders/${h.value}`,e,{headers:{"Content-Type":"multipart/form-data","X-Requested-With":"XMLHttpRequest"}})):await _.post("/orders",e,{headers:{"Content-Type":"multipart/form-data","X-Requested-With":"XMLHttpRequest"}}),V.push("/admin-spa/sales/orders")}catch(e){if(console.error("Error saving order:",e),e.response&&e.response.data&&e.response.data.errors){const d=e.response.data.errors;let a=`Validation errors:
`;Object.keys(d).forEach(r=>{a+=`${r}: ${d[r].join(", ")}
`}),alert(a)}else e.response&&e.response.data&&e.response.data.message?alert(`Error: ${e.response.data.message}`):alert("Error saving order. Please try again.")}finally{g.value=!1}},v=s=>s.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,"),A=s=>{let e=s.name||"";return s.reference_number&&(e+=" ~ "+s.reference_number),e};return Z(N,s=>{s&&u.value&&(o.value={user_id:s.user_id||s.customer_id,branch_id:s.branch_id,description:s.description,discount:s.discount,vat:s.vat,sub_total:s.sub_total,amount_total:s.amount_total,amount_paid:s.amount_paid||0,sales:s.sales||[]},$())},{immediate:!0}),tt(async()=>{await Promise.all([X(),G(),J()]),u.value&&await z()}),(s,e)=>{var d;return n(),l("div",nt,[t("div",lt,[t("div",it,[t("div",dt,[t("div",null,[t("h1",ut,i(u.value?`Edit Order #${(d=p(N))==null?void 0:d.order_id}`:"Create New Order"),1),t("p",ct,i(u.value?"Modify order details and items":"Add a new customer order to the system"),1)]),t("div",mt,[R(p(U),{onClick:W,variant:"ghost"},{default:T(()=>e[2]||(e[2]=[y(" Cancel ")])),_:1,__:[2]}),R(p(U),{onClick:B,disabled:p(w)},{default:T(()=>[y(i(p(w)?"Saving...":u.value?"Update Order":"Create Order"),1)]),_:1},8,["disabled"])])])])]),p(w)&&u.value?(n(),l("div",pt,e[3]||(e[3]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):F("",!0),q.value?(n(),l("div",gt,[t("div",vt,[t("div",ft,[e[4]||(e[4]=t("h3",{class:"text-sm font-medium text-red-800"},"Error",-1)),t("div",_t,i(q.value),1)])])])):F("",!0),t("form",{onSubmit:st(B,["prevent"]),class:"space-y-6"},[t("div",xt,[t("div",bt,[t("div",yt,[t("div",ht,[t("div",wt,[t("div",null,[e[6]||(e[6]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-2"},"Add Item:",-1)),t("select",{onChange:O,class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg py-3"},[e[5]||(e[5]=t("option",{value:"",selected:"",disabled:""},"Select new Item",-1)),(n(!0),l(M,null,j(b.value,a=>(n(),l("option",{key:a.id,value:a.id},i(A(a)),9,kt))),128))],32)]),t("div",null,[e[7]||(e[7]=t("h2",{class:"text-lg font-semibold text-gray-900 mb-2"},"Customer:",-1)),E(t("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>o.value.user_id=a),class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg py-3",required:""},[(n(!0),l(M,null,j(k.value,a=>(n(),l("option",{key:a.id,value:a.id},i(a.name),9,Ct))),128))],512),[[et,o.value.user_id]])])]),t("div",St,[t("table",Mt,[e[8]||(e[8]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Item"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Discount"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Selling Price"),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Amount"),t("th",{class:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"},"Action")])],-1)),t("tbody",jt,[(n(!0),l(M,null,j(o.value.sales,(a,r)=>(n(),l("tr",{key:r},[t("td",Et,[t("span",Vt,i(A(a.item)),1)]),t("td",Nt,[E(t("input",{type:"number","onUpdate:modelValue":c=>a.discount=c,onChange:C,min:"0",step:"0.01",max:a.selling_price*a.quantity,class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg font-semibold",required:""},null,40,qt),[[H,a.discount,void 0,{number:!0}]])]),t("td",It,[t("span",$t,i(v(a.selling_price)),1)]),t("td",Bt,[t("span",At,i(v(a.selling_price*a.quantity)),1)]),t("td",Dt,[t("button",{type:"button",onClick:c=>Q(r),class:"inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"}," Delete ",8,Rt)])]))),128))])])])]),t("div",Tt,[t("div",Ft,[e[16]||(e[16]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1)),t("dl",Ht,[t("div",Ut,[e[9]||(e[9]=t("dt",{class:"text-lg font-medium text-gray-700"},"Subtotal:",-1)),t("dd",Lt,i(v(o.value.sub_total)),1)]),t("div",Pt,[e[10]||(e[10]=t("dt",{class:"text-lg font-medium text-gray-700"},"Total:",-1)),t("dd",zt,i(v(o.value.amount_total)),1)]),t("div",Wt,[e[11]||(e[11]=t("dt",{class:"text-lg font-medium text-gray-700"},"Discount:",-1)),t("dd",Xt,i(v(o.value.discount)),1)]),t("div",Gt,[e[12]||(e[12]=t("dt",{class:"text-lg font-medium text-gray-700"},"Amount paid:",-1)),t("dd",null,[E(t("input",{type:"number",step:"0.01","onUpdate:modelValue":e[1]||(e[1]=a=>o.value.amount_paid=a),class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg font-semibold text-red-600",style:{height:"50px","font-size":"1.1rem"}},null,512),[[H,o.value.amount_paid,void 0,{number:!0}]])])])]),t("div",Jt,[x.value?(n(),l("button",{key:0,type:"submit",disabled:g.value,class:"w-full flex justify-center items-center px-4 py-3 border border-transparent text-lg font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"},[g.value?(n(),l("svg",Qt,e[13]||(e[13]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(n(),l("svg",Kt,e[14]||(e[14]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"},null,-1)]))),y(" "+i(g.value?"Saving...":"Save and Exit"),1)],8,Ot)):(n(),l("button",{key:1,type:"button",onClick:$,class:"w-full flex justify-center items-center px-4 py-3 border border-transparent text-lg font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"},e[15]||(e[15]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1),y(" Calculate ")])))])])])])])])],32)])}}});export{ae as default};
