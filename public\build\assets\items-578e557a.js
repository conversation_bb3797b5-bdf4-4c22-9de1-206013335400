import{s as F,r as c,i as h}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{a as p}from"./axios-917b1704.js";const L=F("adminItems",()=>{const r=c(null),d=c(null),m=c(!1),n=c(null),g=c(!1),v=c(!1),f=c(!1),b=h(()=>{var e;return((e=r.value)==null?void 0:e.total)||0}),_=h(()=>{var e;return((e=r.value)==null?void 0:e.current_page)||1}),I=h(()=>{var e;return((e=r.value)==null?void 0:e.last_page)||1}),w=h(()=>_.value<I.value),E=h(()=>{var e;return((e=r.value)==null?void 0:e.data)||[]});return{items:r,currentItem:d,loading:m,error:n,creating:g,updating:v,deleting:f,totalItems:b,currentPage:_,lastPage:I,hasMorePages:w,itemsList:E,fetchItems:async(e={})=>{var l,s;m.value=!0,n.value=null;try{const a=new URLSearchParams;e.search&&a.append("search",e.search),e.status&&(e.status==="sold"?a.append("type","sold"):a.append("type","available")),e.auction_type_id&&a.append("auction_type_id",e.auction_type_id),e.branch_id&&a.append("branch_id",e.branch_id),e.page&&a.append("page",e.page.toString()),e.per_page&&a.append("per_page",e.per_page.toString()),e.sort_by&&a.append("sort_by",e.sort_by),e.sort_order&&a.append("sort_order",e.sort_order);const t=await p.get(`/api/items?${a.toString()}`);r.value=t.data}catch(a){n.value=((s=(l=a.response)==null?void 0:l.data)==null?void 0:s.message)||"Failed to fetch items",console.error("Error fetching items:",a)}finally{m.value=!1}},fetchItem:async e=>{var l,s;m.value=!0,n.value=null;try{const a=await p.get(`/api/items/${e}`);return d.value=a.data.data,a.data.data}catch(a){throw n.value=((s=(l=a.response)==null?void 0:l.data)==null?void 0:s.message)||"Failed to fetch item",console.error("Error fetching item:",a),a}finally{m.value=!1}},createItem:async e=>{var l,s;g.value=!0,n.value=null;try{const a=new FormData;Object.entries(e).forEach(([o,u])=>{o==="media"&&Array.isArray(u)?u.forEach((i,y)=>{a.append(`media[${y}]`,i)}):u!=null&&a.append(o,u.toString())});const t=await p.post("/api/items",a,{headers:{"Content-Type":"multipart/form-data"}});return r.value&&(r.value.data.unshift(t.data.data),r.value.total+=1),t.data.data}catch(a){throw n.value=((s=(l=a.response)==null?void 0:l.data)==null?void 0:s.message)||"Failed to create item",console.error("Error creating item:",a),a}finally{g.value=!1}},updateItem:async e=>{var l,s,a;v.value=!0,n.value=null;try{const t=new FormData;t.append("_method","PUT"),Object.entries(e).forEach(([u,i])=>{u==="media"&&Array.isArray(i)?i.forEach((y,S)=>{t.append(`media[${S}]`,y)}):i!=null&&u!=="id"&&t.append(u,i.toString())});const o=await p.post(`/api/items/${e.id}`,t,{headers:{"Content-Type":"multipart/form-data"}});if(r.value){const u=r.value.data.findIndex(i=>i.id===e.id);u!==-1&&(r.value.data[u]=o.data.data)}return((l=d.value)==null?void 0:l.id)===e.id&&(d.value=o.data.data),o.data.data}catch(t){throw n.value=((a=(s=t.response)==null?void 0:s.data)==null?void 0:a.message)||"Failed to update item",console.error("Error updating item:",t),t}finally{v.value=!1}},deleteItem:async e=>{var l,s,a;f.value=!0,n.value=null;try{await p.delete(`/api/items/${e}`),r.value&&(r.value.data=r.value.data.filter(t=>t.id!==e),r.value.total-=1),((l=d.value)==null?void 0:l.id)===e&&(d.value=null)}catch(t){throw n.value=((a=(s=t.response)==null?void 0:s.data)==null?void 0:a.message)||"Failed to delete item",console.error("Error deleting item:",t),t}finally{f.value=!1}},bulkDeleteItems:async e=>{var l,s;f.value=!0,n.value=null;try{if(await p.post("/api/items/bulk-delete",{item_ids:e.map(a=>parseInt(a))}),r.value){const a=e.map(t=>parseInt(t));r.value.data=r.value.data.filter(t=>!a.includes(t.id)),r.value.total-=e.length}}catch(a){throw n.value=((s=(l=a.response)==null?void 0:l.data)==null?void 0:s.message)||"Failed to delete items",console.error("Error bulk deleting items:",a),a}finally{f.value=!1}},bulkUpdateItemStatus:async(e,l)=>{var s,a;v.value=!0,n.value=null;try{if(await p.post("/api/items/bulk-status-update",{item_ids:e.map(t=>parseInt(t)),status:l}),r.value){const t=e.map(o=>parseInt(o));r.value.data=r.value.data.map(o=>t.includes(o.id)?{...o,closed_by:l==="close"?1:null}:o)}}catch(t){throw n.value=((a=(s=t.response)==null?void 0:s.data)==null?void 0:a.message)||"Failed to update item status",console.error("Error bulk updating item status:",t),t}finally{v.value=!1}},resetState:()=>{r.value=null,d.value=null,n.value=null,m.value=!1,g.value=!1,v.value=!1,f.value=!1}}});export{L as u};
