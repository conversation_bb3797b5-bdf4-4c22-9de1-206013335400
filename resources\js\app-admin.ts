import { createApp } from 'vue';
import { createPinia } from 'pinia';
import router from '@/router/admin';
import '../css/app.css';

// PrimeVue imports
import PrimeVue from 'primevue/config';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import ColumnGroup from 'primevue/columngroup';
import Row from 'primevue/row';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import Calendar from 'primevue/calendar';
import MultiSelect from 'primevue/multiselect';
import Paginator from 'primevue/paginator';
import ProgressBar from 'primevue/progressbar';
import Toolbar from 'primevue/toolbar';
import Dialog from 'primevue/dialog';
import ConfirmDialog from 'primevue/confirmdialog';
import Toast from 'primevue/toast';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';

// PrimeVue CSS - Import after Vue app creation
// Custom PrimeVue admin theme
import '../css/primevue-admin-theme.css';

// Import global components
import {
  Button,
  Input,
  Card,
  Modal,
  Select,
  Loading,
  Container,
  Badge,
  Alert,
  Pagination,
  NotificationContainer
} from '@/components/ui';

// Import admin components
import {
  AdminLayout,
  AdminSidebar,
  AdminHeader,
  AdminContainer,
  AdminNavigation,
  AdminMenuItem,
  AdminMobileMenu,
  MobileMenuItem,
  PrimeDataTable
} from '@/components/admin';

// Import stores for initialization
import { useAuthStore } from '@/stores/auth';
import { useAdminStore } from '@/stores/admin';
import { useNotificationsStore } from '@/stores/notifications';
import { api } from '@/utils/api';

// Create root component
const RootComponent = {
  template: `
    <div class="min-h-screen bg-gray-100">
      <router-view />
      <NotificationContainer />
    </div>
  `,
  setup() {
    const notificationsStore = useNotificationsStore();
    const authStore = useAuthStore();
    const adminStore = useAdminStore();

    // Initialize stores on app startup
    const initializeApp = async () => {
      try {
        // Show loading state
        console.log('Initializing admin application...');

        // Check if user data is available from server (most reliable for admin)
        const serverUser = (window as any).user;
        console.log('Window user data:', serverUser);
        console.log('Window CSRF token:', (window as any).csrfToken);

        if (serverUser) {
          console.log('Admin: Using server-provided user data:', serverUser);
          authStore.setUser(serverUser);
        } else {
          console.log('Admin: No server user data, initializing auth store...');
          // Initialize auth store if no server data
          await authStore.initialize();
        }

        console.log('Auth store initialized');
        console.log('Auth store user:', authStore.user);
        console.log('Auth store isAuthenticated:', authStore.isAuthenticated);
        console.log('Auth store sessionAuth:', authStore.sessionAuth);

        // Verify user has admin access
        if (authStore.user) {
          const user = authStore.user as any;

          // Check admin access using the same logic as Laravel's StaffMiddleware
          const isCustomer = typeof user.isCustomer === 'function' ? user.isCustomer() : false;
          const isSupplier = typeof user.isSupplier === 'function' ? user.isSupplier() : false;
          const hasAdminAccess = !isCustomer && !isSupplier;

          if (!hasAdminAccess) {
            console.warn('User does not have admin access');
            notificationsStore.warning('You do not have permission to access the admin panel');
            // Redirect to home page
            setTimeout(() => {
              window.location.href = '/';
            }, 2000);
            return;
          }

          console.log('Admin access verified for user:', user.name || user.email);

          // Double-check admin access with the server
          try {
            const accessCheck = await api.get('/admin/check-access');
            if (!accessCheck.hasAccess) {
              console.warn('Server denied admin access:', accessCheck.reason);
              notificationsStore.error('Access denied: ' + accessCheck.reason);
              setTimeout(() => {
                window.location.href = '/';
              }, 2000);
              return;
            }
            console.log('Server confirmed admin access for:', accessCheck.user.name);
          } catch (accessError) {
            console.error('Failed to verify admin access with server:', accessError);
            // Continue anyway if server check fails, but log the error
          }
        } else {
          console.warn('No authenticated user found');
          // Redirect to login with return URL
          const returnUrl = encodeURIComponent(window.location.pathname);
          window.location.href = `/login?redirect=${returnUrl}`;
          return;
        }

        // Initialize admin store
        adminStore.initialize();
        console.log('Admin store initialized');

        // Show welcome message
        notificationsStore.success('Admin panel loaded successfully!');
        console.log('Admin application initialized successfully');
      } catch (error) {
        console.error('Failed to initialize admin app:', error);
        notificationsStore.error('Failed to initialize admin panel. Please refresh the page.');

        // Log detailed error information
        if (error instanceof Error) {
          console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
        }
      }
    };

    // Initialize on mount
    initializeApp();

    return {};
  }
};

// Create Vue app
const app = createApp(RootComponent);

// Create Pinia store
const pinia = createPinia();

// Use plugins
app.use(pinia);
app.use(router);
app.use(PrimeVue);
app.use(ConfirmationService);
app.use(ToastService);

// Register global UI components
app.component('Button', Button);
app.component('Input', Input);
app.component('Card', Card);
app.component('Modal', Modal);
app.component('Select', Select);
app.component('Loading', Loading);
app.component('Container', Container);
app.component('Badge', Badge);
app.component('Alert', Alert);
app.component('Pagination', Pagination);
app.component('NotificationContainer', NotificationContainer);

// Register admin components
app.component('AdminLayout', AdminLayout);
app.component('AdminSidebar', AdminSidebar);
app.component('AdminHeader', AdminHeader);
app.component('AdminContainer', AdminContainer);
app.component('AdminNavigation', AdminNavigation);
app.component('AdminMenuItem', AdminMenuItem);
app.component('AdminMobileMenu', AdminMobileMenu);
app.component('MobileMenuItem', MobileMenuItem);
app.component('PrimeDataTable', PrimeDataTable);

// Register PrimeVue components
app.component('DataTable', DataTable);
app.component('Column', Column);
app.component('ColumnGroup', ColumnGroup);
app.component('Row', Row);
app.component('PrimeButton', Button); // Use PrimeButton to avoid conflict with existing Button
app.component('InputText', InputText);
app.component('PrimeDropdown', Dropdown); // Use PrimeDropdown to avoid conflict
app.component('Calendar', Calendar);
app.component('MultiSelect', MultiSelect);
app.component('Paginator', Paginator);
app.component('ProgressBar', ProgressBar);
app.component('Toolbar', Toolbar);
app.component('PrimeDialog', Dialog); // Use PrimeDialog to avoid conflict
app.component('ConfirmDialog', ConfirmDialog);
app.component('PrimeToast', Toast); // Use PrimeToast to avoid conflict

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Admin Vue error:', err, info);

  // Show user-friendly error message
  try {
    const notificationsStore = useNotificationsStore();
    notificationsStore.error('An unexpected error occurred. Please refresh the page.');
  } catch (storeError) {
    console.error('Failed to show error notification:', storeError);
    // Fallback to alert if store is not available
    alert('An unexpected error occurred. Please refresh the page.');
  }
};

// Global warning handler
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Admin Vue warning:', msg, trace);
};

// Mount the app with error handling
try {
  app.mount('#admin-app');
  console.log('Admin app mounted successfully');
} catch (error) {
  console.error('Failed to mount admin app:', error);

  // Show fallback error message
  const adminAppElement = document.getElementById('admin-app');
  if (adminAppElement) {
    adminAppElement.innerHTML = `
      <div class="min-h-screen bg-gray-100 flex items-center justify-center">
        <div class="text-center">
          <div class="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 class="text-2xl font-bold text-gray-900 mb-2">Failed to Load Admin Panel</h1>
          <p class="text-gray-600 mb-4">There was an error loading the admin interface.</p>
          <button
            onclick="window.location.reload()"
            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Refresh Page
          </button>
        </div>
      </div>
    `;
  }
}

// Export for global access if needed
export { app, pinia, router };

// Add global properties for backward compatibility
declare global {
  interface Window {
    adminApp?: typeof app;
    user?: any;
  }
}

// Store app instance globally for debugging
window.adminApp = app;
