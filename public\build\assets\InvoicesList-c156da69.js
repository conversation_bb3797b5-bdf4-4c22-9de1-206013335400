import{h as j,r,i as A,L as F,o as g,m as f,x as l,u as s,g as u,v as n,H as i,_ as d,b as w,j as M}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as R,h}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{_ as U}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";const J={class:"flex space-x-3"},O={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},H={class:"flex space-x-2"},X=j({__name:"InvoicesList",setup(q){const c=R(),k=r(!1),C=r(null),v=r(1),I=r(3),_=r(45),a=r({status:"",dateRange:"",customer:"",invoiceNumber:""}),D=[{value:"",label:"All Statuses"},{value:"draft",label:"Draft"},{value:"sent",label:"Sent"},{value:"paid",label:"Paid"},{value:"overdue",label:"Overdue"},{value:"cancelled",label:"Cancelled"}],V=[{value:"",label:"All Time"},{value:"today",label:"Today"},{value:"week",label:"This Week"},{value:"month",label:"This Month"},{value:"quarter",label:"This Quarter"}],x=[{key:"number",label:"Invoice #",sortable:!0},{key:"customer",label:"Customer",sortable:!0},{key:"amount",label:"Amount",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"dueDate",label:"Due Date",sortable:!0},{key:"date",label:"Created",sortable:!0},{key:"actions",label:"Actions",sortable:!1}],y=r([{id:1,number:"INV-2024-001",customer:"John Doe",customerEmail:"<EMAIL>",amount:299.99,status:"paid",date:new Date("2024-01-15"),dueDate:new Date("2024-02-15"),orderId:1001},{id:2,number:"INV-2024-002",customer:"Jane Smith",customerEmail:"<EMAIL>",amount:149.5,status:"sent",date:new Date("2024-01-14"),dueDate:new Date("2024-02-14"),orderId:1002},{id:3,number:"INV-2024-003",customer:"Bob Johnson",customerEmail:"<EMAIL>",amount:399.99,status:"overdue",date:new Date("2024-01-10"),dueDate:new Date("2024-02-10"),orderId:1003},{id:4,number:"INV-2024-004",customer:"Alice Brown",customerEmail:"<EMAIL>",amount:199.99,status:"draft",date:new Date("2024-01-12"),dueDate:new Date("2024-02-12"),orderId:1004}]),N=A(()=>{let t=y.value;return a.value.status&&(t=t.filter(e=>e.status===a.value.status)),a.value.customer&&(t=t.filter(e=>e.customer.toLowerCase().includes(a.value.customer.toLowerCase())||e.customerEmail.toLowerCase().includes(a.value.customer.toLowerCase()))),a.value.invoiceNumber&&(t=t.filter(e=>e.number.toLowerCase().includes(a.value.invoiceNumber.toLowerCase()))),t}),b=()=>{c.push("/admin-spa/sales/invoices/create")},S=t=>{a.value.customer=t,m()},$=(t,e)=>{console.log("Sorting by:",t,e)},E=t=>{v.value=t},m=()=>{v.value=1},L=()=>{console.log("Exporting invoices...")},P=t=>{c.push(`/admin-spa/sales/invoices/${t.id}`)},B=t=>{c.push(`/admin-spa/sales/invoices/${t.id}/edit`)},z=t=>{console.log("Downloading PDF for invoice:",t.number)},T=t=>{console.log("Sending invoice:",t.number)};return F(()=>{}),(t,e)=>(g(),f(s(U),{title:"Invoices",subtitle:"Manage customer invoices and billing",loading:k.value,error:C.value,items:N.value,columns:x,"show-create-button":!0,"create-button-text":"New Invoice","create-route":"/admin-spa/sales/invoices/create","empty-state-title":"No invoices found","empty-state-message":"Invoices will appear here when orders are processed.","show-pagination":!0,"current-page":v.value,"total-pages":I.value,"total-items":_.value,onCreate:b,onSearch:S,onSort:$,onPageChange:E},{actions:l(()=>[u("div",J,[n(s(d),{variant:"outline",size:"sm",onClick:L},{default:l(()=>e[4]||(e[4]=[u("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})],-1),i(" Export ")])),_:1,__:[4]}),n(s(d),{variant:"primary",size:"sm",onClick:b},{default:l(()=>e[5]||(e[5]=[u("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[u("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),i(" New Invoice ")])),_:1,__:[5]})])]),filters:l(()=>[u("div",O,[n(s(h),{modelValue:a.value.status,"onUpdate:modelValue":e[0]||(e[0]=o=>a.value.status=o),placeholder:"Filter by status",options:D,onChange:m},null,8,["modelValue"]),n(s(h),{modelValue:a.value.dateRange,"onUpdate:modelValue":e[1]||(e[1]=o=>a.value.dateRange=o),placeholder:"Date range",options:V,onChange:m},null,8,["modelValue"]),n(s(w),{modelValue:a.value.customer,"onUpdate:modelValue":e[2]||(e[2]=o=>a.value.customer=o),placeholder:"Search customer...",onInput:m},null,8,["modelValue"]),n(s(w),{modelValue:a.value.invoiceNumber,"onUpdate:modelValue":e[3]||(e[3]=o=>a.value.invoiceNumber=o),placeholder:"Invoice number...",onInput:m},null,8,["modelValue"])])]),"item-actions":l(({item:o})=>[u("div",H,[n(s(d),{variant:"ghost",size:"sm",onClick:p=>P(o)},{default:l(()=>e[6]||(e[6]=[i(" View ")])),_:2,__:[6]},1032,["onClick"]),n(s(d),{variant:"ghost",size:"sm",onClick:p=>z(o)},{default:l(()=>e[7]||(e[7]=[i(" PDF ")])),_:2,__:[7]},1032,["onClick"]),n(s(d),{variant:"ghost",size:"sm",onClick:p=>T(o)},{default:l(()=>e[8]||(e[8]=[i(" Send ")])),_:2,__:[8]},1032,["onClick"]),o.status==="draft"?(g(),f(s(d),{key:0,variant:"ghost",size:"sm",onClick:p=>B(o)},{default:l(()=>e[9]||(e[9]=[i(" Edit ")])),_:2,__:[9]},1032,["onClick"])):M("",!0)])]),_:1},8,["loading","error","items","current-page","total-pages","total-items"]))}});export{X as default};
