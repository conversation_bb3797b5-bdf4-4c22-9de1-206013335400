import{h as T,r as i,L as A,o as r,f as l,g as e,n as b,H as w,t as n,C as g,Q as v,p as H,F as h,k as f}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{e as D}from"./export-37598506.js";const F={class:"space-y-6"},L={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},z={class:"flex items-center justify-between mb-6"},N={class:"flex items-center space-x-3"},$=["disabled"],E={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},P={class:"bg-green-50 border border-green-200 rounded-lg p-4"},q={class:"flex items-center"},I={class:"ml-4"},U={class:"text-2xl font-semibold text-green-900"},W={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},X={class:"flex items-center"},G={class:"ml-4"},Q={class:"text-2xl font-semibold text-blue-900"},Y={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},J={class:"flex items-center"},K={class:"ml-4"},O={class:"text-2xl font-semibold text-purple-900"},Z={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-semibold text-yellow-900"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ne=["value"],re=["value"],le={key:0,class:"flex items-center justify-center py-12"},ie={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-6"},ae={class:"flex items-center"},de={class:"text-red-800"},ce={key:2,class:"overflow-x-auto"},ue={class:"min-w-full divide-y divide-gray-200"},me={class:"bg-white divide-y divide-gray-200"},pe={class:"px-6 py-4 whitespace-nowrap"},xe={class:"flex items-center"},ge={class:"flex-shrink-0 h-10 w-10"},ve=["src","alt"],he={key:1,class:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center"},fe={class:"ml-4"},ye={class:"text-sm font-medium text-gray-900"},be={class:"text-sm text-gray-500"},we={class:"px-6 py-4 whitespace-nowrap"},_e={class:"text-sm font-medium text-gray-900"},ke={class:"text-sm text-gray-500"},Me={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"text-sm font-medium text-gray-900"},Se={class:"text-sm text-gray-500"},je={class:"px-6 py-4 whitespace-nowrap"},Ve={class:"text-sm font-medium text-gray-900"},Be={class:"px-6 py-4 whitespace-nowrap"},Re={class:"text-sm font-medium text-gray-900"},Te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ae={class:"px-6 py-4 whitespace-nowrap"},He={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},De=["onClick"],Fe=["onClick"],Le={key:3,class:"text-center py-12"},Ee=T({__name:"SalesReports",setup(ze){const d=i(!1),m=i(""),c=i([]),_=i([]),y=i([]),u=i({totalSales:0,itemsSold:0,avgSalePrice:0,totalAuctions:0}),a=i({dateRange:"month",category:"",branch:""}),x=async()=>{d.value=!0,m.value="";try{const o=await fetch("/api/admin/reports/sales",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(!o.ok)throw new Error(`HTTP error! status: ${o.status}`);const t=await o.json();if(t.success)c.value=t.sales||[],u.value=t.summary||{totalSales:0,itemsSold:0,avgSalePrice:0,totalAuctions:0};else throw new Error(t.error||"Failed to fetch sales data")}catch(o){m.value=o.message||"Failed to load sales data",console.error("Sales report error:",o)}finally{d.value=!1}},k=async()=>{try{const o=await fetch("/api/admin/reports/branches",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(o.ok){const t=await o.json();t.success&&(y.value=t.branches||[])}}catch(o){console.error("Failed to load branches:",o)}},M=()=>{x()},C=()=>{if(c.value.length===0){alert("No data to export");return}D(c.value,"csv")},S=o=>{console.log("Viewing sale:",o)},j=o=>{console.log("Downloading invoice for:",o)},p=o=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(o),V=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),B=o=>({completed:"bg-green-100 text-green-800",paid:"bg-blue-100 text-blue-800",delivered:"bg-purple-100 text-purple-800",pending:"bg-yellow-100 text-yellow-800"})[o]||"bg-gray-100 text-gray-800";return A(()=>{x(),k()}),(o,t)=>(r(),l("div",F,[e("div",L,[e("div",z,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Sales Report"),e("p",{class:"text-gray-600 mt-1"},"Analyze sales performance and trends")],-1)),e("div",N,[e("button",{onClick:x,disabled:d.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[(r(),l("svg",{class:b(["w-4 h-4 mr-2",{"animate-spin":d.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[3]||(t[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),t[4]||(t[4]=w(" Refresh "))],8,$),e("button",{onClick:C,class:"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),w(" Export ")]))])]),e("div",E,[e("div",P,[e("div",q,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",I,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-green-600"},"Total Sales",-1)),e("p",U,"$"+n(p(u.value.totalSales)),1)])])]),e("div",W,[e("div",X,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1)),e("div",G,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-blue-600"},"Items Sold",-1)),e("p",Q,n(u.value.itemsSold),1)])])]),e("div",Y,[e("div",J,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",K,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-purple-600"},"Avg Sale Price",-1)),e("p",O,"$"+n(p(u.value.avgSalePrice)),1)])])]),e("div",Z,[e("div",ee,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1)),e("div",te,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-yellow-600"},"Auctions",-1)),e("p",se,n(u.value.totalAuctions),1)])])])]),e("div",oe,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),g(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>a.value.dateRange=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},t[15]||(t[15]=[H('<option value="today">Today</option><option value="week">This Week</option><option value="month">This Month</option><option value="quarter">This Quarter</option><option value="year">This Year</option><option value="custom">Custom Range</option>',6)]),512),[[v,a.value.dateRange]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Category",-1)),g(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>a.value.category=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[t[17]||(t[17]=e("option",{value:""},"All Categories",-1)),(r(!0),l(h,null,f(_.value,s=>(r(),l("option",{key:s.id,value:s.id},n(s.name),9,ne))),128))],512),[[v,a.value.category]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Branch",-1)),g(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>a.value.branch=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},[t[19]||(t[19]=e("option",{value:""},"All Branches",-1)),(r(!0),l(h,null,f(y.value,s=>(r(),l("option",{key:s.id,value:s.id},n(s.name),9,re))),128))],512),[[v,a.value.branch]])]),e("div",{class:"flex items-end"},[e("button",{onClick:M,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Apply Filters ")])]),d.value?(r(),l("div",le,t[21]||(t[21]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("span",{class:"ml-2 text-gray-600"},"Loading sales data...",-1)]))):m.value?(r(),l("div",ie,[e("div",ae,[t[22]||(t[22]=e("svg",{class:"h-5 w-5 text-red-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("span",de,n(m.value),1)])])):c.value.length>0?(r(),l("div",ce,[e("table",ue,[t[24]||(t[24]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Item"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Auction"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Winner"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Sale Price"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Commission"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Sale Date"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",me,[(r(!0),l(h,null,f(c.value,s=>(r(),l("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",pe,[e("div",xe,[e("div",ge,[s.item_image?(r(),l("img",{key:0,src:s.item_image,alt:s.item_title,class:"h-10 w-10 rounded-full object-cover"},null,8,ve)):(r(),l("div",he,t[23]||(t[23]=[e("svg",{class:"h-6 w-6 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)])))]),e("div",fe,[e("div",ye,n(s.item_title),1),e("div",be,"#"+n(s.item_id),1)])])]),e("td",we,[e("div",_e,n(s.auction_title),1),e("div",ke,"#"+n(s.auction_id),1)]),e("td",Me,[e("div",Ce,n(s.winner_name),1),e("div",Se,n(s.winner_email),1)]),e("td",je,[e("div",Ve,"$"+n(p(s.sale_price)),1)]),e("td",Be,[e("div",Re,"$"+n(p(s.commission)),1)]),e("td",Te,n(V(s.sale_date)),1),e("td",Ae,[e("span",{class:b([B(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(s.status),3)]),e("td",He,[e("button",{onClick:R=>S(s),class:"text-blue-600 hover:text-blue-900 mr-3"}," View ",8,De),e("button",{onClick:R=>j(s),class:"text-green-600 hover:text-green-900"}," Invoice ",8,Fe)])]))),128))])])])):(r(),l("div",Le,t[25]||(t[25]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No sales found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No sales transactions match the current filters.",-1)])))])]))}});export{Ee as default};
