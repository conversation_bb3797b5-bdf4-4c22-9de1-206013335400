import{h as L,r as c,R as P,o as l,m as f,x as r,u as n,g as e,v as h,f as d,t as m,_ as k,H as b,j as g,d as _,C as H,G as I,k as w,F as B}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as U}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{A as j}from"./AdminDetailTemplate-67d04137.js";import{u as R}from"./useNotifications-98e2c61c.js";const T={class:"space-y-6"},G={class:"p-6"},O={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},W={class:"space-y-4"},q={key:0,class:"mt-4 p-4 bg-blue-50 rounded-lg"},J={class:"flex items-center justify-between"},K={class:"flex items-center space-x-3"},Q={class:"text-sm font-medium text-blue-900"},X={class:"text-xs text-blue-700"},Y={class:"p-6"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ee={class:"flex items-center"},te={class:"flex items-center"},se={class:"p-6"},le={class:"overflow-x-auto"},ae={class:"min-w-full divide-y divide-gray-200"},oe={class:"bg-gray-50"},ie={class:"bg-white divide-y divide-gray-200"},re={class:"mt-4 text-sm text-gray-500"},ne={class:"flex justify-end space-x-4"},ge=L({__name:"BulkImport",setup(de){const C=U(),{showNotification:v}=R(),M=c(!1),S=c(null),x=c(!1),o=c(null),u=c([]),y=c([]),p=P({skipHeader:!0,updateExisting:!1}),F=()=>{C.push("/admin-spa/items/list")},V=a=>{var i;const s=(i=a.target.files)==null?void 0:i[0];s&&(o.value=s,A())},E=()=>{o.value=null,u.value=[],y.value=[]},A=async a=>{try{y.value=["Name","Description","Category","Starting Price","Status"],u.value=[["Vintage Watch","Antique pocket watch from 1920s","Jewelry","$150.00","Active"],["Modern Laptop","High-performance gaming laptop","Electronics","$800.00","Active"],["Wooden Chair","Handcrafted oak dining chair","Furniture","$75.00","Active"]]}catch{v({type:"error",title:"Parse Error",message:"Failed to parse the selected file."})}},N=async()=>{if(o.value){x.value=!0;try{await new Promise(a=>setTimeout(a,2e3)),v({type:"success",title:"Import Successful",message:`Successfully imported ${u.value.length} items.`}),C.push("/admin-spa/items/list")}catch{v({type:"error",title:"Import Failed",message:"Failed to import items. Please try again."})}finally{x.value=!1}}},D=a=>{if(a===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB"],i=Math.floor(Math.log(a)/Math.log(t));return parseFloat((a/Math.pow(t,i)).toFixed(2))+" "+s[i]};return(a,t)=>(l(),f(n(j),{title:"Bulk Import Items",subtitle:"Import multiple items from CSV/Excel files",loading:M.value,error:S.value,onBack:F},{default:r(()=>[e("div",T,[h(n(_),null,{default:r(()=>[e("div",G,[t[6]||(t[6]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Upload File",-1)),e("div",O,[e("div",W,[t[3]||(t[3]=e("div",{class:"mx-auto h-12 w-12 text-gray-400"},[e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 48 48"},[e("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1)),e("div",null,[t[2]||(t[2]=e("label",{for:"file-upload",class:"cursor-pointer"},[e("span",{class:"mt-2 block text-sm font-medium text-gray-900"}," Drop files here or click to upload "),e("span",{class:"mt-1 block text-xs text-gray-500"}," CSV, Excel files up to 10MB ")],-1)),e("input",{id:"file-upload",name:"file-upload",type:"file",class:"sr-only",accept:".csv,.xlsx,.xls",onChange:V},null,32)])])]),o.value?(l(),d("div",q,[e("div",J,[e("div",K,[t[4]||(t[4]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",Q,m(o.value.name),1),e("p",X,m(D(o.value.size)),1)])]),h(n(k),{variant:"ghost",size:"sm",onClick:E},{default:r(()=>t[5]||(t[5]=[b(" Remove ")])),_:1,__:[5]})])])):g("",!0)])]),_:1}),o.value?(l(),f(n(_),{key:0},{default:r(()=>[e("div",Y,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Import Options",-1)),e("div",Z,[e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Skip Header Row ",-1)),e("div",ee,[H(e("input",{id:"skip-header","onUpdate:modelValue":t[0]||(t[0]=s=>p.skipHeader=s),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[I,p.skipHeader]]),t[7]||(t[7]=e("label",{for:"skip-header",class:"ml-2 text-sm text-gray-600"}," First row contains column headers ",-1))])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Update Existing Items ",-1)),e("div",te,[H(e("input",{id:"update-existing","onUpdate:modelValue":t[1]||(t[1]=s=>p.updateExisting=s),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[I,p.updateExisting]]),t[9]||(t[9]=e("label",{for:"update-existing",class:"ml-2 text-sm text-gray-600"}," Update items if they already exist ",-1))])])])])]),_:1})):g("",!0),u.value.length>0?(l(),f(n(_),{key:1},{default:r(()=>[e("div",se,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Preview",-1)),e("div",le,[e("table",ae,[e("thead",oe,[e("tr",null,[(l(!0),d(B,null,w(y.value,s=>(l(),d("th",{key:s,class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},m(s),1))),128))])]),e("tbody",ie,[(l(!0),d(B,null,w(u.value.slice(0,5),(s,i)=>(l(),d("tr",{key:i},[(l(!0),d(B,null,w(s,($,z)=>(l(),d("td",{key:z,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},m($),1))),128))]))),128))])])]),e("p",re," Showing first 5 rows of "+m(u.value.length)+" total rows ",1)])]),_:1})):g("",!0),e("div",ne,[h(n(k),{variant:"outline",onClick:F},{default:r(()=>t[13]||(t[13]=[b(" Cancel ")])),_:1,__:[13]}),o.value?(l(),f(n(k),{key:0,loading:x.value,onClick:N},{default:r(()=>t[14]||(t[14]=[b(" Import Items ")])),_:1,__:[14]},8,["loading"])):g("",!0)])])]),_:1},8,["loading","error"]))}});export{ge as default};
