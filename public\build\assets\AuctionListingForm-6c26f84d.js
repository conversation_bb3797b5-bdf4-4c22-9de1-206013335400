import{h as te,r as A,R as q,i as u,w as M,L as P,o as f,m as L,x as m,u as i,g as o,v as n,n as ae,f as w,t as p,j as B,d as k,k as se,H as C,F as oe,_ as H,B as ie}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as re,f as ne,h as le}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{a as U}from"./axios-917b1704.js";import{_}from"./FormField.vue_vue_type_script_setup_true_lang-529d40d6.js";import"./app-admin-1baa1658.js";import{A as de}from"./AdminFormTemplate-3cd16ecb.js";import{A as G}from"./AdminBadge-74cb3994.js";import{u as ue}from"./items-578e557a.js";import{u as ce}from"./branches-1476f76c.js";import{u as me}from"./useNotifications-98e2c61c.js";const pe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},_e={class:"md:col-span-2"},fe={class:"md:col-span-2"},ge={key:0,class:"mt-1 text-sm text-red-600"},be={class:"md:col-span-2"},ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},he={class:"text-lg font-medium text-gray-900 mb-4"},xe={class:"space-y-4"},Se=["src","alt"],Ve={class:"flex-1 min-w-0"},Ae={class:"text-base font-medium text-gray-900 truncate"},we={class:"text-sm text-gray-600 mt-1"},Be={class:"text-sm text-gray-600"},ke={key:0,class:"text-sm text-gray-500 mt-1 line-clamp-2"},Ce={class:"flex items-center space-x-2 mt-2"},Ue={class:"flex justify-end space-x-3 pt-6 border-t"},ze=te({__name:"AuctionListingForm",setup(Ie){const T=re(),g=ne(),v=ue(),I=ce(),{showNotification:y}=me(),F=A(!1),h=A(!1),x=A(null),$=A([]),t=q({name:"",code:"",item_ids:[],branch_id:"",description:"",bid_amount:"",user_id:"",date_from:"",date_to:""}),l=q({name:"",code:"",item_ids:"",branch_id:"",description:"",bid_amount:"",user_id:"",date_from:"",date_to:""}),c=u(()=>!!g.params.id),S=u(()=>g.params.id?parseInt(g.params.id):null),J=u(()=>[{label:"Dashboard",href:"/admin-spa"},{label:"Auction Listings",href:"/admin-spa/auction-listings/list"},{label:c.value?"Edit Auction":"Create Auction"}]),E=u(()=>t.name!==""&&t.item_ids.length>0),V=u(()=>t.item_ids.length?v.itemsList.filter(e=>t.item_ids.includes(e.id.toString())):[]);u(()=>V.value[0]||null);const N=u(()=>v.itemsList.filter(e=>!e.closed_by).map(e=>({key:e.id.toString(),label:`${e.name} (${e.reference_number||"No ref"})`,value:e.id.toString(),description:e.description||void 0}))),D=u({get:()=>N.value.filter(e=>t.item_ids.includes(e.value)),set:e=>{t.item_ids=e.map(a=>a.value)}}),K=u(()=>[...I.branches.map(e=>({label:e.name,value:e.id.toString()}))]),Q=u(()=>[...$.value.map(e=>({label:`${e.name} (${e.email})`,value:e.id.toString()}))]),W=async()=>{var e,a,s,b;if(S.value){F.value=!0;try{const d=await U.get(`/api/admin/auction-types/${S.value}`),r=d.data.data||d.data;t.name=r.name||"",t.code=r.code||"",t.item_ids=((e=r.items)==null?void 0:e.map(ee=>ee.id.toString()))||[],t.branch_id=((a=r.branch_id)==null?void 0:a.toString())||"",t.description=r.description||"",t.bid_amount=((s=r.bid_amount)==null?void 0:s.toString())||"",t.user_id=((b=r.created_by)==null?void 0:b.toString())||"",t.date_from=r.date_from?z(r.date_from):"",t.date_to=r.date_to?z(r.date_to):""}catch{x.value="Failed to load auction listing",y("Failed to load auction listing","error")}finally{F.value=!1}}},O=async()=>{try{const e=await U.get("/api/admin/users");$.value=e.data.data||e.data}catch(e){console.error("Failed to load users:",e)}},j=async()=>{var e,a,s,b;if(E.value){h.value=!0,X();try{const d={name:t.name||void 0,description:t.description||void 0,bid_amount:t.bid_amount?parseFloat(t.bid_amount):void 0,date_from:t.date_from||void 0,date_to:t.date_to||void 0,type:"live",created_by:t.user_id?parseInt(t.user_id):void 0,items:t.item_ids.map(r=>parseInt(r))};if(c.value&&S.value){const r=await U.put(`/api/admin/auction-types/${S.value}`,d);y("Auction listing updated successfully","success")}else{const r=await U.post("/api/admin/auction-types",d);y("Auction listing created successfully","success")}T.push("/admin-spa/auction-listings/list")}catch(d){(a=(e=d.response)==null?void 0:e.data)!=null&&a.errors?Object.assign(l,d.response.data.errors):(x.value=((b=(s=d.response)==null?void 0:s.data)==null?void 0:b.message)||"Failed to save auction listing",y("Failed to save auction listing","error"))}finally{h.value=!1}}},R=()=>{T.push("/admin-spa/auction-listings/list")},X=()=>{Object.keys(l).forEach(e=>{l[e]=""}),x.value=null},Y=e=>{switch(e){case"live":return"success";case"online":return"primary";case"cash":return"warning";default:return"secondary"}},Z=e=>e?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e):"$0.00",z=e=>new Date(e).toISOString().slice(0,16);return M(()=>g.query.item_id,e=>{e&&!c.value&&(t.item_ids=[e.toString()])},{immediate:!0}),P(async()=>{await Promise.all([v.fetchItems({per_page:100}),I.fetchBranches(),O()]),c.value&&await W()}),M(()=>g.query.item_id,e=>{e&&!c.value&&(t.item_id=e.toString())},{immediate:!0}),P(async()=>{await Promise.all([auctionTypesStore.fetchAuctionTypes(),v.fetchItems({per_page:100}),I.fetchBranches(),O()]),c.value&&await loadAuction()}),(e,a)=>(f(),L(i(de),{title:c.value?"Edit Auction Listing":"Create Auction Listing",subtitle:c.value?"Update auction listing details":"Create a new auction listing",loading:F.value,error:x.value,breadcrumbs:J.value,onSubmit:j,onCancel:R},{default:m(()=>[o("form",{onSubmit:ie(j,["prevent"]),class:"space-y-6"},[n(i(k),{class:"p-6"},{default:m(()=>[a[10]||(a[10]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),o("div",pe,[o("div",_e,[n(i(_),{modelValue:t.name,"onUpdate:modelValue":a[0]||(a[0]=s=>t.name=s),label:"Auction Name",placeholder:"Enter auction name",error:l.name},null,8,["modelValue","error"])]),n(i(_),{modelValue:t.code,"onUpdate:modelValue":a[1]||(a[1]=s=>t.code=s),label:"Auction Code",placeholder:"Enter auction code",error:l.code},null,8,["modelValue","error"]),o("div",fe,[a[9]||(a[9]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Select Items * ",-1)),n(i(le),{modelValue:D.value,"onUpdate:modelValue":a[2]||(a[2]=s=>D.value=s),items:N.value,placeholder:"Select items for this auction...","multi-select":!0,searchable:"","search-placeholder":"Search items...",class:ae(l.item_ids?"border-red-300":"")},null,8,["modelValue","items","class"]),l.item_ids?(f(),w("p",ge,p(l.item_ids),1)):B("",!0)]),n(i(_),{modelValue:t.branch_id,"onUpdate:modelValue":a[3]||(a[3]=s=>t.branch_id=s),label:"Branch",type:"select",placeholder:"Select branch",options:K.value,error:l.branch_id},null,8,["modelValue","options","error"]),o("div",be,[n(i(_),{modelValue:t.description,"onUpdate:modelValue":a[4]||(a[4]=s=>t.description=s),label:"Description",type:"textarea",placeholder:"Enter auction description",error:l.description,rows:"4"},null,8,["modelValue","error"])])])]),_:1,__:[10]}),n(i(k),{class:"p-6"},{default:m(()=>[a[11]||(a[11]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Bidding Information",-1)),o("div",ve,[n(i(_),{modelValue:t.bid_amount,"onUpdate:modelValue":a[5]||(a[5]=s=>t.bid_amount=s),label:"Starting Bid Amount",type:"number",step:"0.01",min:"0",placeholder:"0.00",error:l.bid_amount},null,8,["modelValue","error"]),n(i(_),{modelValue:t.user_id,"onUpdate:modelValue":a[6]||(a[6]=s=>t.user_id=s),label:"Owner/Bidder",type:"select",placeholder:"Select owner",options:Q.value,error:l.user_id},null,8,["modelValue","options","error"])])]),_:1,__:[11]}),n(i(k),{class:"p-6"},{default:m(()=>[a[12]||(a[12]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Schedule",-1)),o("div",ye,[n(i(_),{modelValue:t.date_from,"onUpdate:modelValue":a[7]||(a[7]=s=>t.date_from=s),label:"Start Date & Time",type:"datetime-local",error:l.date_from},null,8,["modelValue","error"]),n(i(_),{modelValue:t.date_to,"onUpdate:modelValue":a[8]||(a[8]=s=>t.date_to=s),label:"End Date & Time",type:"datetime-local",error:l.date_to},null,8,["modelValue","error"])])]),_:1,__:[12]}),V.value.length>0?(f(),L(i(k),{key:0,class:"p-6"},{default:m(()=>[o("h3",he," Selected Items ("+p(V.value.length)+") ",1),o("div",xe,[(f(!0),w(oe,null,se(V.value,s=>(f(),w("div",{key:s.id,class:"flex items-start space-x-4 p-4 border border-gray-200 rounded-lg"},[o("img",{src:s.image||"/img/product.jpeg",alt:s.name,class:"w-16 h-16 rounded-lg object-cover flex-shrink-0"},null,8,Se),o("div",Ve,[o("h4",Ae,p(s.name),1),o("p",we,"Ref: "+p(s.reference_number||"N/A"),1),o("p",Be,"Target: "+p(Z(s.target_amount)),1),s.description?(f(),w("p",ke,p(s.description),1)):B("",!0),o("div",Ce,[n(i(G),{variant:s.closed_by?"success":"warning",size:"sm"},{default:m(()=>[C(p(s.closed_by?"Sold":"Available"),1)]),_:2},1032,["variant"]),s.auctionType?(f(),L(i(G),{key:0,variant:Y(s.auctionType.type),size:"sm"},{default:m(()=>[C(p(s.auctionType.name),1)]),_:2},1032,["variant"])):B("",!0)])])]))),128))])]),_:1})):B("",!0),o("div",Ue,[n(i(H),{type:"button",variant:"outline",onClick:R,disabled:h.value},{default:m(()=>a[13]||(a[13]=[C(" Cancel ")])),_:1,__:[13]},8,["disabled"]),n(i(H),{type:"submit",variant:"primary",loading:h.value,disabled:!E.value},{default:m(()=>[C(p(c.value?"Update Auction":"Create Auction"),1)]),_:1},8,["loading","disabled"])])],32)]),_:1},8,["title","subtitle","loading","error","breadcrumbs"]))}});export{ze as default};
