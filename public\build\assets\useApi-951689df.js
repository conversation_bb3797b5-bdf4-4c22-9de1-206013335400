import{r as v,i as y}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as w,c as s}from"./app-admin-1baa1658.js";function S(f,h={}){const u=v(null),o=v(null),t=v(!1),c=v(!1),{immediate:m=!1,onSuccess:n,onError:l}=h,E=y(()=>u.value!==null),k=y(()=>o.value!==null),i=()=>{u.value=null,o.value=null,t.value=!1,c.value=!1},p=async r=>{i(),t.value=!0;try{const e=await w.get(f,r);return u.value=e,c.value=!0,n&&n(e),e}catch(e){const a=e instanceof s?e:new s("Unknown error",0);throw o.value=a,l&&l(a),a}finally{t.value=!1}},U=async r=>{i(),t.value=!0;try{const e=await w.post(f,r);return u.value=e,c.value=!0,n&&n(e),e}catch(e){const a=e instanceof s?e:new s("Unknown error",0);throw o.value=a,l&&l(a),a}finally{t.value=!1}},d=async r=>{i(),t.value=!0;try{const e=await w.put(f,r);return u.value=e,c.value=!0,n&&n(e),e}catch(e){const a=e instanceof s?e:new s("Unknown error",0);throw o.value=a,l&&l(a),a}finally{t.value=!1}},g=async()=>{i(),t.value=!0;try{const r=await w.delete(f);return u.value=r,c.value=!0,n&&n(r),r}catch(r){const e=r instanceof s?r:new s("Unknown error",0);throw o.value=e,l&&l(e),e}finally{t.value=!1}};return m&&p(),{data:u,error:o,isLoading:t,isSuccess:c,hasData:E,hasError:k,execute:p,post:U,put:d,remove:g,reset:i}}export{S as u};
