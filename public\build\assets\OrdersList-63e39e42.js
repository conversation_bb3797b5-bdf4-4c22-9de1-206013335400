import{h as Q,r as i,i as W,w as x,L as G,o as g,f as w,v as c,x as l,u as C,P as v,S as H,g as a,t as r,j as S,n as J,C as K,m as X}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as Y}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{P as Z}from"./app-admin-1baa1658.js";import{u as ee}from"./useApi-951689df.js";const te={class:"p-6"},oe={class:"flex items-center space-x-3"},se={class:"font-medium text-gray-900"},ae={class:"text-sm text-gray-500"},re={class:"text-gray-900"},le={class:"font-medium text-gray-900"},ne={key:0,class:"text-xs text-green-600"},ue={class:"text-gray-500"},ve=Q({__name:"OrdersList",setup(de){const p=Y(),{data:b,isLoading:O,execute:P}=ee("/admin/orders"),u=i(1),V=i(1),h=i(0),m=i(20),o=i({status:"",dateRange:"",customer:"",orderId:""}),D=[{value:"",label:"All Statuses"},{value:"1",label:"Active"},{value:"11",label:"Completed"},{value:"13",label:"Cancelled"}],I=[{value:"",label:"All Time"},{value:"today",label:"Today"},{value:"week",label:"This Week"},{value:"month",label:"This Month"},{value:"quarter",label:"This Quarter"}],k=[{field:"order_id",header:"Order ID",sortable:!0,bodySlot:"order-id-body"},{field:"customer",header:"Customer",sortable:!0,bodySlot:"customer-body"},{field:"sales_count",header:"Items",sortable:!1,bodySlot:"items-body"},{field:"amount_total",header:"Amount",sortable:!0,bodySlot:"amount-body"},{field:"status",header:"Status",sortable:!0,bodySlot:"status-body"},{field:"created_at",header:"Date",sortable:!0,bodySlot:"date-body"}],F=W(()=>{var e;return((e=b.value)==null?void 0:e.data)||[]});x(b,e=>{e&&(u.value=e.current_page||1,V.value=e.last_page||1,h.value=e.total||0,m.value=e.per_page||20)},{immediate:!0});const n=async()=>{const e={page:u.value,per_page:m.value};o.value.status&&(e.status=o.value.status),o.value.customer&&(e.customer=o.value.customer),o.value.orderId&&(e.order_id=o.value.orderId),o.value.dateRange&&(e.date_range=o.value.dateRange);try{await P(e)}catch(s){console.error("Error loading orders:",s)}},R=()=>{p.push("/admin-spa/sales/orders/create")},T=e=>{console.log("Sort event:",e),n()},A=e=>{u.value=e.page+1,m.value=e.rows,n()},B=e=>{console.log("Filter event:",e),n()},d=()=>{u.value=1,n()},N=()=>{console.log("Exporting orders...")},U=e=>{console.log("Deleting order:",e.id)},E=e=>{p.push(`/admin-spa/sales/orders/${e.id}`)},$=e=>{p.push(`/admin-spa/sales/orders/${e.id}/edit`)},L=e=>{console.log("Processing order:",e.id)},y=e=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e),z=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),M=e=>({Active:"bg-blue-100 text-blue-800",Completed:"bg-green-100 text-green-800",Cancelled:"bg-red-100 text-red-800",Pending:"bg-yellow-100 text-yellow-800",Processing:"bg-purple-100 text-purple-800"})[e]||"bg-gray-100 text-gray-800";return x(o,()=>{d()},{deep:!0}),G(()=>{n()}),(e,s)=>{const _=v("PrimeDropdown"),f=v("InputText"),j=v("PrimeButton"),q=H("tooltip");return g(),w("div",te,[c(C(Z),{title:"Orders",subtitle:"Manage customer orders and transactions",items:F.value,columns:k,loading:C(O),"current-page":u.value,"total-items":h.value,"per-page":m.value,lazy:!0,"show-pagination":!0,"show-global-filter":!0,"global-filter-placeholder":"Search orders...","global-filter-fields":["order_id","customer","customer_email","status"],"show-toolbar":!0,"show-create-button":!0,"create-button-text":"New Order","show-export":!0,"show-refresh":!0,"empty-state-title":"No orders found","empty-state-message":"Orders will appear here when customers make purchases.","data-key":"id",onCreate:R,onPage:A,onSort:T,onFilter:B,onRefresh:n,onExport:N,onView:E,onEdit:$,onDelete:U},{filters:l(()=>[a("div",oe,[c(_,{modelValue:o.value.status,"onUpdate:modelValue":s[0]||(s[0]=t=>o.value.status=t),options:D,"option-label":"label","option-value":"value",placeholder:"Filter by status",class:"w-48",onChange:d},null,8,["modelValue"]),c(_,{modelValue:o.value.dateRange,"onUpdate:modelValue":s[1]||(s[1]=t=>o.value.dateRange=t),options:I,"option-label":"label","option-value":"value",placeholder:"Date range",class:"w-48",onChange:d},null,8,["modelValue"]),c(f,{modelValue:o.value.customer,"onUpdate:modelValue":s[2]||(s[2]=t=>o.value.customer=t),placeholder:"Search customer...",class:"w-48",onInput:d},null,8,["modelValue"]),c(f,{modelValue:o.value.orderId,"onUpdate:modelValue":s[3]||(s[3]=t=>o.value.orderId=t),placeholder:"Order ID...",class:"w-32",onInput:d},null,8,["modelValue"])])]),"customer-body":l(({data:t})=>[a("div",null,[a("div",se,r(t.customer),1),a("div",ae,r(t.customer_email),1)])]),"items-body":l(({data:t})=>[a("span",re,r(t.sales_count)+" item"+r(t.sales_count===1?"":"s"),1)]),"amount-body":l(({data:t})=>[a("div",null,[a("div",le,"$"+r(y(t.amount_total)),1),t.discount>0?(g(),w("div",ne," -$"+r(y(t.discount))+" discount ",1)):S("",!0)])]),"status-body":l(({data:t})=>[a("span",{class:J([M(t.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},r(t.status),3)]),"date-body":l(({data:t})=>[a("span",ue,r(z(t.created_at)),1)]),"row-actions":l(({data:t})=>[t.status_id!==11?K((g(),X(j,{key:0,icon:"pi pi-cog",severity:"info",outlined:"",size:"small",onClick:ie=>L(t)},null,8,["onClick"])),[[q,"Process Order"]]):S("",!0)]),_:1},8,["items","loading","current-page","total-items","per-page"])])}}});export{ve as default};
