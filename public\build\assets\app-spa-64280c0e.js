import{o as a,f as l,g as e,h as me,r as T,i as q,n as G,j as L,F as ue,k as ce,l as He,t as x,m as de,u as i,p as Be,q as Oe,s as We,w as _e,v as A,x as O,T as Ge,y as Wt,z as jt,A as Ut,B as xe,C as pe,D as Ie,E as Ze,G as Ee,H as W,I as ft,J as Yt,K as qe,L as $e,M as vt,_ as te,N as De,O as Kt,P as gt,Q as It,b as he,d as Fe,e as St,c as Qt,a as Xt}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{r as Gt,u as ge,a as Me,_ as ke,b as Ne,c as Zt,d as Jt,e as Ve,f as At,g as we,L as ze,N as Bt,A as it,h as Tt,i as Nt,j as es,k as ts}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{a as ye}from"./axios-917b1704.js";function ss(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"})])}function os(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"})])}function bt(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])}function yt(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function Je(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"})])}function et(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function Ke(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"})])}function as(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"})])}function rs(o,s){return a(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}var ns=typeof global=="object"&&global&&global.Object===Object&&global;const ls=ns;var is=typeof self=="object"&&self&&self.Object===Object&&self,ds=ls||is||Function("return this")();const zt=ds;var us=zt.Symbol;const Qe=us;var Dt=Object.prototype,cs=Dt.hasOwnProperty,ms=Dt.toString,Re=Qe?Qe.toStringTag:void 0;function vs(o){var s=cs.call(o,Re),r=o[Re];try{o[Re]=void 0;var c=!0}catch{}var h=ms.call(o);return c&&(s?o[Re]=r:delete o[Re]),h}var gs=Object.prototype,ps=gs.toString;function hs(o){return ps.call(o)}var fs="[object Null]",bs="[object Undefined]",xt=Qe?Qe.toStringTag:void 0;function ys(o){return o==null?o===void 0?bs:fs:xt&&xt in Object(o)?vs(o):hs(o)}function xs(o){return o!=null&&typeof o=="object"}var ws="[object Symbol]";function _s(o){return typeof o=="symbol"||xs(o)&&ys(o)==ws}var ks=/\s/;function Cs(o){for(var s=o.length;s--&&ks.test(o.charAt(s)););return s}var $s=/^\s+/;function Ms(o){return o&&o.slice(0,Cs(o)+1).replace($s,"")}function dt(o){var s=typeof o;return o!=null&&(s=="object"||s=="function")}var wt=0/0,js=/^[-+]0x[0-9a-f]+$/i,Is=/^0b[01]+$/i,Ss=/^0o[0-7]+$/i,As=parseInt;function _t(o){if(typeof o=="number")return o;if(_s(o))return wt;if(dt(o)){var s=typeof o.valueOf=="function"?o.valueOf():o;o=dt(s)?s+"":s}if(typeof o!="string")return o===0?o:+o;o=Ms(o);var r=Is.test(o);return r||Ss.test(o)?As(o.slice(2),r?2:8):js.test(o)?wt:+o}var Bs=function(){return zt.Date.now()};const tt=Bs;var Ts="Expected a function",Ns=Math.max,zs=Math.min;function Ds(o,s,r){var c,h,w,B,N,j,v=0,g=!1,E=!1,D=!0;if(typeof o!="function")throw new TypeError(Ts);s=_t(s)||0,dt(r)&&(g=!!r.leading,E="maxWait"in r,w=E?Ns(_t(r.maxWait)||0,s):w,D="trailing"in r?!!r.trailing:D);function M(C){var k=c,V=h;return c=h=void 0,v=C,B=o.apply(V,k),B}function _(C){return v=C,N=setTimeout(F,s),g?M(C):B}function m(C){var k=C-j,V=C-v,Z=s-k;return E?zs(Z,w-V):Z}function I(C){var k=C-j,V=C-v;return j===void 0||k>=s||k<0||E&&V>=w}function F(){var C=tt();if(I(C))return $(C);N=setTimeout(F,m(C))}function $(C){return N=void 0,D&&c?M(C):(c=h=void 0,B)}function u(){N!==void 0&&clearTimeout(N),v=0,c=j=h=N=void 0}function d(){return N===void 0?B:$(tt())}function p(){var C=tt(),k=I(C);if(c=arguments,h=this,j=C,k){if(N===void 0)return _(j);if(E)return clearTimeout(N),N=setTimeout(F,s),M(j)}return N===void 0&&(N=setTimeout(F,s)),B}return p.cancel=u,p.flush=d,p}const Ls={class:"overflow-hidden"},Ps={key:0,class:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},Es=["checked","indeterminate"],Vs=["onClick"],Rs={key:0},Fs=["colspan"],qs={key:1},Hs=["colspan"],Os={key:0,class:"px-4 py-3"},Ws=["checked","onChange"],Us=["innerHTML"],Ys={key:1,class:"text-gray-900"},Ks=me({__name:"Table",props:{columns:{},data:{},loading:{type:Boolean,default:!1},striped:{type:Boolean,default:!1},bordered:{type:Boolean,default:!1},hover:{type:Boolean,default:!0},size:{default:"md"},sortBy:{},sortDirection:{default:"asc"},selectable:{type:Boolean,default:!1},selectedRows:{default:()=>[]},emptyMessage:{default:"No data available"},stickyHeader:{type:Boolean,default:!1},maxHeight:{}},emits:["sort","select","row-click"],setup(o,{emit:s}){const r=o,c=s,h=T([...r.selectedRows]),w=q(()=>"min-w-full divide-y divide-gray-200"),B=q(()=>{const $="bg-gray-50",u=r.stickyHeader?"sticky top-0 z-10":"";return[$,u].filter(Boolean).join(" ")}),N=q(()=>"bg-white divide-y divide-gray-200"),j={sm:"text-sm",md:"text-sm",lg:"text-base"},v=q(()=>r.columns.length+(r.selectable?1:0)),g=q(()=>r.data.length>0&&h.value.length===r.data.length),E=q(()=>h.value.length>0&&h.value.length<r.data.length),D=($,u)=>u.split(".").reduce((d,p)=>d==null?void 0:d[p],$),M=($,u)=>$.id||$.key||u,_=$=>{const u=r.sortBy===$&&r.sortDirection==="asc"?"desc":"asc";c("sort",{column:$,direction:u})},m=$=>h.value.some(u=>M(u,-1)===M($,-1)),I=$=>{const u=m($);u?h.value=h.value.filter(d=>M(d,-1)!==M($,-1)):h.value.push($),c("select",{selectedRows:[...h.value],row:$,isSelected:!u})},F=()=>{g.value?h.value=[]:h.value=[...r.data],c("select",{selectedRows:[...h.value]})};return($,u)=>(a(),l("div",Ls,[e("div",{class:G(["overflow-auto",$.maxHeight?`max-h-[${$.maxHeight}]`:"",$.bordered?"border border-gray-200 rounded-lg":""])},[e("table",{class:G(w.value)},[e("thead",{class:G(B.value)},[e("tr",null,[$.selectable?(a(),l("th",Ps,[e("input",{type:"checkbox",checked:g.value,indeterminate:E.value,onChange:F,class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Es)])):L("",!0),(a(!0),l(ue,null,ce($.columns,d=>(a(),l("th",{key:d.key,class:G(["px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",d.align==="center"?"text-center":d.align==="right"?"text-right":"text-left",d.sortable?"cursor-pointer hover:bg-gray-100 select-none":"",d.headerClass||""]),style:He(d.width?{width:d.width}:{}),onClick:p=>d.sortable?_(d.key):null},[e("div",{class:G(["flex items-center",d.align==="center"?"justify-center":d.align==="right"?"justify-end":"justify-start"])},[e("span",null,x(d.label),1),d.sortable?(a(),l(ue,{key:0},[$.sortBy===d.key&&$.sortDirection==="asc"?(a(),de(i(os),{key:0,class:"ml-1 h-4 w-4"})):$.sortBy===d.key&&$.sortDirection==="desc"?(a(),de(i(Gt),{key:1,class:"ml-1 h-4 w-4"})):(a(),de(i(ss),{key:2,class:"ml-1 h-4 w-4 text-gray-400"}))],64)):L("",!0)],2)],14,Vs))),128))])],2),e("tbody",{class:G(N.value)},[$.loading?(a(),l("tr",Rs,[e("td",{colspan:v.value,class:"px-4 py-8 text-center"},u[0]||(u[0]=[Be('<div class="flex items-center justify-center"><svg class="animate-spin h-5 w-5 text-primary-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><span class="text-gray-500">Loading...</span></div>',1)]),8,Fs)])):$.data.length===0?(a(),l("tr",qs,[e("td",{colspan:v.value,class:"px-4 py-8 text-center text-gray-500"},x($.emptyMessage),9,Hs)])):(a(!0),l(ue,{key:2},ce($.data,(d,p)=>(a(),l("tr",{key:M(d,p),class:G(["transition-colors duration-150",$.hover?"hover:bg-gray-50":"",$.striped&&p%2===1?"bg-gray-50":"",m(d)?"bg-primary-50":""])},[$.selectable?(a(),l("td",Os,[e("input",{type:"checkbox",checked:m(d),onChange:C=>I(d),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Ws)])):L("",!0),(a(!0),l(ue,null,ce($.columns,C=>(a(),l("td",{key:C.key,class:G(["px-4 py-3",C.align==="center"?"text-center":C.align==="right"?"text-right":"text-left",j[$.size],C.cellClass||""])},[C.render?(a(),l(ue,{key:0},[typeof C.render(D(d,C.key),d,p)=="object"?(a(),de(Oe(C.render(D(d,C.key),d,p)),{key:0})):(a(),l("span",{key:1,innerHTML:C.render(D(d,C.key),d,p)},null,8,Us))],64)):(a(),l("span",Ys,x(D(d,C.key)),1))],2))),128))],2))),128))],2)],2)],2)]))}}),Ue=We("watchlist",()=>{const o=T([]),s=T(!1),r=T(null),c=T(0),h=()=>{window.dispatchEvent(new CustomEvent("watchlist-updated",{detail:{count:o.value.length}}))},w=q(()=>o.value.length),B=q(()=>o.value.length===0),N=q(()=>o.value.map(u=>u.id)),j=async(u=1,d=15,p="")=>{var k;const C=ge();if(!C.isAuthenticated)return o.value=[],{data:[],meta:{}};s.value=!0,r.value=null;try{const V=new URLSearchParams({page:u.toString(),per_page:d.toString(),...p&&{search:p}}),Z={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},oe=(k=document.querySelector('meta[name="csrf-token"]'))==null?void 0:k.getAttribute("content");oe&&(Z["X-CSRF-TOKEN"]=oe),C.token&&(Z.Authorization=`Bearer ${C.token}`);const le=await fetch(`/api/watchlist?${V}`,{headers:Z,credentials:"include"});if(!le.ok)throw new Error("Failed to fetch watchlist");const ee=await le.json();return u===1?o.value=ee.data||[]:o.value.push(...ee.data||[]),c.value=Date.now(),h(),ee}catch(V){return r.value=V instanceof Error?V.message:"Failed to fetch watchlist",console.error("Watchlist fetch error:",V),{data:[],meta:{}}}finally{s.value=!1}},v=async u=>{var p;const d=ge();if(!d.isAuthenticated)return r.value="Please sign in to add items to your watchlist",!1;_(u.id)||o.value.unshift(u);try{const C={"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"},k=(p=document.querySelector('meta[name="csrf-token"]'))==null?void 0:p.getAttribute("content");k&&(C["X-CSRF-TOKEN"]=k),d.token&&(C.Authorization=`Bearer ${d.token}`);const V=await fetch("/api/watchlist",{method:"POST",headers:C,credentials:"include",body:JSON.stringify({item_id:u.id})}),Z=await V.json();if(!V.ok)throw m(u.id),new Error(Z.message||"Failed to add item to watchlist");return r.value=null,h(),!0}catch(C){return m(u.id),r.value=C instanceof Error?C.message:"Failed to add item to watchlist",console.error("Add to watchlist error:",C),!1}},g=async u=>{var C;const d=ge();if(!d.isAuthenticated)return r.value="Please sign in to manage your watchlist",!1;const p=[...o.value];m(u);try{const k={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},V=(C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content");V&&(k["X-CSRF-TOKEN"]=V),d.token&&(k.Authorization=`Bearer ${d.token}`);const Z=await fetch(`/api/watchlist/${u}`,{method:"DELETE",headers:k,credentials:"include"}),oe=await Z.json();if(!Z.ok)throw o.value=p,new Error(oe.message||"Failed to remove item from watchlist");return r.value=null,h(),!0}catch(k){return o.value=p,r.value=k instanceof Error?k.message:"Failed to remove item from watchlist",console.error("Remove from watchlist error:",k),!1}},E=async u=>_(u.id)?await g(u.id):await v(u),D=async u=>{var p;const d=ge();if(!d.isAuthenticated)return!1;try{const C={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},k=(p=document.querySelector('meta[name="csrf-token"]'))==null?void 0:p.getAttribute("content");k&&(C["X-CSRF-TOKEN"]=k),d.token&&(C.Authorization=`Bearer ${d.token}`);const V=await fetch(`/api/watchlist/check/${u}`,{headers:C,credentials:"include"});return V.ok&&(await V.json()).in_watchlist||!1}catch(C){return console.error("Check watchlist status error:",C),!1}},M=async()=>{var d;const u=ge();if(!u.isAuthenticated)return o.value=[],0;try{const p={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},C=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");C&&(p["X-CSRF-TOKEN"]=C),u.token&&(p.Authorization=`Bearer ${u.token}`);const k=await fetch("/api/watchlist/count",{headers:p,credentials:"include"});if(!k.ok)return o.value.length;const Z=(await k.json()).count||0;return Z!==o.value.length&&await j(),Z}catch(p){return console.error("Sync watchlist count error:",p),o.value.length}},_=u=>o.value.some(d=>d.id===u),m=u=>{const d=o.value.findIndex(p=>p.id===u);d>-1&&o.value.splice(d,1)};return{items:o,isLoading:s,error:r,lastSyncTime:c,watchlistCount:w,isEmpty:B,watchlistItemIds:N,fetchWatchlist:j,addToWatchlist:v,removeFromWatchlist:g,toggleWatchlist:E,checkWatchlistStatus:D,syncWatchlistCount:M,isInWatchlist:_,clearWatchlist:()=>{o.value=[],r.value=null,c.value=0,h()},refreshWatchlist:async()=>await j(),initializeWatchlist:async()=>{ge().isAuthenticated&&await j()}}}),Qs={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"auth-modal-title",role:"dialog","aria-modal":"true"},Xs={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Gs={key:0,class:"inline-block align-bottom bg-white rounded-2xl shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full overflow-hidden"},Zs={class:"relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 px-6 py-8 text-center"},Js={class:"mb-4"},eo={class:"mx-auto w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"},to={id:"auth-modal-title",class:"text-2xl font-bold text-white mb-2"},so={class:"text-slate-300 text-sm"},oo={class:"relative bg-white"},ao={class:"px-6 py-8"},ro={key:"login",class:"space-y-6"},no={class:"relative"},lo={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},io=["disabled"],uo={class:"relative"},co={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},mo=["type","disabled"],vo={class:"flex items-center justify-between"},go={class:"flex items-center"},po={class:"text-center"},ho={class:"text-sm text-gray-600"},fo={key:0,class:"bg-green-50 border border-green-200 rounded-xl p-4"},bo={class:"flex"},yo={class:"ml-3"},xo={class:"text-sm text-green-800"},wo={key:1,class:"bg-red-50 border border-red-200 rounded-xl p-4"},_o={class:"flex"},ko={class:"ml-3"},Co={class:"text-sm text-red-800"},$o=["disabled"],Mo={key:0},jo={key:1,class:"flex items-center"},Io={key:"register",class:"space-y-6"},So={class:"grid grid-cols-2 gap-4"},Ao=["disabled"],Bo=["disabled"],To={class:"relative"},No={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},zo=["disabled"],Do={key:0},Lo={class:"relative"},Po={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Eo=["required","disabled"],Vo={class:"grid grid-cols-1 gap-4"},Ro={class:"relative"},Fo={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},qo=["type","disabled"],Ho={class:"relative"},Oo={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Wo=["type","disabled"],Uo={class:"flex items-start"},Yo={class:"flex items-center h-5"},Ko={key:1,class:"bg-green-50 border border-green-200 rounded-xl p-4"},Qo={class:"flex"},Xo={class:"ml-3"},Go={class:"text-sm text-green-800"},Zo={key:2,class:"bg-red-50 border border-red-200 rounded-xl p-4"},Jo={class:"flex"},ea={class:"ml-3"},ta={class:"text-sm text-red-800"},sa=["disabled"],oa={key:0},aa={key:1,class:"flex items-center"},ra={class:"text-center"},na={class:"text-sm text-gray-600"},la=me({__name:"AuthModal",props:{show:{type:Boolean},startWithRegister:{type:Boolean,default:!1},title:{default:""},subtitle:{default:""},closable:{type:Boolean,default:!0},closeOnBackdrop:{type:Boolean,default:!0},showPhoneField:{type:Boolean,default:!0},showSocialLogin:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!0},customBranding:{}},emits:["update:show","success","close","forgot-password","social-login"],setup(o,{expose:s,emit:r}){const c=o,h=r,w=ge(),B=Me(),N=T(c.startWithRegister),j=T(!1),v=T(!1),g=T(""),E=T(""),D=T({email:"",password:"",remember:!1}),M=T({firstName:"",lastName:"",email:"",phone:"",password:"",passwordConfirmation:"",acceptTerms:!1,acceptMarketing:!1}),_=q(()=>w.isLoading),m=q(()=>c.title?c.title:N.value?"Join Us Today":"Welcome Back"),I=q(()=>c.subtitle?c.subtitle:N.value?"Create an account to get started":"Sign in to your account to continue"),F=q(()=>M.value.firstName.trim()&&M.value.lastName.trim()&&M.value.email.trim()&&M.value.phone.trim()&&M.value.password&&M.value.passwordConfirmation&&M.value.password===M.value.passwordConfirmation&&M.value.password.length>=4&&M.value.acceptTerms),$=()=>{N.value=!N.value,p(),C(),c.autoFocus&&setTimeout(async()=>{await le()},150)},u=()=>{g.value=""},d=()=>{E.value=""},p=()=>{u(),d()},C=()=>{D.value={email:"",password:"",remember:!1},M.value={firstName:"",lastName:"",email:"",phone:"",password:"",passwordConfirmation:"",acceptTerms:!1,acceptMarketing:!1},j.value=!1,v.value=!1},k=async()=>{var ee;if(p(),!D.value.email.trim()){g.value="Email address is required.",B.error("Email address is required.");return}if(!D.value.password){g.value="Password is required.",B.error("Password is required.");return}w.setLoading(!0);try{const R=(ee=document.querySelector('meta[name="csrf-token"]'))==null?void 0:ee.getAttribute("content");if(!R){const ae="Security token not found. Please refresh the page.";g.value=ae,B.error(ae);return}const Y=await fetch("/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":R},body:JSON.stringify({username:D.value.email,password:D.value.password,remember:D.value.remember,redirect:window.location.href})});if(Y.ok){const ae=await Y.json();E.value="Login successful! Redirecting...",B.success("Login successful! Welcome back."),h("success",ae.user||{email:D.value.email}),setTimeout(()=>{h("close"),h("update:show",!1),ae.redirect?window.location.href=ae.redirect:window.location.reload()},1e3)}else{const ae=await Y.json();let z="Invalid login credentials. Please check your email and password.";if(Y.status===422&&ae.errors){const y=[];for(const P in ae.errors)y.push(...ae.errors[P]);z=y.join(" ")}else ae.message&&(z=ae.message);g.value=z,B.error(z)}}catch(R){console.error("Login error:",R);let Y="Login failed. Please try again.";R instanceof TypeError&&R.message.includes("fetch")?Y="Network error. Please check your connection and try again.":R instanceof Error&&(Y=R.message),g.value=Y,B.error(Y)}finally{w.setLoading(!1)}},V=async()=>{var ee;if(p(),!M.value.firstName.trim()){g.value="First name is required.";return}if(!M.value.lastName.trim()){g.value="Last name is required.";return}if(!M.value.email.trim()){g.value="Email address is required.";return}if(!M.value.phone.trim()){g.value="Phone number is required.";return}if(!M.value.password){g.value="Password is required.";return}if(M.value.password.length<4){g.value="Password must be at least 4 characters long.";return}if(M.value.password!==M.value.passwordConfirmation){g.value="Passwords do not match.";return}if(!M.value.acceptTerms){g.value="You must accept the terms and conditions.";return}w.setLoading(!0);try{const R=(ee=document.querySelector('meta[name="csrf-token"]'))==null?void 0:ee.getAttribute("content"),Y={name:`${M.value.firstName.trim()} ${M.value.lastName.trim()}`,email:M.value.email.trim(),phone:M.value.phone.trim(),password:M.value.password,password_confirmation:M.value.passwordConfirmation},ae=await fetch("/api/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":R||""},body:JSON.stringify(Y)}),z=await ae.json();if(!ae.ok){if(ae.status===422&&z.errors){const y=[];for(const P in z.errors)y.push(...z.errors[P]);g.value=y.join(" ")}else g.value=z.message||"Registration failed. Please try again.";return}E.value="Account created successfully! Logging you in...",B.success("Account created successfully! Logging you in..."),D.value.email=M.value.email,D.value.password=M.value.password,await k()}catch(R){console.error("Registration error:",R);let Y="Registration failed. Please try again.";R instanceof TypeError&&R.message.includes("fetch")?Y="Network error. Please check your connection and try again.":R instanceof Error&&(Y=R.message),g.value=Y,B.error(Y)}finally{w.setLoading(!1)}},Z=()=>{p(),C(),N.value=c.startWithRegister,h("close"),h("update:show",!1)},oe=()=>{c.closeOnBackdrop&&Z()},le=async()=>{if(!c.autoFocus)return;await jt();const ee=N.value?"register-firstName":"login-email",R=document.getElementById(ee);R&&R.focus()};return _e(()=>c.startWithRegister,ee=>{N.value=ee}),_e(()=>c.show,async ee=>{ee&&(N.value=c.startWithRegister,u(),C(),c.autoFocus&&setTimeout(async()=>{await le()},200))}),s({toggleForm:$,clearError:u,resetForms:C,autoFocusFirstInput:le}),(ee,R)=>(a(),de(Wt,{to:"body"},[A(Ge,{"enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:O(()=>[ee.show?(a(),l("div",Qs,[e("div",Xs,[e("div",{class:"fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity backdrop-blur-sm",onClick:oe}),A(Ge,{"enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to-class":"opacity-100 translate-y-0 sm:scale-100","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100 translate-y-0 sm:scale-100","leave-to-class":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:O(()=>[ee.show?(a(),l("div",Gs,[e("div",Zs,[ee.closable?(a(),l("button",{key:0,type:"button",class:"absolute top-4 right-4 text-white/80 hover:text-white transition-colors duration-200 rounded-full p-2 hover:bg-white/10",onClick:Z},[A(i(Ut),{class:"h-5 w-5"})])):L("",!0),e("div",Js,[e("div",eo,[A(i(rs),{class:"h-8 w-8 text-white"})])]),e("h2",to,x(m.value),1),e("p",so,x(I.value),1)]),e("div",oo,[e("div",ao,[A(Ge,{mode:"out-in","enter-active-class":"duration-300 ease-out","enter-from-class":"opacity-0 translate-x-4","enter-to-class":"opacity-100 translate-x-0","leave-active-class":"duration-200 ease-in","leave-from-class":"opacity-100 translate-x-0","leave-to-class":"opacity-0 -translate-x-4"},{default:O(()=>[N.value?N.value?(a(),l("div",Io,[e("form",{onSubmit:xe(V,["prevent"]),class:"space-y-5"},[e("div",So,[e("div",null,[R[21]||(R[21]=e("label",{for:"register-firstName",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," First Name ",-1)),pe(e("input",{id:"register-firstName","onUpdate:modelValue":R[6]||(R[6]=Y=>M.value.firstName=Y),type:"text",required:"",class:"block w-full px-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"First name",disabled:_.value},null,8,Ao),[[Ie,M.value.firstName]])]),e("div",null,[R[22]||(R[22]=e("label",{for:"register-lastName",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Last Name ",-1)),pe(e("input",{id:"register-lastName","onUpdate:modelValue":R[7]||(R[7]=Y=>M.value.lastName=Y),type:"text",required:"",class:"block w-full px-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Last name",disabled:_.value},null,8,Bo),[[Ie,M.value.lastName]])])]),e("div",null,[R[23]||(R[23]=e("label",{for:"register-email",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Email Address ",-1)),e("div",To,[e("div",No,[A(i(bt),{class:"h-5 w-5 text-gray-400"})]),pe(e("input",{id:"register-email","onUpdate:modelValue":R[8]||(R[8]=Y=>M.value.email=Y),type:"email",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your email",disabled:_.value},null,8,zo),[[Ie,M.value.email]])])]),ee.showPhoneField?(a(),l("div",Do,[R[24]||(R[24]=e("label",{for:"register-phone",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Phone Number ",-1)),e("div",Lo,[e("div",Po,[A(i(as),{class:"h-5 w-5 text-gray-400"})]),pe(e("input",{id:"register-phone","onUpdate:modelValue":R[9]||(R[9]=Y=>M.value.phone=Y),type:"tel",required:!M.value.email,class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your phone number",disabled:_.value},null,8,Eo),[[Ie,M.value.phone]])])])):L("",!0),e("div",Vo,[e("div",null,[R[25]||(R[25]=e("label",{for:"register-password",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Password ",-1)),e("div",Ro,[e("div",Fo,[A(i(Ke),{class:"h-5 w-5 text-gray-400"})]),pe(e("input",{id:"register-password","onUpdate:modelValue":R[10]||(R[10]=Y=>M.value.password=Y),type:j.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Create a password",disabled:_.value},null,8,qo),[[Ze,M.value.password]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:R[11]||(R[11]=Y=>j.value=!j.value)},[j.value?(a(),de(i(Je),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(a(),de(i(et),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])]),e("div",null,[R[26]||(R[26]=e("label",{for:"register-password-confirmation",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Confirm Password ",-1)),e("div",Ho,[e("div",Oo,[A(i(Ke),{class:"h-5 w-5 text-gray-400"})]),pe(e("input",{id:"register-password-confirmation","onUpdate:modelValue":R[12]||(R[12]=Y=>M.value.passwordConfirmation=Y),type:v.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Confirm your password",disabled:_.value},null,8,Wo),[[Ze,M.value.passwordConfirmation]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:R[13]||(R[13]=Y=>v.value=!v.value)},[v.value?(a(),de(i(Je),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(a(),de(i(et),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])])]),e("div",Uo,[e("div",Yo,[pe(e("input",{id:"accept-terms","onUpdate:modelValue":R[14]||(R[14]=Y=>M.value.acceptTerms=Y),type:"checkbox",required:"",class:"h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"},null,512),[[Ee,M.value.acceptTerms]])]),R[27]||(R[27]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"accept-terms",class:"text-gray-700"},[W(" I agree to the "),e("button",{type:"button",class:"text-slate-600 hover:text-slate-500 font-medium"}," Terms of Service "),W(" and "),e("button",{type:"button",class:"text-slate-600 hover:text-slate-500 font-medium"}," Privacy Policy ")])],-1))]),E.value?(a(),l("div",Ko,[e("div",Qo,[A(i(ft),{class:"h-5 w-5 text-green-400"}),e("div",Xo,[e("p",Go,x(E.value),1)])])])):L("",!0),g.value?(a(),l("div",Zo,[e("div",Jo,[A(i(yt),{class:"h-5 w-5 text-red-400"}),e("div",ea,[e("p",ta,x(g.value),1)])])])):L("",!0),e("button",{type:"submit",disabled:_.value||!F.value,class:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-slate-700 to-slate-900 hover:from-slate-800 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[_.value?(a(),l("span",aa,R[28]||(R[28]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),W(" Creating account... ")]))):(a(),l("span",oa,"Create Account"))],8,sa)],32),e("div",ra,[e("p",na,[R[29]||(R[29]=W(" Already have an account? ")),e("button",{type:"button",class:"font-medium text-slate-600 hover:text-slate-500 transition-colors duration-200",onClick:R[15]||(R[15]=Y=>N.value=!1)}," Sign in here ")])])])):L("",!0):(a(),l("div",ro,[e("form",{onSubmit:xe(k,["prevent"]),class:"space-y-5"},[e("div",null,[R[16]||(R[16]=e("label",{for:"login-email",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Email Address ",-1)),e("div",no,[e("div",lo,[A(i(bt),{class:"h-5 w-5 text-gray-400"})]),pe(e("input",{id:"login-email","onUpdate:modelValue":R[0]||(R[0]=Y=>D.value.email=Y),type:"email",required:"",class:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your email",disabled:_.value},null,8,io),[[Ie,D.value.email]])])]),e("div",null,[R[17]||(R[17]=e("label",{for:"login-password",class:"block text-sm font-medium text-gray-700 mb-2 text-left"}," Password ",-1)),e("div",uo,[e("div",co,[A(i(Ke),{class:"h-5 w-5 text-gray-400"})]),pe(e("input",{id:"login-password","onUpdate:modelValue":R[1]||(R[1]=Y=>D.value.password=Y),type:j.value?"text":"password",required:"",class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors duration-200",placeholder:"Enter your password",disabled:_.value},null,8,mo),[[Ze,D.value.password]]),e("button",{type:"button",class:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:R[2]||(R[2]=Y=>j.value=!j.value)},[j.value?(a(),de(i(Je),{key:1,class:"h-5 w-5 text-gray-400 hover:text-gray-600"})):(a(),de(i(et),{key:0,class:"h-5 w-5 text-gray-400 hover:text-gray-600"}))])])]),e("div",vo,[e("div",go,[pe(e("input",{id:"remember-me","onUpdate:modelValue":R[3]||(R[3]=Y=>D.value.remember=Y),type:"checkbox",class:"h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"},null,512),[[Ee,D.value.remember]]),R[18]||(R[18]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-700"}," Remember me ",-1))]),e("button",{type:"button",class:"text-sm text-slate-600 hover:text-slate-500 font-medium",onClick:R[4]||(R[4]=Y=>ee.$emit("forgot-password"))}," Forgot password? ")]),e("div",po,[e("p",ho,[R[19]||(R[19]=W(" Don't have an account? ")),e("button",{type:"button",class:"font-medium text-slate-600 hover:text-slate-500 transition-colors duration-200",onClick:R[5]||(R[5]=Y=>N.value=!0)}," Create one here ")])]),E.value?(a(),l("div",fo,[e("div",bo,[A(i(ft),{class:"h-5 w-5 text-green-400"}),e("div",yo,[e("p",xo,x(E.value),1)])])])):L("",!0),g.value?(a(),l("div",wo,[e("div",_o,[A(i(yt),{class:"h-5 w-5 text-red-400"}),e("div",ko,[e("p",Co,x(g.value),1)])])])):L("",!0),e("button",{type:"submit",disabled:_.value||!D.value.email||!D.value.password,class:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-slate-700 to-slate-900 hover:from-slate-800 hover:to-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[_.value?(a(),l("span",jo,R[20]||(R[20]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),W(" Signing in... ")]))):(a(),l("span",Mo,"Sign In"))],8,$o)],32)]))]),_:1})])])])):L("",!0)]),_:1})])])):L("",!0)]),_:1})]))}}),Xe=ke(la,[["__scopeId","data-v-da7d539c"]]),ia={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},da=me({__name:"WatchlistNavIcon",setup(o){const s=Ue(),r=ge(),c=Ne(),h=T(!1),w=q(()=>{const j=window.location.pathname;return j==="/cart"||j.startsWith("/spa")||j.startsWith("/home-vue")||j.startsWith("/bid-dashboard")}),B=j=>{if(!r.isAuthenticated){j.preventDefault(),h.value=!0;return}w.value&&(j.preventDefault(),c.push("/bid-dashboard"))},N=async j=>{h.value=!1,await s.initializeWatchlist(),w.value?c.push("/bid-dashboard"):window.location.href="/bid-dashboard"};return(j,v)=>(a(),l(ue,null,[(a(),de(Oe(w.value?"router-link":"a"),{to:w.value&&i(r).isAuthenticated?"/bid-dashboard":void 0,href:w.value||!i(r).isAuthenticated?void 0:"/bid-dashboard",onClick:B,class:"relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200",title:i(r).isAuthenticated?i(s).watchlistCount>0?`${i(s).watchlistCount} items in watchlist`:"View your watchlist":"Sign in to view your watchlist"},{default:O(()=>[v[1]||(v[1]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})],-1)),i(r).isAuthenticated&&i(s).watchlistCount>0?(a(),l("span",ia,x(i(s).watchlistCount>9?"9+":i(s).watchlistCount),1)):L("",!0)]),_:1,__:[1]},8,["to","href","title"])),A(Xe,{show:h.value,"onUpdate:show":v[0]||(v[0]=g=>h.value=g),title:"Sign in to view your watchlist",subtitle:"Track your favorite auction items and manage your bidding activity.",onSuccess:N},null,8,["show"])],64))}}),ua=["aria-selected","aria-controls","id","disabled","onClick","onKeydown"],ca=["onClick","aria-label"],ma=["id","aria-labelledby","tabindex"],va=["innerHTML"],ga=me({__name:"Tabs",props:{tabs:{},modelValue:{default:0},variant:{default:"default"},size:{default:"md"},vertical:{type:Boolean,default:!1},centered:{type:Boolean,default:!1},addable:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1}},emits:["update:modelValue","tab-change","tab-close","tab-add"],setup(o,{expose:s,emit:r}){const c=o,h=r,w=T(c.modelValue);_e(()=>c.modelValue,u=>{w.value=u}),_e(w,u=>{h("update:modelValue",u),h("tab-change",u,c.tabs[u])});const B=q(()=>{const u=["tabs-container"];return c.vertical&&u.push("flex"),u.join(" ")}),N=q(()=>{const u=["flex","tab-list"];c.vertical?u.push("flex-col","space-y-1","mr-4"):u.push("space-x-1"),c.centered&&!c.vertical&&u.push("justify-center");const d={default:["border-b","border-gray-200"],pills:[],underline:["border-b","border-gray-200"],bordered:["border","border-gray-200","rounded-lg","p-1","bg-gray-50"]};return[...u,...d[c.variant]].join(" ")}),j=u=>{const d=w.value===u,p=c.tabs[u],C=["flex","items-center","space-x-2","font-medium","transition-all","duration-200","focus:outline-none"];c.variant!=="underline"&&C.push("focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500");const k={sm:["px-3","py-1.5","text-sm"],md:["px-4","py-2","text-sm"],lg:["px-6","py-3","text-base"]},V={default:d?["text-indigo-600","border-b-2","border-indigo-600"]:["text-gray-500","hover:text-gray-700","border-b-2","border-transparent"],pills:d?["bg-indigo-100","text-indigo-700","rounded-lg"]:["text-gray-500","hover:text-gray-700","hover:bg-gray-100","rounded-lg"],underline:d?["text-indigo-600","border-b-2","border-indigo-600"]:["text-gray-500","hover:text-gray-700","border-b-2","border-transparent"],bordered:d?["bg-white","text-indigo-700","shadow-sm","rounded-md"]:["text-gray-500","hover:text-gray-700","hover:bg-white","rounded-md"]},Z=p.disabled?["opacity-50","cursor-not-allowed","pointer-events-none"]:["cursor-pointer"];return[...C,...k[c.size],...V[c.variant],...Z].join(" ")},v=u=>{const d=w.value===u,p=["w-4","h-4"];return d?p.push("text-current"):p.push("text-gray-400"),p.join(" ")},g=u=>{const d=w.value===u,p=["inline-flex","items-center","justify-center","px-2","py-0.5","rounded-full","text-xs","font-medium","min-w-[1.25rem]","h-5"];return d?p.push("bg-indigo-100","text-indigo-800"):p.push("bg-gray-100","text-gray-600"),p.join(" ")},E=u=>{const d=w.value===u,p=["ml-2","p-0.5","rounded","hover:bg-gray-200","focus:outline-none","focus:ring-1","focus:ring-gray-400"];return d?p.push("text-indigo-500"):p.push("text-gray-400"),p.join(" ")},D=q(()=>["flex","items-center","justify-center","w-8","h-8","text-gray-400","hover:text-gray-600","hover:bg-gray-100","rounded","transition-colors","duration-200","focus:outline-none","focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500"].join(" ")),M=q(()=>{const u=["tab-panels"];return c.vertical?u.push("flex-1"):u.push("mt-4"),u.join(" ")}),_=q(()=>["tab-panel","focus:outline-none"].join(" ")),m=u=>{var d;(d=c.tabs[u])!=null&&d.disabled||(w.value=u)},I=u=>{const d=c.tabs[u];if(h("tab-close",u,d),w.value===u){const p=u>0?u-1:0;c.tabs.length>1&&jt(()=>{w.value=Math.min(p,c.tabs.length-2)})}else w.value>u&&w.value--},F=()=>{h("tab-add")},$=(u,d)=>{const{key:p}=u;if(p==="ArrowLeft"||p==="ArrowUp"){u.preventDefault();const C=d>0?d-1:c.tabs.length-1;m(C)}else if(p==="ArrowRight"||p==="ArrowDown"){u.preventDefault();const C=d<c.tabs.length-1?d+1:0;m(C)}else p==="Home"?(u.preventDefault(),m(0)):p==="End"&&(u.preventDefault(),m(c.tabs.length-1))};return s({selectTab:m,closeTab:I,addTab:F,activeTab:()=>w.value}),(u,d)=>(a(),l("div",{class:G(B.value)},[e("div",{class:G(N.value),role:"tablist"},[(a(!0),l(ue,null,ce(u.tabs,(p,C)=>(a(),l("button",{key:p.key||C,class:G(j(C)),"aria-selected":w.value===C,"aria-controls":`tabpanel-${p.key||C}`,id:`tab-${p.key||C}`,role:"tab",disabled:p.disabled,onClick:k=>m(C),onKeydown:k=>$(k,C)},[p.icon?(a(),de(Oe(p.icon),{key:0,class:G(v(C))},null,8,["class"])):L("",!0),e("span",null,x(p.label),1),p.badge!==void 0?(a(),l("span",{key:1,class:G(g(C))},x(p.badge),3)):L("",!0),p.closable?(a(),l("button",{key:2,class:G(E(C)),onClick:xe(k=>I(C),["stop"]),"aria-label":`Close ${p.label} tab`},d[0]||(d[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),10,ca)):L("",!0)],42,ua))),128)),u.addable?(a(),l("button",{key:0,class:G(D.value),onClick:F,"aria-label":"Add new tab"},d[1]||(d[1]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1)]),2)):L("",!0)],2),e("div",{class:G(M.value)},[(a(!0),l(ue,null,ce(u.tabs,(p,C)=>pe((a(),l("div",{key:`panel-${p.key||C}`,class:G(_.value),id:`tabpanel-${p.key||C}`,"aria-labelledby":`tab-${p.key||C}`,role:"tabpanel",tabindex:w.value===C?0:-1},[qe(u.$slots,p.key||`tab-${C}`,{tab:p,index:C,active:w.value===C},()=>[p.content?(a(),l("div",{key:0,innerHTML:p.content},null,8,va)):L("",!0)],!0)],10,ma)),[[Yt,w.value===C]])),128))],2)],2))}}),pa=ke(ga,[["__scopeId","data-v-c55ef745"]]),ha={key:0,class:"banner-section"},fa={class:"banner-container relative overflow-hidden"},ba={class:"relative"},ya={href:"/register-bid",class:"block w-full"},xa=["src","alt"],wa={key:0,class:"absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center"},_a={class:"container mx-auto px-4"},ka={class:"grid grid-cols-1 md:grid-cols-3 gap-4 items-center text-white"},Ca={class:"text-center"},$a={class:"countdown-timer"},Ma={class:"grid grid-cols-4 gap-2 text-center"},ja={class:"countdown-item"},Ia={class:"text-3xl font-bold"},Sa={class:"countdown-item"},Aa={class:"text-3xl font-bold"},Ba={class:"countdown-item"},Ta={class:"text-3xl font-bold"},Na={class:"countdown-item"},za={class:"text-3xl font-bold"},Da={class:"text-center md:text-right"},La={class:"flex items-center justify-center md:justify-end space-x-2"},Pa={class:"text-sm"},Ea={key:1,class:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6"},Va={class:"text-white text-center"},Ra={class:"flex items-center justify-center space-x-2"},Fa={key:0,class:"absolute bottom-4 left-1/2 transform -translate-x-1/2"},qa={class:"flex space-x-2"},Ha=["onClick"],Oa=me({__name:"Banner",props:{adverts:{}},setup(o){const s=o,r=T(new Date);let c=null;const h=T(0),w=q(()=>{if(!s.adverts||s.adverts.length===0)return null;const v=h.value<s.adverts.length?h.value:0;return s.adverts[v]}),B=q(()=>{if(!w.value||!w.value.date_to)return{days:0,hours:0,minutes:0,seconds:0};const g=new Date(w.value.date_to).getTime()-r.value.getTime();if(g<=0)return{days:0,hours:0,minutes:0,seconds:0};const E=Math.floor(g/(1e3*60*60*24)),D=Math.floor(g%(1e3*60*60*24)/(1e3*60*60)),M=Math.floor(g%(1e3*60*60)/(1e3*60)),_=Math.floor(g%(1e3*60)/1e3);return{days:E,hours:D,minutes:M,seconds:_}}),N=()=>{r.value=new Date},j=v=>{v>=0&&v<s.adverts.length&&(h.value=v)};return $e(()=>{c=setInterval(N,1e3)}),vt(()=>{c&&clearInterval(c)}),(v,g)=>v.adverts&&v.adverts.length>0&&w.value?(a(),l("div",ha,[e("div",fa,[e("div",ba,[e("a",ya,[e("img",{src:w.value.image,alt:w.value.description||"Advertisement",class:"w-full h-auto object-cover",style:{"max-height":"400px"}},null,8,xa)]),w.value.date_to?(a(),l("div",wa,[e("div",_a,[e("div",ka,[g[5]||(g[5]=e("div",{class:"hidden md:block"},null,-1)),e("div",Ca,[e("div",$a,[e("div",Ma,[e("div",ja,[e("div",Ia,x(B.value.days),1),g[0]||(g[0]=e("div",{class:"text-sm uppercase tracking-wide"},"Days",-1))]),e("div",Sa,[e("div",Aa,x(B.value.hours),1),g[1]||(g[1]=e("div",{class:"text-sm uppercase tracking-wide"},"Hours",-1))]),e("div",Ba,[e("div",Ta,x(B.value.minutes),1),g[2]||(g[2]=e("div",{class:"text-sm uppercase tracking-wide"},"Minutes",-1))]),e("div",Na,[e("div",za,x(B.value.seconds),1),g[3]||(g[3]=e("div",{class:"text-sm uppercase tracking-wide"},"Seconds",-1))])])])]),e("div",Da,[e("div",La,[g[4]||(g[4]=e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})],-1)),e("span",Pa,x(w.value.description),1)])])])])])):w.value.description?(a(),l("div",Ea,[e("div",Va,[e("div",Ra,[g[6]||(g[6]=e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})],-1)),e("span",null,x(w.value.description),1)])])])):L("",!0)]),v.adverts.length>1?(a(),l("div",Fa,[e("div",qa,[(a(!0),l(ue,null,ce(v.adverts,(E,D)=>(a(),l("button",{key:E.id,onClick:M=>j(D),class:G(["w-3 h-3 rounded-full transition-all duration-300",h.value===D?"bg-white":"bg-white bg-opacity-50 hover:bg-opacity-75"])},null,10,Ha))),128))])])):L("",!0)])])):L("",!0)}}),Lt=ke(Oa,[["__scopeId","data-v-4bed1f7b"]]),Wa={class:"relative"},Ua={key:0,class:"flex justify-center items-center mt-6 space-x-4"},Ya=["disabled"],Ka={class:"flex space-x-2"},Qa=["onClick","aria-label"],Xa=["disabled"],Ga={key:1,class:"text-center mt-2 text-sm text-gray-500"},Za=50,Ja=me({__name:"HorizontalSlider",props:{items:{},autoPlay:{type:Boolean,default:!1},autoPlayInterval:{default:5e3},showCounter:{type:Boolean,default:!0},containerClasses:{default:""},navigationButtonClasses:{default:""},dotClasses:{default:""},keyExtractor:{type:Function,default:(o,s)=>s}},setup(o){const s=o,r=T(),c=T(0),h=T(null),w=T(0),B=T(0),N=(u,d)=>s.keyExtractor(u,d),j=u=>{u>=0&&u<s.items.length&&(c.value=u,M())},v=()=>{c.value<s.items.length-1&&(c.value++,M())},g=()=>{c.value>0&&(c.value--,M())},E=()=>{!s.autoPlay||s.items.length<=1||(h.value=setInterval(()=>{c.value<s.items.length-1?c.value++:c.value=0},s.autoPlayInterval))},D=()=>{h.value&&(clearInterval(h.value),h.value=null)},M=()=>{s.autoPlay&&(D(),E())},_=u=>{w.value=u.touches[0].clientX},m=u=>{u.preventDefault()},I=u=>{B.value=u.changedTouches[0].clientX,F()},F=()=>{const u=w.value-B.value;Math.abs(u)>Za&&(u>0?v():g())},$=u=>{switch(u.key){case"ArrowLeft":u.preventDefault(),g();break;case"ArrowRight":u.preventDefault(),v();break;case"Home":u.preventDefault(),j(0);break;case"End":u.preventDefault(),j(s.items.length-1);break}};return _e(()=>s.items,()=>{c.value=0,M()},{deep:!0}),$e(()=>{s.autoPlay&&E()}),vt(()=>{D()}),(u,d)=>(a(),l("div",Wa,[e("div",{ref_key:"sliderContainer",ref:r,class:G(["overflow-hidden focus:outline-none",u.containerClasses]),tabindex:"0",onKeydown:$,onTouchstart:_,onTouchmove:m,onTouchend:I},[e("div",{class:"flex transition-transform duration-300 ease-in-out",style:He({transform:`translateX(-${c.value*100}%)`})},[(a(!0),l(ue,null,ce(u.items,(p,C)=>(a(),l("div",{key:N(p,C),class:"w-full flex-shrink-0"},[qe(u.$slots,"default",{item:p,index:C,isActive:C===c.value},void 0,!0)]))),128))],4)],34),u.items.length>1?(a(),l("div",Ua,[e("button",{onClick:g,disabled:c.value===0,class:G(["flex items-center justify-center w-10 h-10 rounded-full bg-white border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500",u.navigationButtonClasses]),"aria-label":"Previous item"},[A(i(Zt),{class:"h-5 w-5 text-gray-600"})],10,Ya),e("div",Ka,[(a(!0),l(ue,null,ce(u.items,(p,C)=>(a(),l("button",{key:`dot-${N(p,C)}`,onClick:k=>j(C),class:G(["w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",[C===c.value?"bg-blue-600 scale-110":"bg-gray-300 hover:bg-gray-400",u.dotClasses]]),"aria-label":`Go to item ${C+1}`},null,10,Qa))),128))]),e("button",{onClick:v,disabled:c.value===u.items.length-1,class:G(["flex items-center justify-center w-10 h-10 rounded-full bg-white border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500",u.navigationButtonClasses]),"aria-label":"Next item"},[A(i(Jt),{class:"h-5 w-5 text-gray-600"})],10,Xa)])):L("",!0),u.showCounter&&u.items.length>1?(a(),l("div",Ga,x(c.value+1)+" of "+x(u.items.length),1)):L("",!0)]))}}),er=ke(Ja,[["__scopeId","data-v-bf9f80b5"]]),Pe=(o,s="MWK",r="en-MW",c={})=>{const{showDecimals:h=!0,compact:w=!1}=c;if(o===0)return s==="MWK"?"MK 0":`${s} 0`;if(s==="MWK"||s==="MK"){const N={minimumFractionDigits:h?2:0,maximumFractionDigits:h?2:0};return w&&o>=1e6&&(N.notation="compact",N.compactDisplay="short"),`MK ${new Intl.NumberFormat("en-MW",N).format(o)}`}const B={style:"currency",currency:s,minimumFractionDigits:h?2:0,maximumFractionDigits:h?2:0};return w&&o>=1e6&&(B.notation="compact",B.compactDisplay="short"),new Intl.NumberFormat(r,B).format(o)},Pt=(o,s={year:"numeric",month:"short",day:"numeric"},r="en-US")=>{const c=typeof o=="string"?new Date(o):o;return new Intl.DateTimeFormat(r,s).format(c)},ut=(o,s="en-US")=>{const r=typeof o=="string"?new Date(o):o,h=Math.floor((new Date().getTime()-r.getTime())/1e3),w=new Intl.RelativeTimeFormat(s,{numeric:"auto"});return h<60?w.format(-h,"second"):h<3600?w.format(-Math.floor(h/60),"minute"):h<86400?w.format(-Math.floor(h/3600),"hour"):h<2592e3?w.format(-Math.floor(h/86400),"day"):h<31536e3?w.format(-Math.floor(h/2592e3),"month"):w.format(-Math.floor(h/31536e3),"year")},kt=(o,s="MWK")=>Pe(o,s),tr=o=>{const s=new Date,r=o.getTime()-s.getTime();if(r<=0)return"Ended";const c=Math.floor(r/(1e3*60*60*24)),h=Math.floor(r%(1e3*60*60*24)/(1e3*60*60)),w=Math.floor(r%(1e3*60*60)/(1e3*60));return c>0?`${c}d ${h}h`:h>0?`${h}h ${w}m`:`${w}m`},pt="vertigo_cart_state",sr="vertigo_cart_sync",Et=o=>({id:o.id,name:o.name,title:o.title,description:o.description,target_amount:o.target_amount,bid_amount:o.bid_amount,auction_id:o.auction_id,auction_type_id:o.auction_type_id,status:o.status,type:o.type,cropped:o.cropped,image:o.image,code:o.code,reference_number:o.reference_number,date_from:o.date_from,date_to:o.date_to,closed_by:o.closed_by,branch_id:o.branch_id,created_at:o.created_at,updated_at:o.updated_at,quantity:o.quantity,optimistic:o.optimistic,lastUpdated:o.lastUpdated}),Le=(o,s)=>{try{const r={items:o.map(Et),lastSyncTime:s,version:"2.0"};localStorage.setItem(pt,JSON.stringify(r))}catch(r){console.warn("Failed to save cart to localStorage:",r)}},or=()=>{try{const o=localStorage.getItem(pt);if(o){const s=JSON.parse(o);if(s.version==="2.0")return s}}catch(o){console.warn("Failed to load cart from localStorage:",o)}return null},st=()=>{try{localStorage.removeItem(pt)}catch(o){console.warn("Failed to clear cart storage:",o)}},Ae=We("cart",()=>{const o=T([]),s=T(!1),r=T(null),c=T(Date.now()),h=T(0),w=T(navigator.onLine);let B=null;typeof BroadcastChannel<"u"&&(B=new BroadcastChannel(sr),B.onmessage=P=>{P.data.type==="CART_UPDATED"&&P.data.timestamp>c.value&&(o.value=P.data.items,c.value=P.data.timestamp,v())});const N=()=>{w.value=navigator.onLine,w.value&&h.value>0&&g()};window.addEventListener("online",N),window.addEventListener("offline",N),_e(o,P=>{Y(P,c.value),j(P)},{deep:!0});const j=P=>{if(B){const H=Date.now();c.value=H;const K=P.map(Et);B.postMessage({type:"CART_UPDATED",items:K,timestamp:H})}},v=()=>{window.dispatchEvent(new CustomEvent("cart-updated",{detail:{count:o.value.length}}))},g=async()=>{if(w.value)try{await V(),h.value=0}catch{h.value++,console.warn("Cart sync failed, will retry when online")}},E=q(()=>o.value.length),D=q(()=>o.value.reduce((P,H)=>{const K=H.target_amount||0,J=H.quantity||1;return P+K*J},0)),M=q(()=>o.value.length===0),_=q(()=>o.value.filter(P=>{var H;return((H=P.auction_type)==null?void 0:H.type)==="cash"})),m=q(()=>o.value.filter(P=>{var H;return((H=P.auction_type)==null?void 0:H.type)==="online"})),I=q(()=>o.value.filter(P=>{var H;return((H=P.auction_type)==null?void 0:H.type)==="live"})),F=async(P,H=!1)=>{s.value=!0,r.value=null;try{if(H)return window.location.href=`/add-to-cart/${P.id}?checkout=true`,!0;if(C(P.id))return r.value="Item is already in cart",!1;const K={...P,quantity:1,optimistic:!0,lastUpdated:Date.now()};if(o.value.push(K),v(),!w.value)return h.value++,!0;const J=await ye.post(`/api/cart/add/${P.id}`,{quantity:1,source:"frontend"});if(J.data.success){const ie=o.value.findIndex(fe=>fe.id===P.id);return ie>-1&&(o.value[ie].optimistic=!1),Le(o.value,c.value),v(),!0}else{const ie=o.value.findIndex(fe=>fe.id===P.id&&fe.optimistic);return ie>-1&&(o.value.splice(ie,1),v()),r.value=J.data.message||"Failed to add item to cart",!1}}catch(K){const J=o.value.findIndex(ie=>ie.id===P.id&&ie.optimistic);return J>-1&&(o.value.splice(J,1),v()),w.value?r.value="Failed to add item to cart":(r.value="No internet connection. Item will be added when connection is restored.",h.value++),console.error("Error adding to cart:",K),!1}finally{s.value=!1}},$=async P=>{s.value=!0,r.value=null;try{const H=o.value.findIndex(ie=>ie.id===P.id);if(H===-1)return r.value="Item not found in cart",!1;const K={...o.value[H]};if(o.value.splice(H,1),v(),!w.value)return h.value++,!0;const J=await ye.delete(`/api/cart/remove/${P.id}`);return J.data.success?(Le(o.value,c.value),v(),!0):(o.value.splice(H,0,K),v(),r.value=J.data.message||"Failed to remove item from cart",!1)}catch(H){if(o.value.findIndex(J=>J.id===P.id)===-1){const J={...P,quantity:1};o.value.push(J),v()}return w.value?r.value="Failed to remove item from cart":(r.value="No internet connection. Item will be removed when connection is restored.",h.value++),console.error("Error removing from cart:",H),!1}finally{s.value=!1}},u=async(P,H)=>{const K=o.value.find(ie=>ie.id===P);if(!K)return r.value="Item not found in cart",!1;if(H<=0)return await $(K);const J=K.quantity||1;if(K.quantity=H,K.lastUpdated=Date.now(),v(),!w.value)return h.value++,!0;try{const ie=await ye.put(`/api/cart/update/${P}`,{quantity:H});return ie.data.success?(Le(o.value,c.value),v(),!0):(K.quantity=J,v(),r.value=ie.data.message||"Failed to update item quantity",!1)}catch(ie){return K.quantity=J,v(),w.value?r.value="Failed to update item quantity":(r.value="No internet connection. Quantity will be updated when connection is restored.",h.value++),console.error("Error updating quantity:",ie),!1}},d=async(P=!0)=>{try{return P&&w.value&&!(await ye.delete("/api/cart/clear")).data.success?(r.value="Failed to clear cart on server",!1):(o.value=[],r.value=null,st(),v(),!0)}catch(H){return w.value?(r.value="Failed to clear cart",console.error("Error clearing cart:",H),!1):(o.value=[],st(),v(),h.value++,!0)}},p=async()=>{const P=[];if(o.value.length===0)return{valid:!0,issues:[]};try{const H=o.value.map(ie=>ie.id),K=await ye.post("/api/cart/validate",{item_ids:H});K.data.issues&&P.push(...K.data.issues);const J=K.data.unavailable_items||[];return J.length>0&&(o.value=o.value.filter(ie=>!J.includes(ie.id)),v(),P.push(`${J.length} item(s) are no longer available and have been removed from your cart.`)),{valid:P.length===0,issues:P}}catch(H){return console.error("Cart validation error:",H),{valid:!0,issues:[]}}},C=P=>o.value.some(H=>H.id===P),k=P=>{if(P&&P.length>0)o.value=P.map(H=>({...H,quantity:H.quantity||1,lastUpdated:Date.now()})),c.value=Date.now(),Le(o.value,c.value);else{const H=or();H&&H.items.length>0&&(o.value=H.items,c.value=H.lastSyncTime,w.value&&p().then(({valid:K,issues:J})=>{!K&&J.length>0&&console.warn("Cart validation issues:",J)}))}v()},V=async(P=!0)=>{if(!w.value&&P)return console.warn("Cannot fetch cart while offline"),!1;s.value=!0,r.value=null;try{const H=await ye.get("/api/cart");return H.data.success&&H.data.items?(o.value=H.data.items.map(K=>({id:K.id,name:K.name,target_amount:K.target_amount,quantity:K.quantity||1,auction_type:K.auction_type,image:K.image,cropped:K.image,lastUpdated:Date.now()})),c.value=Date.now(),Le(o.value,c.value)):(o.value=[],st()),v(),h.value=0,!0}catch(H){return r.value="Failed to fetch cart",console.error("Error fetching cart:",H),P&&h.value<3&&(h.value++,setTimeout(()=>{V(!1)},Math.pow(2,h.value)*1e3)),!1}finally{s.value=!1}},Z=P=>!P||P<=0?"MK 0":`MK ${P.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,oe=P=>{const H=P.target_amount||0,K=P.quantity||1;return H*K},le=()=>{o.value=[],s.value=!1,r.value=null},ee=(P,H={})=>{try{const K={event:P,timestamp:Date.now(),cart_count:o.value.length,cart_total:D.value,...H};typeof gtag<"u"&&gtag("event",P,{event_category:"cart",event_label:H.item_name||"",value:H.item_price||0});const J=JSON.parse(localStorage.getItem("cart_analytics")||"[]");J.push(K),J.length>100&&J.splice(0,J.length-100),localStorage.setItem("cart_analytics",JSON.stringify(J))}catch(K){console.warn("Failed to track cart event:",K)}};let R=null;const Y=(P,H)=>{R&&clearTimeout(R),R=setTimeout(()=>{Le(P,H)},300)};return{items:o,loading:s,error:r,lastSyncTime:c,retryCount:h,isOnline:w,cartCount:E,cartTotal:D,isEmpty:M,cashItems:_,onlineItems:m,liveItems:I,addToCart:F,removeFromCart:$,updateQuantity:u,clearCart:d,isInCart:C,initializeCart:k,fetchCart:V,validateCartItems:p,syncWithServer:g,formatCurrency:Z,getItemSubtotal:oe,reset:le,setLastShoppingPage:P=>{try{!P.includes("/cart")&&!P.includes("/checkout")&&sessionStorage.setItem("lastShoppingPage",P)}catch(H){console.warn("Could not save last shopping page:",H)}},getLastShoppingPage:()=>{try{return sessionStorage.getItem("lastShoppingPage")}catch(P){return console.warn("Could not retrieve last shopping page:",P),null}},trackCartEvent:ee,cleanup:()=>{B&&B.close(),window.removeEventListener("online",N),window.removeEventListener("offline",N),R&&clearTimeout(R)}}});function Vt(){const o=ge(),s=Ne(),r=T(!1),c=T("login"),h=q(()=>o.isAuthenticated),w=q(()=>o.user),B=q(()=>o.isLoading),N=async p=>{try{return await o.login(p)}catch(C){throw console.error("Login failed:",C),C}},j=async()=>{try{await o.logout(),s.push("/")}catch(p){throw console.error("Logout failed:",p),p}},v=async p=>{try{const C=await fetch("/api/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(p)});if(!C.ok){const V=await C.json();throw new Error(V.message||"Registration failed")}const k=await C.json();return await N({email:p.email,password:p.password,remember:!1}),k}catch(C){throw console.error("Registration failed:",C),C}},g=p=>h.value?(p==null||p(),!0):(D(),!1),E=async p=>h.value?(await(p==null?void 0:p()),!0):(D(),!1),D=()=>{c.value="login",r.value=!0},M=()=>{c.value="register",r.value=!0},_=()=>{r.value=!1},m=p=>{var C;return w.value&&((C=w.value.roles)==null?void 0:C.some(k=>k.name===p))||!1},I=p=>{var C;return w.value&&((C=w.value.permissions)==null?void 0:C.includes(p))||!1};return{isAuthenticated:h,user:w,isLoading:B,showAuthModal:r,authModalTab:c,login:N,logout:j,register:v,initialize:async()=>{await o.initialize()},requireAuth:g,requireAuthAsync:E,showLoginModal:D,showRegisterModal:M,hideAuthModal:_,hasRole:m,hasPermission:I,canPlaceBids:()=>h.value&&I("place_bids"),redirectToLogin:p=>{const C=p?{redirect:p}:{};s.push({name:"login",query:C})},redirectIfAuthenticated:(p="/")=>{h.value&&s.push(p)}}}const ar={class:"group relative bg-white rounded-xl border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1"},rr={class:"relative aspect-[4/3] overflow-hidden bg-gray-100"},nr=["src","alt"],lr={key:1,class:"w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200"},ir={class:"absolute top-3 left-3"},dr={class:"flex items-center space-x-1"},ur={class:"text-xs font-semibold"},cr={key:2,class:"absolute top-3 right-3"},mr={class:"text-xs font-medium"},vr={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100"},gr={class:"p-5"},pr={class:"mb-3"},hr={class:"font-bold text-gray-900 text-lg line-clamp-2 mb-1"},fr={key:0,class:"text-xs text-gray-500 font-mono"},br={class:"mb-4"},yr={class:"flex items-baseline justify-between mb-1"},xr={class:"text-sm font-medium text-gray-600"},wr={key:0,class:"text-xs text-gray-500"},_r={class:"flex items-baseline space-x-2"},kr={class:"text-2xl font-bold text-gray-900"},Cr={key:0,class:"text-sm text-gray-500 line-through"},$r={key:0,class:"mb-4 p-3 bg-gray-50 rounded-lg"},Mr={class:"flex items-center justify-between text-sm"},jr={class:"text-gray-600"},Ir={class:"font-medium text-gray-900"},Sr={key:0,class:"mt-2"},Ar={class:"w-full bg-gray-200 rounded-full h-1.5"},Br={key:1,class:"space-y-2"},Tr={key:0,class:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24"},Nr={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},zr={key:0,class:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24"},Dr={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Lr={key:2,class:"space-y-2"},Pr=["fill"],Er=me({__name:"ItemCard",props:{item:{}},emits:["view-details","place-bid","preview","add-to-cart","remove-from-cart","checkout","watch-item"],setup(o,{emit:s}){const r=o,c=s,h=Ae(),w=Ue(),B=Me(),{isAuthenticated:N,user:j,initialize:v}=Vt(),g=T(!1),E=T(!1),D=T(!1),M=()=>De("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[De("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})]),_=()=>De("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[De("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})]),m=()=>De("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[De("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})]),I=b=>{switch(b){case"cash":return"Daily Sale";case"online":return"Online";case"live":return"Live";default:return"Auction"}},F=b=>{switch(b){case"cash":return M;case"online":return _;case"live":return m;default:return _}},$=b=>{const f="px-2 py-1 rounded-full text-white shadow-sm";switch(b){case"cash":return`${f} bg-green-500`;case"online":return`${f} bg-blue-500`;case"live":return`${f} bg-orange-500`;default:return`${f} bg-gray-500`}},u=b=>{var f,Q;return b.closed_by?"SOLD":((f=b.auction_type)==null?void 0:f.type)==="live"&&ae(b)?"LIVE":((Q=b.auction_type)==null?void 0:Q.type)!=="cash"&&z(b)?"ENDING SOON":null},d=b=>{const f=u(b),Q="px-2 py-1 rounded-full text-white shadow-sm";switch(f){case"SOLD":return`${Q} bg-gray-600`;case"LIVE":return`${Q} bg-red-500 animate-pulse`;case"ENDING SOON":return`${Q} bg-yellow-500`;default:return`${Q} bg-blue-500`}},p=b=>{switch(b){case"cash":return"Price";case"online":case"live":return"Current Bid";default:return"Price"}},C=b=>{var f;switch((f=b.auction_type)==null?void 0:f.type){case"cash":return b.target_amount||0;case"online":case"live":return b.bid_amount||b.target_amount||0;default:return b.target_amount||0}},k=b=>{var f;return((f=b.auction_type)==null?void 0:f.type)!=="cash"&&b.bid_amount&&b.target_amount&&b.bid_amount>b.target_amount?b.target_amount:null},V=b=>!b||b<=0?"0":b.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,"),Z=b=>{var f;return((f=b.auction_type)==null?void 0:f.type)!=="cash"&&(b.bid_amount||0)>0},oe=b=>Math.floor(Math.random()*10)+1+"",le=b=>!!(b.date_from||b.date_to),ee=b=>{switch(b){case"cash":return"Available";case"online":return"Ends";case"live":return"Auction Date";default:return"Date"}},R=b=>{var f;if(((f=b.auction_type)==null?void 0:f.type)==="cash")return"Now";if(b.date_to){const Q=new Date(b.date_to),n=new Date,t=Q.getTime()-n.getTime();if(t<0)return"Ended";const U=Math.floor(t/(1e3*60*60*24)),S=Math.floor(t%(1e3*60*60*24)/(1e3*60*60));return U>0?`${U}d ${S}h`:S>0?`${S}h`:"Soon"}return"TBD"},Y=b=>{if(!b.date_from||!b.date_to)return"0%";const f=new Date(b.date_from).getTime(),Q=new Date(b.date_to).getTime(),n=new Date().getTime();if(n<f)return"0%";if(n>Q)return"100%";const t=(n-f)/(Q-f)*100;return`${Math.min(100,Math.max(0,t))}%`},ae=b=>{if(!b.date_from||!b.date_to)return!1;const f=new Date;return f>=new Date(b.date_from)&&f<=new Date(b.date_to)},z=b=>{if(!b.date_to)return!1;const f=new Date(b.date_to),Q=new Date,n=f.getTime()-Q.getTime();return n>0&&n<24*60*60*1e3},y=async b=>{g.value=!0;try{c("add-to-cart",b)}finally{setTimeout(()=>{g.value=!1},500)}},P=async b=>{g.value=!0;try{c("remove-from-cart",b)}finally{setTimeout(()=>{g.value=!1},500)}},H=b=>{c("checkout",b)},K=b=>{c("place-bid",b)},J=async b=>{if(!N.value){D.value=!0;return}E.value=!0;try{if(await w.toggleWatchlist(b)){const Q=je.value?"added to":"removed from";B.success(`${b.name} ${Q} watchlist`)}else B.error(w.error||"Failed to update watchlist")}catch(f){console.error("Watchlist error:",f),B.error("Failed to update watchlist")}finally{E.value=!1}c("watch-item",b)},ie=async b=>{D.value=!1,B.success("You are now signed in and can add items to your watchlist!","Welcome!"),await w.initializeWatchlist(),await J(r.item)},fe=b=>!!(b.closed_by||b.status==="sold"),Se=q(()=>h.isInCart(r.item.id)),je=q(()=>w.isInWatchlist(r.item.id));$e(async()=>{await v(),N.value&&w.items.length===0&&await w.syncWatchlistCount()});const Ce=b=>b.date_to?new Date>new Date(b.date_to):!1,Te=b=>{var f;return Ce(b)?"Auction Ended":((f=b.auction_type)==null?void 0:f.type)==="live"&&ae(b)?"Bid Live":"Place Bid"};return(b,f)=>{var Q,n,t,U,S,X,re;return a(),l("div",ar,[e("div",rr,[b.item.cropped||b.item.image?(a(),l("img",{key:0,src:b.item.cropped||b.item.image,alt:b.item.name,class:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110 cursor-pointer",onClick:f[0]||(f[0]=ne=>b.$emit("preview",b.item))},null,8,nr)):(a(),l("div",lr,f[8]||(f[8]=[e("div",{class:"text-center"},[e("svg",{class:"w-16 h-16 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("p",{class:"text-xs text-gray-500"},"No Image")],-1)]))),e("div",ir,[e("div",{class:G($((Q=b.item.auction_type)==null?void 0:Q.type))},[e("div",dr,[(a(),de(Oe(F((n=b.item.auction_type)==null?void 0:n.type)),{class:"w-3 h-3"})),e("span",ur,x(I((t=b.item.auction_type)==null?void 0:t.type)),1)])],2)]),u(b.item)?(a(),l("div",cr,[e("div",{class:G(d(b.item))},[e("span",mr,x(u(b.item)),1)],2)])):L("",!0),e("div",vr,[e("button",{onClick:f[1]||(f[1]=ne=>b.$emit("preview",b.item)),class:"bg-white text-gray-900 px-4 py-2 rounded-lg font-medium transform translate-y-2 group-hover:translate-y-0 transition-all duration-300 shadow-lg hover:shadow-xl"}," Quick View ")])]),e("div",gr,[e("div",pr,[e("h3",hr,x(b.item.name),1),b.item.code||b.item.reference_number?(a(),l("div",fr," Ref: "+x(b.item.code||b.item.reference_number),1)):L("",!0)]),e("div",br,[e("div",yr,[e("span",xr,x(p((U=b.item.auction_type)==null?void 0:U.type)),1),Z(b.item)?(a(),l("span",wr,x(oe(b.item))+" bids",1)):L("",!0)]),e("div",_r,[e("span",kr,[f[9]||(f[9]=e("span",{class:"text-sm font-normal text-gray-500 mr-1"},"MK",-1)),W(x(V(C(b.item))),1)]),k(b.item)?(a(),l("span",Cr,[f[10]||(f[10]=e("span",{class:"text-xs font-normal text-gray-400 mr-1"},"MK",-1)),W(x(V(k(b.item))),1)])):L("",!0)])]),le(b.item)?(a(),l("div",$r,[e("div",Mr,[e("span",jr,x(ee((S=b.item.auction_type)==null?void 0:S.type)),1),e("span",Ir,x(R(b.item)),1)]),((X=b.item.auction_type)==null?void 0:X.type)!=="cash"?(a(),l("div",Sr,[e("div",Ar,[e("div",{class:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:He({width:Y(b.item)})},null,4)])])):L("",!0)])):L("",!0),((re=b.item.auction_type)==null?void 0:re.type)==="cash"?(a(),l("div",Br,[Se.value?(a(),de(i(te),{key:1,variant:"outline",class:"w-full font-semibold border-red-600 text-red-600 hover:bg-red-50 hover:border-red-700",onClick:f[3]||(f[3]=xe(ne=>P(b.item),["stop"])),disabled:g.value},{default:O(()=>[g.value?(a(),l("svg",zr,f[13]||(f[13]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),l("svg",Dr,f[14]||(f[14]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"},null,-1)]))),W(" "+x(g.value?"Removing...":"Remove"),1)]),_:1},8,["disabled"])):(a(),de(i(te),{key:0,variant:"outline",class:"w-full font-semibold border-blue-600 text-blue-600 hover:bg-blue-50 hover:border-blue-700",onClick:f[2]||(f[2]=xe(ne=>y(b.item),["stop"])),disabled:fe(b.item)||g.value},{default:O(()=>[g.value?(a(),l("svg",Tr,f[11]||(f[11]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),l("svg",Nr,f[12]||(f[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"},null,-1)]))),W(" "+x(fe(b.item)?"Sold Out":g.value?"Adding...":"Add to Cart"),1)]),_:1},8,["disabled"])),A(i(te),{variant:"primary",class:"w-full font-semibold bg-green-600 hover:bg-green-700 border-green-600",onClick:f[4]||(f[4]=xe(ne=>H(b.item),["stop"])),disabled:fe(b.item)},{default:O(()=>[f[15]||(f[15]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),W(" "+x(fe(b.item)?"Unavailable":"Buy Now"),1)]),_:1,__:[15]},8,["disabled"])])):(a(),l("div",Lr,[A(i(te),{variant:"primary",class:"w-full font-semibold bg-blue-600 hover:bg-blue-700 border-blue-600",onClick:f[5]||(f[5]=xe(ne=>K(b.item),["stop"])),disabled:Ce(b.item)},{default:O(()=>[f[16]||(f[16]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1)),W(" "+x(Te(b.item)),1)]),_:1,__:[16]},8,["disabled"]),A(i(te),{variant:"outline",class:G(["w-full font-medium border-gray-300 transition-all duration-200",je.value?"text-red-600 border-red-300 bg-red-50 hover:bg-red-100":"text-gray-700 hover:bg-gray-50"]),loading:E.value,onClick:f[6]||(f[6]=xe(ne=>J(b.item),["stop"]))},{default:O(()=>[(a(),l("svg",{class:"w-4 h-4 mr-2",fill:je.value?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},f[17]||(f[17]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Pr)),W(" "+x(je.value?"Remove from Watchlist":"Add to Watchlist"),1)]),_:1},8,["class","loading"])]))]),A(Xe,{show:D.value,"onUpdate:show":f[7]||(f[7]=ne=>D.value=ne),title:"Sign in to add to watchlist",subtitle:"Create an account or sign in to save items to your watchlist and track your favorite auctions.",onSuccess:ie},null,8,["show"])])}}}),Rt=ke(Er,[["__scopeId","data-v-9c64a7b0"]]),Vr={key:1},Rr={class:"text-center"},Fr={key:0,class:"mb-4"},qr={class:"text-lg font-medium text-gray-900 mb-2"},Hr={class:"text-sm text-gray-600 mb-6"},Or={class:"flex flex-col sm:flex-row gap-3 justify-center"},Wr=me({__name:"AuthGuard",props:{title:{default:"Authentication Required"},message:{default:"Please sign in to continue with this action."},loginButtonText:{default:"Sign In"},registerButtonText:{default:"Create Account"},showRegisterButton:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},fallbackStyle:{default:"card"},requireAuth:{type:Boolean,default:!0}},emits:["auth-required","auth-success"],setup(o,{expose:s,emit:r}){const c=o,h=r,w=ge(),B=T(!1),N=T("login"),j=q(()=>{if(!c.requireAuth)return!0;const M=window.user;if(M)return console.log("AuthGuard: User authenticated via server data",M),!0;const _=!!w.user,m=w.sessionAuth,I=!!w.token,F=w.isAuthenticated,$=_||m||F;return console.log("AuthGuard: authentication check",{serverUser:!!M,hasUser:_,hasSession:m,hasToken:I,storeAuth:F,finalResult:$}),$}),v=q(()=>w.isLoading),g=q(()=>{const M="auth-guard-fallback";switch(c.fallbackStyle){case"card":return`${M} bg-white rounded-lg border border-gray-200 p-8 shadow-sm`;case"inline":return`${M} py-6`;case"minimal":return`${M} py-4`;default:return`${M} bg-white rounded-lg border border-gray-200 p-8 shadow-sm`}}),E=(M="login")=>{N.value=M,B.value=!0,h("auth-required")},D=M=>{B.value=!1,h("auth-success",M)};return $e(async()=>{const M=window.user;M&&!w.user&&(console.log("AuthGuard: Syncing server user data with auth store"),w.setUser(M))}),s({showAuthModal:E,isAuthenticated:j}),(M,_)=>(a(),l("div",null,[j.value?qe(M.$slots,"default",{key:0},void 0,!0):(a(),l("div",Vr,[qe(M.$slots,"fallback",{},()=>[e("div",{class:G(g.value)},[e("div",Rr,[M.showIcon?(a(),l("div",Fr,[A(i(Ke),{class:"mx-auto h-12 w-12 text-gray-400"})])):L("",!0),e("h3",qr,x(M.title),1),e("p",Hr,x(M.message),1),e("div",Or,[A(i(te),{variant:"primary",onClick:E,loading:v.value},{default:O(()=>[W(x(M.loginButtonText),1)]),_:1},8,["loading"]),M.showRegisterButton?(a(),de(i(te),{key:0,variant:"outline",onClick:_[0]||(_[0]=m=>E("register")),loading:v.value},{default:O(()=>[W(x(M.registerButtonText),1)]),_:1},8,["loading"])):L("",!0)])])],2)],!0)])),A(Xe,{show:B.value,"onUpdate:show":_[1]||(_[1]=m=>B.value=m),"start-with-register":N.value==="register",title:M.title,subtitle:M.message,onSuccess:D,onClose:_[2]||(_[2]=m=>B.value=!1)},null,8,["show","start-with-register","title","subtitle"])]))}}),ht=ke(Wr,[["__scopeId","data-v-7401f350"]]),Ur={class:"auction-filters"},Yr={key:0,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},Kr={class:"flex flex-wrap gap-2"},Qr=["onClick"],Xr={class:"space-y-8"},Gr={class:"filter-section"},Zr={class:"space-y-3"},Jr=["value"],en={class:"ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex-1"},tn={key:0,class:"text-xs text-gray-500 font-medium"},sn={key:0,class:"filter-section"},on={class:"space-y-3 max-h-48 overflow-y-auto"},an=["value"],rn={class:"ml-3 text-sm text-gray-700 group-hover:text-gray-900 flex-1"},nn={key:0,class:"text-xs text-gray-500 font-medium"},ln={class:"filter-section"},dn={class:"space-y-4"},un={class:"grid grid-cols-2 gap-2"},cn=["onClick"],mn={class:"pt-2"},vn={class:"grid grid-cols-2 gap-3"},gn=me({__name:"AuctionFilters",props:{auctionTypeOptions:{default:()=>[{label:"Daily Sale",value:"cash"},{label:"Online Auctions",value:"online"},{label:"Live Auctions",value:"live"}]},locationOptions:{default:()=>[{label:"Blantyre branch",value:"1"},{label:"Lilongwe branch",value:"2"}]},showLocationFilter:{type:Boolean,default:!0}},emits:["filter-change","apply-filters","reset-filters"],setup(o,{emit:s}){const r=o,c=s,h=T([]),w=T([]),B=T({min:"",max:""}),N=[{label:"Under 50,000",min:null,max:5e4},{label:"50,000 - 100,000",min:5e4,max:1e5},{label:"100,000 - 500,000",min:1e5,max:5e5},{label:"500,000 - 1,000,000",min:5e5,max:1e6},{label:"1,000,000 - 5,000,000",min:1e6,max:5e6},{label:"Over 5,000,000",min:5e6,max:null}],j=q(()=>h.value.length>0||w.value.length>0||B.value.min||B.value.max),v=q(()=>{const $=[];if(h.value.forEach(u=>{var p;const d=(p=r.auctionTypeOptions)==null?void 0:p.find(C=>C.value===u);d&&$.push({key:`type-${u}`,label:d.label,type:"auctionType",value:u})}),w.value.forEach(u=>{var p;const d=(p=r.locationOptions)==null?void 0:p.find(C=>C.value===u);d&&$.push({key:`location-${u}`,label:d.label,type:"location",value:u})}),B.value.min||B.value.max){const u=B.value.min||"0",d=B.value.max||"∞";$.push({key:"price-range",label:`${u} - ${d}`,type:"priceRange",value:"custom"})}return $}),g=q(()=>({auctionTypes:h.value,locations:w.value,priceMin:B.value.min?parseFloat(B.value.min):null,priceMax:B.value.max?parseFloat(B.value.max):null})),E=$=>{var u,d;B.value={min:((u=$.min)==null?void 0:u.toString())||"",max:((d=$.max)==null?void 0:d.toString())||""},m()},D=$=>{const u=B.value.min?parseFloat(B.value.min):null,d=B.value.max?parseFloat(B.value.max):null;return u===$.min&&d===$.max},M=()=>{m()},_=($,u)=>{switch($){case"auctionType":h.value=h.value.filter(d=>d!==u);break;case"location":w.value=w.value.filter(d=>d!==u);break;case"priceRange":B.value={min:"",max:""};break}m()},m=()=>{c("filter-change",g.value)},I=()=>{h.value=[],w.value=[],B.value={min:"",max:""},c("reset-filters")},F=()=>{I(),m()};return($,u)=>(a(),l("div",Ur,[j.value?(a(),l("div",Yr,[e("div",{class:"flex items-center justify-between mb-3"},[u[4]||(u[4]=e("h4",{class:"text-sm font-medium text-blue-900"},"Active Filters",-1)),e("button",{onClick:F,class:"text-xs text-blue-600 hover:text-blue-700 font-medium"}," Clear all ")]),e("div",Kr,[(a(!0),l(ue,null,ce(v.value,d=>(a(),l("span",{key:d.key,class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white text-blue-800 border border-blue-200"},[W(x(d.label)+" ",1),e("button",{onClick:p=>_(d.type,d.value),class:"ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-100 transition-colors"},u[5]||(u[5]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Qr)]))),128))])])):L("",!0),e("div",Xr,[e("div",Gr,[u[6]||(u[6]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Auction Type",-1)),e("div",Zr,[(a(!0),l(ue,null,ce($.auctionTypeOptions,d=>(a(),l("label",{key:d.value,class:"flex items-center cursor-pointer group"},[pe(e("input",{"onUpdate:modelValue":u[0]||(u[0]=p=>h.value=p),type:"checkbox",value:d.value,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",onChange:m},null,40,Jr),[[Ee,h.value]]),e("span",en,x(d.label),1),d.count!==void 0?(a(),l("span",tn,x(d.count),1)):L("",!0)]))),128))])]),$.showLocationFilter?(a(),l("div",sn,[u[7]||(u[7]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Location",-1)),e("div",on,[(a(!0),l(ue,null,ce($.locationOptions,d=>(a(),l("label",{key:d.value,class:"flex items-center cursor-pointer group"},[pe(e("input",{"onUpdate:modelValue":u[1]||(u[1]=p=>w.value=p),type:"checkbox",value:d.value,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors",onChange:m},null,40,an),[[Ee,w.value]]),e("span",rn,x(d.label),1),d.count!==void 0?(a(),l("span",nn,x(d.count),1)):L("",!0)]))),128))])])):L("",!0),e("div",ln,[u[10]||(u[10]=e("h4",{class:"text-sm font-semibold text-gray-900 mb-4"},"Price Range (MWK)",-1)),e("div",dn,[e("div",un,[(a(),l(ue,null,ce(N,d=>e("button",{key:d.label,onClick:p=>E(d),class:G(["px-3 py-2 text-xs border rounded-lg transition-all duration-200 font-medium text-center",D(d)?"bg-blue-600 border-blue-600 text-white":"bg-white border-gray-300 text-gray-700 hover:border-blue-300 hover:text-blue-600"])},x(d.label),11,cn)),64))]),e("div",mn,[e("div",vn,[e("div",null,[u[8]||(u[8]=e("label",{class:"block text-xs font-medium text-gray-600 mb-2"},"Min Price",-1)),pe(e("input",{"onUpdate:modelValue":u[2]||(u[2]=d=>B.value.min=d),type:"number",min:"0",step:"1",placeholder:"0",class:"block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onChange:M},null,544),[[Ie,B.value.min]])]),e("div",null,[u[9]||(u[9]=e("label",{class:"block text-xs font-medium text-gray-600 mb-2"},"Max Price",-1)),pe(e("input",{"onUpdate:modelValue":u[3]||(u[3]=d=>B.value.max=d),type:"number",min:"0",step:"1",placeholder:"Any",class:"block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onChange:M},null,544),[[Ie,B.value.max]])])])])])])])]))}}),pn=ke(gn,[["__scopeId","data-v-76900880"]]),hn={class:"min-h-screen bg-gray-50"},fn={class:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8"},bn=me({__name:"AppLayout",setup(o){return(s,r)=>(a(),l("div",hn,[r[0]||(r[0]=Be('<nav class="bg-white shadow"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex"><div class="flex-shrink-0 flex items-center"><h1 class="text-xl font-bold text-gray-900"> Vertigo AMS </h1></div><div class="hidden sm:ml-6 sm:flex sm:space-x-8"><a href="#" class="border-primary-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Dashboard </a><a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Auctions </a><a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"> Items </a></div></div><div class="hidden sm:ml-6 sm:flex sm:items-center"><div class="ml-3 relative"><button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true"><span class="sr-only">Open user menu</span><div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center"><span class="text-sm font-medium text-gray-700">U</span></div></button></div></div></div></div></nav>',1)),e("main",null,[e("div",fn,[qe(s.$slots,"default")])])]))}}),yn={class:"relative"},xn={key:0,class:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"},wn={class:"flex items-center justify-between p-4 border-b border-gray-200"},_n={class:"text-lg font-semibold text-gray-900"},kn={class:"flex-1 overflow-y-auto p-4"},Cn={key:0,class:"text-center py-8"},$n={key:1,class:"space-y-4"},Mn={class:"flex-shrink-0"},jn=["src","alt"],In={class:"flex-1 min-w-0"},Sn={class:"text-sm font-medium text-gray-900 truncate"},An={class:"text-sm text-gray-500"},Bn={class:"mt-1"},Tn={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Nn={class:"flex-shrink-0"},zn=["onClick","disabled"],Dn={key:0,class:"border-t border-gray-200 p-4 space-y-4"},Ln={class:"flex justify-between items-center"},Pn={class:"text-lg font-semibold text-gray-900"},En={class:"space-y-2"},Vn={key:1,class:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"},Ft=me({__name:"CartDrawer",setup(o){const s=Ae(),r=T(!1),c=()=>{r.value=!r.value},h=()=>{r.value=!1},w=async v=>{await s.removeFromCart(v)&&console.log(`${v.name} removed from cart`)},B=()=>{s.clearCart(),h()},N=()=>{h(),window.location.href="/checkout"},j=v=>{switch(v){case"cash":return"Daily Sale";case"online":return"Online Auction";case"live":return"Live Auction";default:return v||"Unknown"}};return(v,g)=>(a(),l("div",yn,[e("button",{onClick:c,class:"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors duration-200"},[g[1]||(g[1]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),i(s).cartCount>0?(a(),l("span",xn,x(i(s).cartCount>9?"9+":i(s).cartCount),1)):L("",!0)]),r.value?(a(),l("div",{key:0,class:"fixed inset-0 z-50 overflow-hidden",onClick:h},[g[9]||(g[9]=e("div",{class:"absolute inset-0 bg-black bg-opacity-50 transition-opacity"},null,-1)),e("div",{class:"absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl transform transition-transform duration-300 ease-in-out",onClick:g[0]||(g[0]=xe(()=>{},["stop"]))},[e("div",wn,[e("h2",_n," Shopping Cart ("+x(i(s).cartCount)+") ",1),e("button",{onClick:h,class:"p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md"},g[2]||(g[2]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",kn,[i(s).isEmpty?(a(),l("div",Cn,g[3]||(g[3]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Your cart is empty",-1),e("p",{class:"text-xs text-gray-400"},"Add some items to get started",-1)]))):(a(),l("div",$n,[(a(!0),l(ue,null,ce(i(s).items,E=>{var D;return a(),l("div",{key:E.id,class:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"},[e("div",Mn,[e("img",{src:E.image||"/images/placeholder.jpg",alt:E.name,class:"w-16 h-16 object-cover rounded-md"},null,8,jn)]),e("div",In,[e("h3",Sn,x(E.name),1),e("p",An,x(i(s).formatCurrency(E.target_amount||0)),1),e("div",Bn,[e("span",Tn,x(j((D=E.auction_type)==null?void 0:D.type)),1)])]),e("div",Nn,[e("button",{onClick:M=>w(E),disabled:i(s).loading,class:"p-1 text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md disabled:opacity-50"},g[4]||(g[4]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,zn)])])}),128))]))]),i(s).isEmpty?L("",!0):(a(),l("div",Dn,[e("div",Ln,[g[5]||(g[5]=e("span",{class:"text-base font-medium text-gray-900"},"Total:",-1)),e("span",Pn,x(i(s).formatCurrency(i(s).cartTotal)),1)]),e("div",En,[A(i(te),{onClick:N,class:"w-full bg-blue-600 hover:bg-blue-700 text-white",disabled:i(s).loading},{default:O(()=>g[6]||(g[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})],-1),W(" Proceed to Checkout ")])),_:1,__:[6]},8,["disabled"]),A(i(te),{onClick:B,variant:"outline",class:"w-full border-gray-300 text-gray-700 hover:bg-gray-50",disabled:i(s).loading},{default:O(()=>g[7]||(g[7]=[W(" Clear Cart ")])),_:1,__:[7]},8,["disabled"])])])),i(s).loading?(a(),l("div",Vn,g[8]||(g[8]=[e("div",{class:"text-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e("p",{class:"mt-2 text-sm text-gray-500"},"Updating cart...")],-1)]))):L("",!0)])])):L("",!0)]))}}),Rn={class:"relative"},Fn=me({__name:"CartIcon",props:{count:{default:0},buttonClass:{default:""},badgeClass:{default:""}},emits:["click"],setup(o){return(s,r)=>(a(),l("div",Rn,[e("button",{onClick:r[0]||(r[0]=c=>s.$emit("click")),class:G(["relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md transition-colors duration-200",s.buttonClass])},[r[1]||(r[1]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),s.count>0?(a(),l("span",{key:0,class:G(["absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",s.badgeClass])},x(s.count>99?"99+":s.count),3)):L("",!0)],2)]))}}),qn={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},Hn=me({__name:"CartNavIcon",setup(o){const s=Ae(),r=q(()=>{const c=window.location.pathname;return c==="/cart"||c.startsWith("/spa")||c.startsWith("/home-vue")});return(c,h)=>(a(),de(Oe(r.value?"router-link":"a"),{to:r.value?"/cart":void 0,href:r.value?void 0:"/cart",class:"relative p-2 text-gray-600 hover:text-primary-600 rounded-lg hover:bg-white/50 transition-all duration-200 inline-block"},{default:O(()=>[h[0]||(h[0]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),i(s).cartCount>0?(a(),l("span",qn,x(i(s).cartCount>9?"9+":i(s).cartCount),1)):L("",!0)]),_:1,__:[0]},8,["to","href"]))}}),On={class:"bg-white border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"},Wn={class:"p-2"},Un={class:"flex items-start space-x-4"},Yn={class:"flex-shrink-0"},Kn={class:"relative"},Qn=["src","alt"],Xn={class:"absolute -top-1 -right-1"},Gn={class:"flex-1 min-w-0"},Zn={class:"flex items-start justify-between"},Jn={class:"flex-1 pr-4"},el={class:"text-lg font-semibold text-gray-900 mb-2 leading-tight hover:text-blue-600 transition-colors cursor-pointer"},tl={class:"flex items-center flex-wrap gap-2 mb-2"},sl={class:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"},ol={key:0,class:"text-sm text-gray-600 line-clamp-1 mb-2"},al={class:"flex items-center space-x-3"},rl={class:"text-lg font-bold text-gray-900"},nl={key:0,class:"text-sm text-gray-500"},ll={class:"flex items-center space-x-1"},il={class:"mb-2 pt-3 border-t border-gray-100"},dl={class:"flex items-center justify-between px-3"},ul={class:"flex items-center space-x-3"},cl={class:"flex items-center border border-gray-300 rounded-md bg-white"},ml=["disabled"],vl={class:"text-right"},gl={class:"text-lg font-bold text-gray-900"},pl=me({__name:"CartItemRow",props:{item:{},loading:{type:Boolean,default:!1}},emits:["update-quantity","remove"],setup(o,{emit:s}){const r=o,c=s,h=T(r.item.quantity||1),w=q(()=>{const _=r.item.target_amount||0,m=h.value||1;return _*m});_e(()=>r.item.quantity,_=>{_!==void 0&&_!==h.value&&(h.value=_)});const B=_=>!_||_<=0?"MK 0":`MK ${_.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,N=_=>{switch(_){case"cash":return"success";case"online":return"primary";case"live":return"danger";default:return"secondary"}},j=_=>{switch(_){case"cash":return"Cash Auction";case"online":return"Online Auction";case"live":return"Live Auction";default:return"Unknown"}},v=_=>{switch(_){case"cash":return"bg-green-500";case"online":return"bg-blue-500";case"live":return"bg-red-500";default:return"bg-gray-500"}},g=()=>{h.value<99&&(h.value++,D())},E=()=>{h.value>1&&(h.value--,D())},D=()=>{h.value<1?h.value=1:h.value>99&&(h.value=99),h.value!==r.item.quantity&&c("update-quantity",r.item,h.value)},M=_=>{const m=_.target;m.src="/img/product.jpeg"};return(_,m)=>{var I,F;return a(),l("div",On,[e("div",Wn,[e("div",Un,[e("div",Yn,[e("div",Kn,[e("img",{src:_.item.image||_.item.cropped||"/img/product.jpeg",alt:_.item.name,class:"w-16 h-16 object-cover rounded-lg border border-gray-200",onError:M},null,40,Qn),e("div",Xn,[e("div",{class:G([v((I=_.item.auction_type)==null?void 0:I.type),"w-4 h-4 rounded-full border-2 border-white shadow-sm"])},null,2)])])]),e("div",Gn,[e("div",Zn,[e("div",Jn,[e("h4",el,x(_.item.name),1),e("div",tl,[A(i(Ve),{variant:N((F=_.item.auction_type)==null?void 0:F.type),size:"sm",class:"font-medium px-2 py-1 text-xs"},{default:O(()=>{var $;return[W(x(j(($=_.item.auction_type)==null?void 0:$.type)),1)]}),_:1},8,["variant"]),e("span",sl," ID: "+x(_.item.id),1)]),_.item.description?(a(),l("p",ol,x(_.item.description),1)):L("",!0),e("div",al,[e("div",rl,x(B(_.item.target_amount)),1),(_.item.quantity||1)>1?(a(),l("div",nl," × "+x(_.item.quantity||1),1)):L("",!0)])]),e("div",ll,[A(i(te),{variant:"ghost",size:"sm",onClick:m[0]||(m[0]=$=>_.$emit("remove",_.item)),disabled:_.loading,class:"text-gray-400 hover:text-red-600 hover:bg-red-50 p-2 rounded-lg transition-all duration-200",title:"Remove item from cart"},{default:O(()=>m[2]||(m[2]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:1,__:[2]},8,["disabled"])])])])])]),e("div",il,[e("div",dl,[e("div",ul,[m[5]||(m[5]=e("span",{class:"text-sm text-gray-600 font-medium"},"Qty:",-1)),e("div",cl,[A(i(te),{variant:"ghost",size:"xs",onClick:E,disabled:_.loading||(_.item.quantity||1)<=1,class:"px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50"},{default:O(()=>m[3]||(m[3]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),_:1,__:[3]},8,["disabled"]),pe(e("input",{"onUpdate:modelValue":m[1]||(m[1]=$=>h.value=$),type:"number",min:"1",max:"99",class:"w-12 px-2 py-1 text-center text-sm font-medium border-0 focus:ring-0 focus:outline-none",onBlur:D,onKeyup:Kt(D,["enter"]),disabled:_.loading},null,40,ml),[[Ie,h.value,void 0,{number:!0}]]),A(i(te),{variant:"ghost",size:"xs",onClick:g,disabled:_.loading||(_.item.quantity||1)>=99,class:"px-2 py-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50"},{default:O(()=>m[4]||(m[4]=[e("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),_:1,__:[4]},8,["disabled"])])]),e("div",vl,[m[6]||(m[6]=e("div",{class:"text-xs text-gray-500 mb-1"},"Subtotal",-1)),e("div",gl,x(B(w.value)),1)])])])])}}});const Ye=ke(pl,[["__scopeId","data-v-c5ab099c"]]),qt=We("items",()=>{const o=T(null),s=T([]),r=T([]),c=T(!1),h=T(null),w=q(()=>{var k;return((k=o.value)==null?void 0:k.total)||0}),B=q(()=>{var k;return((k=o.value)==null?void 0:k.current_page)||1}),N=q(()=>{var k;return((k=o.value)==null?void 0:k.last_page)||1}),j=q(()=>B.value<N.value),v=async(k={})=>{c.value=!0,h.value=null;try{const V=new URLSearchParams;k.search&&V.append("search",k.search),k.type&&V.append("type",k.type),k.branch_id&&V.append("branch_id",k.branch_id),k.auction_type_id&&V.append("auction_type_id",k.auction_type_id),k.per_page&&V.append("per_page",k.per_page.toString()),k.sort_by&&V.append("sort_by",k.sort_by),k.statuses&&k.statuses.length>0&&k.statuses.forEach(oe=>V.append("statuses[]",oe)),k.auctionTypes&&k.auctionTypes.length>0&&k.auctionTypes.forEach(oe=>V.append("auction_types[]",oe)),k.locations&&k.locations.length>0&&k.locations.forEach(oe=>V.append("locations[]",oe)),k.conditions&&k.conditions.length>0&&k.conditions.forEach(oe=>V.append("conditions[]",oe)),k.priceMin!==void 0&&k.priceMin!==null&&V.append("price_min",k.priceMin.toString()),k.priceMax!==void 0&&k.priceMax!==null&&V.append("price_max",k.priceMax.toString()),k.dateRange&&V.append("date_range",k.dateRange),k.customDateRange&&(k.customDateRange.from&&V.append("date_from",k.customDateRange.from),k.customDateRange.to&&V.append("date_to",k.customDateRange.to)),k.hasImages&&V.append("has_images","1"),k.hasReserve&&V.append("has_reserve","1"),k.featuredOnly&&V.append("featured_only","1"),k.acceptsPayPal&&V.append("accepts_paypal","1"),k.freeShipping&&V.append("free_shipping","1");const Z=await ye.get(`/ajax-items?${V.toString()}`);o.value=Z.data}catch(V){h.value="Failed to fetch auction items",console.error("Error fetching items:",V)}finally{c.value=!1}};return{items:o,branches:s,auctionTypes:r,loading:c,error:h,totalItems:w,currentPage:B,lastPage:N,hasMorePages:j,fetchItems:v,fetchBranches:async()=>{try{const k=await ye.get("/ajax-branches");s.value=k.data}catch(k){console.error("Error fetching branches:",k),s.value=[]}},fetchAuctionTypes:async()=>{try{const k=await ye.get("/ajax-auction-types");r.value=k.data}catch(k){console.error("Error fetching auction types:",k),r.value=[]}},getItemById:async k=>{try{return(await ye.get(`/ajax-item/${k}`)).data}catch(V){return console.error("Error fetching item:",V),null}},placeBid:async(k,V)=>{try{const Z=await ye.post(`/place-a-bid/${k}`,{bid_amount:V});return await v(),Z.data}catch(Z){throw console.error("Error placing bid:",Z),Z}},addToCart:async k=>{try{return(await ye.get(`/add-to-cart/${k}`)).data}catch(V){throw console.error("Error adding to cart:",V),V}},removeFromCart:async k=>{try{return(await ye.get(`/remove-from-cart/${k}`)).data}catch(V){throw console.error("Error removing from cart:",V),V}},initializeBranches:k=>{s.value=k},formatCurrency:k=>!k||k<=0?"MK 0":`MK ${k.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,getItemTypeLabel:k=>{switch(k){case"cash":return"Daily Sale";case"online":return"Online Auction";case"live":return"Live Auction";default:return k}},getItemActionLabel:k=>{switch(k){case"cash":return"VIEW DETAILS";case"online":case"live":return"PLACE A BID";default:return"VIEW DETAILS"}},getPriceLabel:k=>{switch(k){case"cash":return"Price:";case"online":case"live":return"Current Bid:";default:return"Price:"}},getItemPrice:k=>{var V;switch((V=k.auction_type)==null?void 0:V.type){case"cash":return k.target_amount||0;case"online":case"live":return k.bid_amount||0;default:return k.target_amount||0}},reset:()=>{o.value=null,s.value=[],r.value=[],c.value=!1,h.value=null}}}),Ht=We("adverts",()=>{const o=T([]),s=T(!1),r=T(null);return{adverts:o,loading:s,error:r,fetchAdverts:async()=>{s.value=!0,r.value=null;try{const B=await ye.get("/api/adverts");o.value=B.data}catch(B){r.value="Failed to fetch advertisements",console.error("Error fetching adverts:",B),o.value=[]}finally{s.value=!1}},initializeAdverts:B=>{o.value=B},reset:()=>{o.value=[],s.value=!1,r.value=null}}}),hl={class:"homepage"},fl={key:0,class:"hero-section relative overflow-hidden"},bl={class:"py-16 lg:py-12"},yl={class:"text-center max-w-5xl mx-auto"},xl={class:"flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4 mb-10"},wl={class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",style:{color:"#0068ff"}},_l={class:"absolute bottom-0 left-0 w-full overflow-hidden leading-none"},kl={class:"relative block w-full h-16 md:h-20 lg:h-24 text-white",fill:"currentColor",viewBox:"0 0 1200 120",preserveAspectRatio:"none",style:{transform:"rotate(180deg)"}},Cl={class:"category-navigation bg-white border-b relative z-10"},$l={class:"py-6"},Ml={class:"flex justify-center"},jl={class:"relative w-full max-w-lg"},Il={id:"auctions",class:"auctions-section py-8 bg-white"},Sl={class:"flex flex-col xl:flex-row gap-6"},Al={class:"w-full xl:w-72 flex-shrink-0"},Bl={class:"sticky top-6"},Tl={class:"bg-white border border-gray-200 rounded-xl overflow-hidden"},Nl={class:"px-6 py-4 border-b border-gray-100 bg-gray-50"},zl={class:"flex items-center justify-between"},Dl={class:"p-6"},Ll={class:"flex-1 min-w-0"},Pl={class:"mb-6"},El={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},Vl={class:"flex items-center space-x-4"},Rl={class:"text-2xl font-bold text-gray-900"},Fl={key:0,class:"text-sm text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg font-medium"},ql={class:"flex items-center space-x-3"},Hl={key:0,class:"flex justify-center py-16"},Ol={class:"text-center"},Wl={key:1,class:"space-y-8"},Ul={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Yl={key:0,class:"flex justify-center pt-8"},Kl={key:0,class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ql={key:2,class:"text-center py-16"},Xl={class:"max-w-md mx-auto"},Gl={class:"space-y-3"},Zl={class:"stats-section bg-gray-50 py-16"},Jl={key:1,class:"fixed bottom-6 right-6 z-50"},ei={class:"absolute -top-2 -right-2 h-6 w-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"},ti=me({__name:"Homepage",setup(o){const s=qt(),r=Ht(),c=Ae(),h=Me(),w=Ne(),B=At(),N=T(!0),j=T(!1),v=T(""),g=T("cash"),E=T(1),D=T(""),M=T(""),_=T(10),m=T("newest"),I=T({}),F=q(()=>s.items),$=q(()=>s.branches),u=q(()=>s.auctionTypes),d=q(()=>r.adverts),p=q(()=>{if(!d.value)return[];const n=new Date;return d.value.filter(t=>t.date_to?new Date(t.date_to)>n:!0)}),C=q(()=>F.value?F.value.current_page>=F.value.last_page:!0),k=q(()=>{var n;return[{key:"all",label:"All Items",badge:((n=F.value)==null?void 0:n.total)||0},{key:"cash",label:"Daily Sale",badge:void 0},{key:"online",label:"Online Auctions",badge:void 0},{key:"live",label:"Live Auctions",badge:void 0}]}),V=q(()=>u.value.map(n=>({value:n.type,label:n.name}))),Z=q(()=>$.value&&$.value.length>0?$.value.map(n=>({value:n.id.toString(),label:n.name})):[{label:"Blantyre branch",value:"1"},{label:"Lilongwe branch",value:"2"}]),oe=q(()=>v.value||g.value!=="cash"||Object.keys(I.value).length>0),le=async()=>{N.value=!0;try{const n={search:v.value,type:g.value,branch_id:D.value,auction_type_id:M.value,per_page:_.value,sort_by:m.value,...I.value};await s.fetchItems(n)}finally{N.value=!1}},ee=async()=>{j.value=!0;try{_.value+=10,await le()}finally{j.value=!1}},R=(n,t)=>{E.value=n;const U=t.key;U==="all"?g.value="":g.value=U,M.value="",le()},Y=()=>{v.value="",le()},ae=Ds(()=>{le()},300),z=()=>{var n;(n=document.getElementById("auctions"))==null||n.scrollIntoView({behavior:"smooth"})},y=()=>{w.push("/about")},P=n=>{w.push(`/item/${n.id}`)},H=n=>{w.push(`/item/${n.id}`)},K=n=>{w.push(`/item/${n.id}`)},J=async n=>{var U;console.log("Add to cart:",n.id),await c.addToCart(n)?(h.success(`${n.name} added to cart`),c.trackCartEvent("item_added",{item_id:n.id,item_name:n.name,item_price:n.target_amount,auction_type:(U=n.auction_type)==null?void 0:U.type,source_page:"homepage"})):h.error(c.error||"Failed to add item to cart")},ie=async n=>{var U;console.log("Remove from cart:",n.id),await c.removeFromCart(n)?(h.success(`${n.name} removed from cart`),c.trackCartEvent("item_removed",{item_id:n.id,item_name:n.name,item_price:n.target_amount,auction_type:(U=n.auction_type)==null?void 0:U.type,source_page:"homepage"})):h.error(c.error||"Failed to remove item from cart")},fe=async n=>{console.log("Checkout item:",n.id),await c.addToCart(n,!0)||h.error("Failed to proceed to checkout")},Se=n=>{console.log("Watch item:",n.id)},je=n=>{I.value=n,le()},Ce=()=>{I.value={},v.value="",g.value="cash",E.value=1,le()},Te=()=>{le()},b=()=>({"":"All Items",cash:"Daily Sale Items",online:"Online Auction Items",live:"Live Auction Items"})[g.value]||"Auction Items",f=()=>{var S,X,re,ne;const n=new URLSearchParams;if(v.value&&n.set("search",v.value),g.value&&n.set("type",g.value),m.value&&m.value!=="newest"&&n.set("sort",m.value),I.value){const se=I.value;(S=se.statuses)!=null&&S.length&&n.set("statuses",se.statuses.join(",")),(X=se.auctionTypes)!=null&&X.length&&n.set("auction_types",se.auctionTypes.join(",")),(re=se.locations)!=null&&re.length&&n.set("locations",se.locations.join(",")),(ne=se.conditions)!=null&&ne.length&&n.set("conditions",se.conditions.join(",")),se.priceMin&&n.set("price_min",se.priceMin.toString()),se.priceMax&&n.set("price_max",se.priceMax.toString()),se.hasImages&&n.set("has_images","1"),se.hasReserve&&n.set("has_reserve","1"),se.featuredOnly&&n.set("featured","1")}const t=n.toString(),U=t?`${window.location.pathname}?${t}`:window.location.pathname;window.history.replaceState({},"",U)},Q=()=>{var U,S,X,re;const n=new URLSearchParams(window.location.search);if(n.get("search")&&(v.value=n.get("search")||""),n.get("type")){const ne=n.get("type")||"";g.value=ne;const se=k.value.findIndex(be=>be.key===(ne||"all"));se!==-1&&(E.value=se)}n.get("sort")&&(m.value=n.get("sort")||"newest");const t={};n.get("statuses")&&(t.statuses=((U=n.get("statuses"))==null?void 0:U.split(","))||[]),n.get("auction_types")&&(t.auctionTypes=((S=n.get("auction_types"))==null?void 0:S.split(","))||[]),n.get("locations")&&(t.locations=((X=n.get("locations"))==null?void 0:X.split(","))||[]),n.get("conditions")&&(t.conditions=((re=n.get("conditions"))==null?void 0:re.split(","))||[]),n.get("price_min")&&(t.priceMin=parseFloat(n.get("price_min")||"0")),n.get("price_max")&&(t.priceMax=parseFloat(n.get("price_max")||"0")),n.get("has_images")&&(t.hasImages=!0),n.get("has_reserve")&&(t.hasReserve=!0),n.get("featured")&&(t.featuredOnly=!0),Object.keys(t).length>0&&(I.value=t)};return _e([v,g,m,I],()=>{f()},{deep:!0}),$e(async()=>{if(Q(),c.isEmpty){const n=window.cartItems||[];n.length>0?c.initializeCart(n):await c.fetchCart()}c.setLastShoppingPage(window.location.href),await Promise.all([s.fetchBranches(),s.fetchAuctionTypes(),r.fetchAdverts(),le()])}),_e(()=>B.path,n=>{(n==="/"||n==="/homepage")&&c.setLastShoppingPage(window.location.href)},{immediate:!1}),(n,t)=>{const U=gt("router-link");return a(),l("div",hl,[A(i(Lt),{adverts:p.value},null,8,["adverts"]),!p.value||p.value.length===0?(a(),l("section",fl,[t[11]||(t[11]=Be('<div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-900" data-v-87e05839></div><div class="absolute inset-0 bg-black bg-opacity-20" data-v-87e05839></div><div class="absolute top-0 left-0 w-full h-full" data-v-87e05839><div class="absolute top-20 left-10 w-72 h-72 bg-white bg-opacity-10 rounded-full blur-3xl" data-v-87e05839></div><div class="absolute bottom-20 right-10 w-96 h-96 bg-blue-300 bg-opacity-20 rounded-full blur-3xl" data-v-87e05839></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-white/5 to-transparent rounded-full" data-v-87e05839></div></div>',3)),A(i(we),{class:"relative z-10"},{default:O(()=>[e("div",bl,[e("div",yl,[t[7]||(t[7]=e("div",{class:"inline-flex items-center px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6 border border-white border-opacity-30"},[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Trusted by thousands of auctioneers in Malawi ")],-1)),t[8]||(t[8]=e("h1",{class:"text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"},[W(" Welcome to "),e("span",{class:"block bg-gradient-to-r from-blue-200 to-white bg-clip-text text-transparent"}," Trust Auctioneers ")],-1)),t[9]||(t[9]=e("p",{class:"text-xl lg:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed"}," Experience the future of auction management with our cutting-edge platform. Discover exceptional items, place confident bids, and join a community of passionate collectors. ",-1)),e("div",xl,[A(i(te),{variant:"primary",size:"md",onClick:z,class:"bg-white text-blue-600 hover:bg-blue-50 hover:text-blue-700 px-6 py-3 text-base font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border-0",style:{color:"#0068ff !important"}},{default:O(()=>[(a(),l("svg",wl,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"},null,-1)]))),t[5]||(t[5]=W(" Explore Live Auctions "))]),_:1,__:[5]}),e("button",{onClick:y,class:"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-6 py-3 text-base font-semibold rounded-lg backdrop-blur-sm transition-all duration-300 inline-flex items-center"},t[6]||(t[6]=[e("svg",{class:"w-4 h-4 mr-2 text-white hover:text-blue-600 transition-colors duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),W(" Learn More ")]))])])])]),_:1}),e("div",_l,[(a(),l("svg",kl,t[10]||(t[10]=[e("path",{d:"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z",opacity:".25"},null,-1),e("path",{d:"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z",opacity:".5"},null,-1),e("path",{d:"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"},null,-1)])))])])):L("",!0),e("section",Cl,[A(i(we),null,{default:O(()=>[e("div",$l,[A(i(pa),{modelValue:E.value,"onUpdate:modelValue":t[0]||(t[0]=S=>E.value=S),tabs:k.value,variant:"underline",size:"lg",centered:"",onTabChange:R,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Ml,[e("div",jl,[t[13]||(t[13]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),pe(e("input",{type:"text","onUpdate:modelValue":t[1]||(t[1]=S=>v.value=S),placeholder:"Search auctions...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full",onInput:t[2]||(t[2]=(...S)=>i(ae)&&i(ae)(...S))},null,544),[[Ie,v.value]]),v.value?(a(),l("button",{key:0,onClick:Y,class:"absolute inset-y-0 right-0 pr-3 flex items-center"},t[12]||(t[12]=[e("svg",{class:"h-4 w-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):L("",!0)])])])]),_:1})]),e("section",Il,[A(i(we),null,{default:O(()=>{var S;return[e("div",Sl,[e("aside",Al,[e("div",Bl,[e("div",Tl,[e("div",Nl,[e("div",zl,[t[14]||(t[14]=e("h3",{class:"text-base font-semibold text-gray-900"},"Filters",-1)),oe.value?(a(),l("button",{key:0,onClick:Ce,class:"text-xs text-blue-600 hover:text-blue-700 font-medium"}," Clear all ")):L("",!0)])]),e("div",Dl,[A(i(pn),{"auction-type-options":V.value,"location-options":Z.value,"show-location-filter":!0,onFilterChange:je,onResetFilters:Ce},null,8,["auction-type-options","location-options"])])])])]),e("main",Ll,[e("div",Pl,[e("div",El,[e("div",Vl,[e("h1",Rl,x(b()),1),(S=F.value)!=null&&S.total?(a(),l("span",Fl,x(F.value.total.toLocaleString())+" "+x(F.value.total===1?"item":"items"),1)):L("",!0)]),e("div",ql,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 font-medium"},"Sort by",-1)),pe(e("select",{"onUpdate:modelValue":t[3]||(t[3]=X=>m.value=X),onChange:Te,class:"text-sm border border-gray-300 rounded-lg px-4 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-medium"},t[15]||(t[15]=[e("option",{value:"newest"},"Newest First",-1),e("option",{value:"oldest"},"Oldest First",-1),e("option",{value:"price_low"},"Price: Low to High",-1),e("option",{value:"price_high"},"Price: High to Low",-1),e("option",{value:"ending_soon"},"Ending Soon",-1)]),544),[[It,m.value]])])])]),N.value?(a(),l("div",Hl,[e("div",Ol,[A(i(ze),{size:"lg"}),t[17]||(t[17]=e("p",{class:"mt-4 text-sm text-gray-500"},"Loading auction items...",-1))])])):F.value&&F.value.data&&F.value.data.length>0?(a(),l("div",Wl,[e("div",Ul,[(a(!0),l(ue,null,ce(F.value.data,X=>(a(),de(i(Rt),{key:X.id,item:X,onViewDetails:P,onPlaceBid:H,onPreview:K,onAddToCart:J,onRemoveFromCart:ie,onCheckout:fe,onWatchItem:Se},null,8,["item"]))),128))]),C.value?L("",!0):(a(),l("div",Yl,[A(i(te),{variant:"outline",size:"lg",onClick:ee,loading:j.value,class:"px-8 py-3 bg-white border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-all duration-200 rounded-xl font-medium"},{default:O(()=>[j.value?L("",!0):(a(),l("svg",Kl,t[18]||(t[18]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]))),W(" "+x(j.value?"Loading...":"Load More Items"),1)]),_:1},8,["loading"])]))])):(a(),l("div",Ql,[e("div",Xl,[t[21]||(t[21]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),t[22]||(t[22]=e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"No auction items found",-1)),t[23]||(t[23]=e("p",{class:"text-gray-600 mb-6"},"We couldn't find any items matching your criteria. Try adjusting your filters or search terms.",-1)),e("div",Gl,[A(i(te),{variant:"primary",onClick:Ce,class:"px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"},{default:O(()=>t[19]||(t[19]=[W(" Clear All Filters ")])),_:1,__:[19]}),t[20]||(t[20]=e("p",{class:"text-sm text-gray-500"},"or check back later for new auctions",-1))])])]))])])]}),_:1})]),e("section",Zl,[A(i(we),null,{default:O(()=>t[24]||(t[24]=[e("div",{class:"text-center mb-12"},[e("h2",{class:"text-3xl font-bold text-gray-900 mb-4"},"Why Choose Vertigo AMS?"),e("p",{class:"text-lg text-gray-600 max-w-2xl mx-auto"}," Join thousands of satisfied customers who trust our platform for their auction needs ")],-1),e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-8"},[e("div",{class:"text-center"},[e("div",{class:"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Secure Bidding"),e("p",{class:"text-gray-600"},"Safe and secure bidding process with verified payments")]),e("div",{class:"text-center"},[e("div",{class:"bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Real-time Updates"),e("p",{class:"text-gray-600"},"Live auction updates and instant bid notifications")]),e("div",{class:"text-center"},[e("div",{class:"bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"},"Trusted Platform"),e("p",{class:"text-gray-600"},"Established reputation with thousands of successful auctions")])],-1)])),_:1,__:[24]})]),A(i(Ft)),A(i(Bt)),i(c).cartCount>0?(a(),l("div",Jl,[A(U,{to:"/cart",class:"flex items-center justify-center w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"},{default:O(()=>[t[25]||(t[25]=e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})],-1)),e("span",ei,x(i(c).cartCount>9?"9+":i(c).cartCount),1)]),_:1,__:[25]})])):L("",!0)])}}});const ot=ke(ti,[["__scopeId","data-v-87e05839"]]),si={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},oi={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},ai={class:"py-6"},ri={class:"flex items-center justify-between"},ni={class:"flex items-center space-x-4"},li={class:"text-2xl font-bold text-gray-100 tracking-tight"},ii={key:0,class:"flex items-center space-x-3"},di={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ui={key:0,class:"flex justify-center py-20"},ci={class:"text-center"},mi={key:1,class:"text-center py-20"},vi={class:"max-w-4xl mx-auto"},gi={class:"bg-white rounded-3xl shadow-xl p-12 mb-8"},pi={class:"mb-10"},hi={key:2,class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},fi={class:"lg:col-span-2 space-y-4"},bi={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},yi={class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},xi={class:"flex items-center justify-between"},wi={class:"flex items-center space-x-3"},_i={class:"flex items-center space-x-3"},ki={class:"text-xs text-gray-600"},Ci={class:"flex items-center space-x-3"},$i={class:"text-right"},Mi={class:"text-lg font-bold text-gray-900"},ji={class:"flex items-center space-x-1"},Ii={class:"p-6"},Si={class:"space-y-3"},Ai={key:0,class:"space-y-6"},Bi={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Ti={class:"px-6 py-3 border-b border-gray-200 bg-green-50"},Ni={class:"flex items-center justify-between"},zi={class:"flex items-center space-x-2"},Di={class:"text-xs text-green-700 bg-green-100 px-2 py-1 rounded-full font-medium"},Li={class:"text-sm font-medium text-green-700"},Pi={class:"p-4 space-y-3"},Ei={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Vi={class:"px-6 py-3 border-b border-gray-200 bg-blue-50"},Ri={class:"flex items-center justify-between"},Fi={class:"flex items-center space-x-2"},qi={class:"text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-full font-medium"},Hi={class:"text-sm font-medium text-blue-700"},Oi={class:"p-4 space-y-3"},Wi={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200"},Ui={class:"px-6 py-3 border-b border-gray-200 bg-red-50"},Yi={class:"flex items-center justify-between"},Ki={class:"flex items-center space-x-2"},Qi={class:"text-xs text-red-700 bg-red-100 px-2 py-1 rounded-full font-medium"},Xi={class:"text-sm font-medium text-red-700"},Gi={class:"p-4 space-y-3"},Zi={class:"bg-gradient-to-r from-white to-gray-50 rounded-xl shadow-md border border-gray-200 p-4"},Ji={class:"space-y-4"},ed={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},td={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},sd={class:"bg-white rounded-lg p-4 border border-gray-200"},od={class:"flex items-center justify-between"},ad={class:"flex items-center space-x-3"},rd={class:"text-sm font-bold text-gray-900"},nd={class:"text-right"},ld={class:"text-lg font-bold text-blue-600"},id={class:"lg:col-span-1"},dd={class:"sticky top-8 space-y-4"},ud={class:"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"},cd={class:"p-6 space-y-4"},md={class:"space-y-4"},vd={class:"flex justify-between items-center py-3 border-b border-gray-100"},gd={class:"text-gray-700 font-semibold"},pd={class:"text-xl font-bold text-gray-900"},hd={key:0,class:"flex justify-between items-center py-3 bg-green-50 rounded-lg px-4"},fd={class:"flex items-center space-x-3"},bd={class:"text-sm font-medium text-gray-700"},yd={class:"text-sm font-bold text-green-700"},xd={key:1,class:"flex justify-between items-center py-3 bg-blue-50 rounded-lg px-4"},wd={class:"flex items-center space-x-3"},_d={class:"text-sm font-medium text-gray-700"},kd={class:"text-sm font-bold text-blue-700"},Cd={key:2,class:"flex justify-between items-center py-3 bg-red-50 rounded-lg px-4"},$d={class:"flex items-center space-x-3"},Md={class:"text-sm font-medium text-gray-700"},jd={class:"text-sm font-bold text-red-700"},Id={class:"bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl p-6 text-white"},Sd={class:"flex justify-between items-center"},Ad={class:"text-2xl font-bold"},Bd={key:0,class:"w-6 h-6 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Td=me({__name:"Cart",setup(o){const s=Ae(),r=Me(),c=T(!1),h=T(!1),w=q(()=>s.isEmpty?"Your Cart is Empty":"Shopping Cart"),B=q(()=>s.cashItems.reduce((_,m)=>_+s.getItemSubtotal(m),0)),N=q(()=>s.onlineItems.reduce((_,m)=>_+s.getItemSubtotal(m),0)),j=q(()=>s.liveItems.reduce((_,m)=>_+s.getItemSubtotal(m),0)),v=async(_,m)=>{if(m<=0){g(_);return}try{await s.updateQuantity(_.id,m)?(r.success("Quantity updated"),s.trackCartEvent("quantity_updated",{item_id:_.id,item_name:_.name,new_quantity:m,item_price:_.target_amount})):r.error(s.error||"Failed to update item quantity")}catch(I){console.error("Error updating quantity:",I),r.error("Failed to update item quantity")}},g=async _=>{try{await s.removeFromCart(_)?(r.success(`${_.name} removed from cart`),s.trackCartEvent("item_removed",{item_id:_.id,item_name:_.name,item_price:_.target_amount,quantity:_.quantity||1})):r.error(s.error||"Failed to remove item from cart")}catch(m){console.error("Error removing item:",m),r.error("Failed to remove item from cart")}},E=async()=>{if(s.isEmpty){r.warning("Your cart is already empty");return}const _=s.cartCount,m=`Are you sure you want to remove all ${_} ${_===1?"item":"items"} from your cart? This action cannot be undone.`;if(confirm(m))try{await s.clearCart()?(r.success(`Cart cleared successfully! ${_} ${_===1?"item":"items"} removed.`),s.trackCartEvent("cart_cleared",{items_count:_,cart_total:s.cartTotal})):r.error(s.error||"Failed to clear cart")}catch(I){console.error("Error clearing cart:",I),r.error("Failed to clear cart")}},D=()=>{try{r.info("Returning to shopping...");const _=s.getLastShoppingPage();let m="/home-vue";_&&_!==window.location.href?(m=_,console.log("Returning to last shopping page:",m)):console.log("No last shopping page found, going to homepage"),setTimeout(()=>{window.location.href=m},500)}catch(_){console.error("Error in handleContinueShopping:",_),window.location.href="/home-vue"}},M=async()=>{if(s.isEmpty){r.warning("Your cart is empty. Add some items before proceeding to checkout.");return}if(s.cartTotal<=0){r.warning("Cart total must be greater than zero to proceed.");return}c.value=!0;try{r.info("Validating cart items...");const _=await s.validateCartItems();if(!_.valid&&(_.issues.forEach(m=>{r.warning(m)}),s.isEmpty)){r.error("Your cart is now empty. Please add items before proceeding."),c.value=!1;return}r.success(`Proceeding to checkout with ${s.cartCount} ${s.cartCount===1?"item":"items"}...`),s.trackCartEvent("checkout_initiated",{items_count:s.cartCount,cart_total:s.cartTotal}),await new Promise(m=>setTimeout(m,500)),window.location.href="/checkout"}catch(_){console.error("Checkout error:",_),r.error("Failed to proceed to checkout. Please try again."),c.value=!1}};return $e(async()=>{const _=window.cartItems;if(s.initializeCart(_),s.trackCartEvent("cart_page_viewed",{items_count:s.cartCount,cart_total:s.cartTotal}),!s.isEmpty&&s.isOnline)try{const m=await s.validateCartItems();!m.valid&&m.issues.length>0&&m.issues.forEach(I=>{r.warning(I)})}catch(m){console.warn("Cart validation failed on mount:",m)}}),(_,m)=>(a(),l("div",si,[e("div",oi,[m[5]||(m[5]=Be('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-06704d34></div><div class="absolute inset-0" data-v-06704d34><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md" data-v-06704d34></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg" data-v-06704d34></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-06704d34></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-06704d34></div></div>',2)),A(i(we),{class:"relative z-10"},{default:O(()=>[e("div",ai,[e("div",ri,[e("div",ni,[m[2]||(m[2]=e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500/30 to-indigo-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[e("h1",li,x(w.value),1),m[1]||(m[1]=e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-blue-200 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Shopping Cart")])],-1))])]),i(s).isEmpty?L("",!0):(a(),l("div",ii,[A(i(te),{variant:"outline",size:"sm",onClick:D,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:O(()=>m[3]||(m[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Continue Shopping ")])),_:1,__:[3]}),A(i(te),{size:"sm",onClick:M,loading:c.value,class:"!bg-gradient-to-r !from-blue-500 !to-indigo-600 !text-white hover:!from-blue-600 hover:!to-indigo-700 font-semibold shadow-lg hover:shadow-xl text-sm transition-all duration-200 border-0"},{default:O(()=>[c.value?L("",!0):(a(),l("svg",di,m[4]||(m[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),W(" "+x(c.value?"Processing...":"Checkout"),1)]),_:1},8,["loading"])]))])])]),_:1})]),A(i(we),{class:"py-12"},{default:O(()=>[i(s).loading?(a(),l("div",ui,[e("div",ci,[A(i(ze),{size:"lg"}),m[6]||(m[6]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading your cart...",-1))])])):i(s).isEmpty?(a(),l("div",mi,[e("div",vi,[e("div",gi,[e("div",pi,[m[8]||(m[8]=e("div",{class:"relative inline-block mb-8"},[e("div",{class:"w-32 h-32 bg-gradient-to-br from-blue-100 via-blue-200 to-blue-300 rounded-full flex items-center justify-center mx-auto shadow-lg"},[e("svg",{class:"w-16 h-16 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])]),e("div",{class:"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"}),e("div",{class:"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 rounded-full animate-pulse"})],-1)),m[9]||(m[9]=e("h3",{class:"text-3xl font-bold text-gray-900 mb-4"},"Your cart is waiting for treasures!",-1)),m[10]||(m[10]=e("p",{class:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto"}," Discover amazing auction items and start building your collection. From vintage collectibles to modern masterpieces. ",-1)),A(i(te),{size:"lg",onClick:D,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"},{default:O(()=>m[7]||(m[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1),W(" Start Shopping Now ")])),_:1,__:[7]})])]),m[11]||(m[11]=e("div",{class:"grid grid-cols-1 md:grid-cols-3 gap-6"},[e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Cash Auctions"),e("p",{class:"text-gray-600 mb-4"},"Immediate purchases with instant ownership transfer"),e("div",{class:"text-sm text-green-600 font-medium"},"• Quick checkout • Instant delivery")]),e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Online Auctions"),e("p",{class:"text-gray-600 mb-4"},"Bid from anywhere, anytime with our online platform"),e("div",{class:"text-sm text-blue-600 font-medium"},"• Remote bidding • Extended timeframes")]),e("div",{class:"bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"},[e("div",{class:"w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("h4",{class:"text-xl font-bold text-gray-900 mb-3"},"Live Auctions"),e("p",{class:"text-gray-600 mb-4"},"Experience the thrill of real-time competitive bidding"),e("div",{class:"text-sm text-red-600 font-medium"},"• Real-time bidding • Live auctioneer")])],-1))])])):(a(),l("div",hi,[e("div",fi,[e("div",bi,[e("div",yi,[e("div",xi,[e("div",wi,[e("div",_i,[m[13]||(m[13]=e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[m[12]||(m[12]=e("h3",{class:"text-lg font-bold text-gray-900"},"Your Items",-1)),e("p",ki,x(i(s).cartCount)+" "+x(i(s).cartCount===1?"item":"items")+" selected",1)])]),A(i(Ve),{variant:"default",size:"sm",class:"bg-blue-100 text-blue-800 font-bold px-2 py-1"},{default:O(()=>[W(x(i(s).cartCount),1)]),_:1})]),e("div",Ci,[e("div",$i,[m[14]||(m[14]=e("p",{class:"text-xs text-gray-600"},"Total Value",-1)),e("p",Mi,x(i(s).formatCurrency(i(s).cartTotal)),1)]),e("div",ji,[A(i(te),{variant:"ghost",size:"sm",onClick:m[0]||(m[0]=I=>h.value=!h.value),class:"text-gray-500 hover:text-blue-600 hover:bg-blue-50 p-2 rounded-md",title:h.value?"Show unified view":"Group by auction type"},{default:O(()=>m[15]||(m[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)])),_:1,__:[15]},8,["title"]),A(i(te),{variant:"ghost",size:"sm",onClick:E,disabled:i(s).loading,class:"text-gray-500 hover:text-red-600 hover:bg-red-50 p-2 rounded-md",title:"Clear all items"},{default:O(()=>m[16]||(m[16]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:1,__:[16]},8,["disabled"])])])])]),e("div",Ii,[e("div",Si,[(a(!0),l(ue,null,ce(i(s).items,I=>(a(),de(i(Ye),{key:`all-${I.id}`,item:I,onUpdateQuantity:v,onRemove:g},null,8,["item"]))),128))])])]),h.value?(a(),l("div",Ai,[i(s).cashItems.length>0?(a(),l("div",Bi,[e("div",Ti,[e("div",Ni,[e("div",zi,[m[17]||(m[17]=e("div",{class:"w-3 h-3 bg-green-500 rounded-full"},null,-1)),m[18]||(m[18]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Cash Auction",-1)),e("span",Di,x(i(s).cashItems.length),1)]),e("span",Li,x(i(s).formatCurrency(B.value)),1)])]),e("div",Pi,[(a(!0),l(ue,null,ce(i(s).cashItems,I=>(a(),de(i(Ye),{key:`cash-${I.id}`,item:I,onUpdateQuantity:v,onRemove:g},null,8,["item"]))),128))])])):L("",!0),i(s).onlineItems.length>0?(a(),l("div",Ei,[e("div",Vi,[e("div",Ri,[e("div",Fi,[m[19]||(m[19]=e("div",{class:"w-3 h-3 bg-blue-500 rounded-full"},null,-1)),m[20]||(m[20]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Online Auction",-1)),e("span",qi,x(i(s).onlineItems.length),1)]),e("span",Hi,x(i(s).formatCurrency(N.value)),1)])]),e("div",Oi,[(a(!0),l(ue,null,ce(i(s).onlineItems,I=>(a(),de(i(Ye),{key:`online-${I.id}`,item:I,onUpdateQuantity:v,onRemove:g},null,8,["item"]))),128))])])):L("",!0),i(s).liveItems.length>0?(a(),l("div",Wi,[e("div",Ui,[e("div",Yi,[e("div",Ki,[m[21]||(m[21]=e("div",{class:"w-3 h-3 bg-red-500 rounded-full"},null,-1)),m[22]||(m[22]=e("h4",{class:"text-sm font-semibold text-gray-900"},"Live Auction",-1)),e("span",Qi,x(i(s).liveItems.length),1)]),e("span",Xi,x(i(s).formatCurrency(j.value)),1)])]),e("div",Gi,[(a(!0),l(ue,null,ce(i(s).liveItems,I=>(a(),de(i(Ye),{key:`live-${I.id}`,item:I,onUpdateQuantity:v,onRemove:g},null,8,["item"]))),128))])])):L("",!0)])):L("",!0),e("div",Zi,[e("div",Ji,[e("div",ed,[A(i(te),{variant:"outline",onClick:E,disabled:i(s).loading||i(s).isEmpty,class:"border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-medium py-2 px-4 rounded-lg transition-all duration-200"},{default:O(()=>m[23]||(m[23]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),W(" Clear Cart ")])),_:1,__:[23]},8,["disabled"]),A(i(te),{variant:"outline",onClick:D,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium py-2 px-4 rounded-lg transition-all duration-200"},{default:O(()=>m[24]||(m[24]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Continue Shopping ")])),_:1,__:[24]}),A(i(te),{onClick:M,loading:c.value,disabled:i(s).isEmpty,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:O(()=>[c.value?L("",!0):(a(),l("svg",td,m[25]||(m[25]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),W(" "+x(c.value?"Processing...":"Proceed to Checkout"),1)]),_:1},8,["loading","disabled"])]),e("div",sd,[e("div",od,[e("div",ad,[m[27]||(m[27]=e("div",{class:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"})])],-1)),e("div",null,[m[26]||(m[26]=e("p",{class:"text-xs text-gray-600"},"Items in Cart",-1)),e("p",rd,x(i(s).cartCount)+" "+x(i(s).cartCount===1?"item":"items"),1)])]),e("div",nd,[m[28]||(m[28]=e("p",{class:"text-xs text-gray-600"},"Total Value",-1)),e("p",ld,x(i(s).formatCurrency(i(s).cartTotal)),1)])])])])])]),e("div",id,[e("div",dd,[e("div",ud,[m[34]||(m[34]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white"},[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold"},"Order Summary"),e("p",{class:"text-blue-100 text-xs"},"Review your selection")])])],-1)),e("div",cd,[e("div",md,[e("div",vd,[e("span",gd,"Items ("+x(i(s).cartCount)+")",1),e("span",pd,x(i(s).formatCurrency(i(s).cartTotal)),1)]),i(s).cashItems.length>0?(a(),l("div",hd,[e("div",fd,[m[29]||(m[29]=e("div",{class:"w-4 h-4 bg-green-500 rounded-full"},null,-1)),e("span",bd,"Cash Items ("+x(i(s).cashItems.length)+")",1)]),e("span",yd,x(i(s).formatCurrency(B.value)),1)])):L("",!0),i(s).onlineItems.length>0?(a(),l("div",xd,[e("div",wd,[m[30]||(m[30]=e("div",{class:"w-4 h-4 bg-blue-500 rounded-full"},null,-1)),e("span",_d,"Online Items ("+x(i(s).onlineItems.length)+")",1)]),e("span",kd,x(i(s).formatCurrency(N.value)),1)])):L("",!0),i(s).liveItems.length>0?(a(),l("div",Cd,[e("div",$d,[m[31]||(m[31]=e("div",{class:"w-4 h-4 bg-red-500 rounded-full"},null,-1)),e("span",Md,"Live Items ("+x(i(s).liveItems.length)+")",1)]),e("span",jd,x(i(s).formatCurrency(j.value)),1)])):L("",!0)]),e("div",Id,[e("div",Sd,[m[32]||(m[32]=e("span",{class:"text-lg font-bold"},"Grand Total",-1)),e("span",Ad,x(i(s).formatCurrency(i(s).cartTotal)),1)])]),A(i(te),{size:"lg",class:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",loading:c.value,onClick:M},{default:O(()=>[c.value?L("",!0):(a(),l("svg",Bd,m[33]||(m[33]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))),W(" "+x(c.value?"Processing...":"Proceed to Checkout"),1)]),_:1},8,["loading"])])]),m[35]||(m[35]=e("div",{class:"bg-white rounded-2xl shadow-lg border border-gray-100 p-6"},[e("h4",{class:"font-bold text-gray-900 mb-4 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),W(" Secure & Trusted ")]),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" SSL encrypted checkout process ")])])],-1))])])]))]),_:1})]))}});const at=ke(Td,[["__scopeId","data-v-06704d34"]]),Nd={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},zd={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},Dd={class:"py-6"},Ld={class:"flex items-center justify-between"},Pd={class:"flex items-center space-x-3"},Ed={key:0,class:"flex justify-center py-20"},Vd={class:"text-center"},Rd={key:1,class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Fd={class:"lg:col-span-2 space-y-6"},qd={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},Hd={class:"p-6"},Od={class:"flex items-center justify-between"},Wd={class:"flex items-center"},Ud={key:0,class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20"},Yd={key:1},Kd={class:"ml-3"},Qd={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},Xd={key:0,class:"p-6"},Gd={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Zd={class:"flex justify-end space-x-3"},Jd={key:1,class:"p-6"},eu={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},tu={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},su={class:"flex justify-between space-x-3"},ou={key:2,class:"p-6"},au={class:"space-y-4"},ru={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},nu=["onClick"],lu={class:"flex items-center space-x-3"},iu={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},du={class:"flex items-center space-x-2"},uu={key:0,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},cu={key:1,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},mu={key:2,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},vu={key:3,class:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},gu={key:4,class:"ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full"},pu={key:0,class:"space-y-4 p-4 bg-blue-50 rounded-lg"},hu={class:"bg-white rounded-lg p-4 border border-blue-200"},fu={class:"bg-gray-50 rounded-lg p-3 border"},bu={class:"flex justify-between items-center"},yu={key:0,class:"mt-2 p-2 bg-red-50 rounded border border-red-200"},xu={key:1,class:"space-y-4 p-4 bg-gray-50 rounded-lg"},wu={class:"grid grid-cols-1 gap-4"},_u={class:"grid grid-cols-2 gap-4"},ku={key:0,class:"space-y-2"},Cu={key:0,class:"flex items-center space-x-2 text-sm"},$u={key:0,class:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},Mu={key:1,class:"flex items-center space-x-2 text-xs text-gray-500"},ju={class:"flex-1 bg-gray-200 rounded-full h-1 ml-2"},Iu={key:2,class:"space-y-4 p-4 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg border border-green-200"},Su={class:"grid grid-cols-1 gap-4"},Au={class:"grid grid-cols-2 gap-4"},Bu={class:"flex justify-between space-x-3"},Tu={key:3,class:"p-6"},Nu={class:"space-y-6"},zu={class:"bg-gray-50 rounded-lg p-4"},Du={class:"space-y-3"},Lu={class:"flex justify-between"},Pu={class:"text-gray-600"},Eu={class:"font-medium"},Vu={class:"flex justify-between"},Ru={class:"font-medium"},Fu={class:"border-t pt-3 flex justify-between"},qu={class:"text-lg font-bold text-gray-900"},Hu={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ou={class:"bg-gray-50 rounded-lg p-4"},Wu={class:"space-y-2 text-sm"},Uu={class:"bg-gray-50 rounded-lg p-4"},Yu={class:"space-y-2 text-sm"},Ku={class:"bg-gray-50 rounded-lg p-4"},Qu={class:"text-sm"},Xu={key:0,class:"text-gray-600"},Gu={key:1,class:"text-gray-600"},Zu={key:2,class:"text-gray-600"},Ju={key:3,class:"text-gray-600"},ec={key:0,class:"p-4 bg-blue-50 border border-blue-200 rounded-lg"},tc={class:"flex items-center space-x-3"},sc={class:"flex-1"},oc={class:"text-sm text-blue-700"},ac={key:1,class:"p-4 bg-red-50 border border-red-200 rounded-lg"},rc={class:"flex items-start space-x-3"},nc={class:"flex-1"},lc={class:"text-sm text-red-700"},ic={class:"flex items-start space-x-3"},dc={class:"flex justify-between space-x-3"},uc={key:0},cc={key:1},mc={key:2},vc={class:"lg:col-span-1"},gc={class:"sticky top-8 space-y-4"},pc={class:"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"},hc={class:"px-6 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white"},fc={class:"flex items-center space-x-3"},bc={class:"text-green-100 text-xs"},yc={class:"p-6 space-y-4"},xc={class:"space-y-3 max-h-64 overflow-y-auto"},wc={class:"flex-1 min-w-0"},_c={class:"text-sm font-medium text-gray-900 truncate"},kc={class:"text-xs text-gray-500"},Cc={class:"text-sm font-medium text-gray-900"},$c={class:"space-y-3 pt-4 border-t border-gray-200"},Mc={class:"flex justify-between text-sm"},jc={class:"font-medium"},Ic={class:"flex justify-between text-sm"},Sc={class:"font-medium"},Ac={class:"flex justify-between text-lg font-bold pt-3 border-t border-gray-200"},Bc={class:"text-green-600"},Tc=me({__name:"Checkout",setup(o){const s=Ae(),r=Me(),c=T(!1),h=T(!1),w=T(!1),B=T(!1),N=T(!1),j=T(""),v=T(""),g=T("customer"),E=T(!1),D=T(""),M=T(""),_=T(!1),m=T([{id:"customer",title:"Customer Info",description:"Contact details",active:!0,completed:!1},{id:"billing",title:"Billing Address",description:"Billing information",active:!1,completed:!1},{id:"payment",title:"Payment",description:"Payment method",active:!1,completed:!1},{id:"review",title:"Review",description:"Confirm order",active:!1,completed:!1}]),I=T({firstName:"",lastName:"",email:"",phone:""}),F=T({firstName:"",lastName:"",email:"",phone:""}),$=T({address:"",city:"",state:"",postalCode:"",country:""}),u=T({address:"",city:"",state:"",postalCode:"",country:""}),d=T({method:"dpopay",cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:"",currency:"MWK"}),p=T({cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:""}),C=T([{id:"dpopay",name:"DPO Pay",description:"Pay with DPO Pay - Credit Cards, Mobile Money & Bank Transfer",enabled:!0,icon:"credit-card",currencies:["MWK","USD","EUR","GBP","ZAR"]},{id:"cash",name:"Cash on Delivery",description:"Pay when you receive your items",enabled:!0},{id:"bank",name:"Bank Transfer",description:"Direct bank transfer (Coming Soon)",enabled:!1},{id:"card",name:"Credit/Debit Card",description:"Pay securely with your credit or debit card (Coming Soon)",enabled:!1}]),k=(n,t)=>n.completed?"bg-green-500 text-white":n.active?"bg-blue-500 text-white":"bg-gray-200 text-gray-500",V=()=>{window.location.href="/cart"},Z=()=>{F.value={firstName:"",lastName:"",email:"",phone:""};let n=!1;if(I.value.firstName.trim()||(F.value.firstName="First name is required",n=!0),I.value.lastName.trim()||(F.value.lastName="Last name is required",n=!0),I.value.email.trim()?/\S+@\S+\.\S+/.test(I.value.email)||(F.value.email="Please enter a valid email address",n=!0):(F.value.email="Email is required",n=!0),I.value.phone.trim()||(F.value.phone="Phone number is required",n=!0),n){r.error("Please fix the errors below");return}h.value=!0,setTimeout(()=>{z(),h.value=!1,r.success("Customer information saved successfully")},1e3)},oe=()=>{u.value={address:"",city:"",state:"",postalCode:"",country:""};let n=!1;if($.value.address.trim()||(u.value.address="Street address is required",n=!0),$.value.city.trim()||(u.value.city="City is required",n=!0),$.value.state.trim()||(u.value.state="State/Province is required",n=!0),$.value.postalCode.trim()||(u.value.postalCode="Postal code is required",n=!0),$.value.country.trim()||(u.value.country="Country is required",n=!0),n){r.error("Please fix the errors below");return}w.value=!0,setTimeout(()=>{z(),w.value=!1,r.success("Billing address saved successfully")},1e3)},le=()=>{p.value={cardNumber:"",expiryDate:"",cvv:"",cardName:"",customerName:"",customerEmail:"",customerPhone:""};let n=!1;if(d.value.method==="dpopay")d.value.customerName.trim()?d.value.customerName.trim().length<2&&(p.value.customerName="Please enter a valid name",n=!0):(p.value.customerName="Full name is required",n=!0),d.value.customerEmail.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d.value.customerEmail)||(p.value.customerEmail="Please enter a valid email address",n=!0):(p.value.customerEmail="Email address is required",n=!0),d.value.customerPhone.trim()&&(/^[\+]?[0-9\-\(\)\s]+$/.test(d.value.customerPhone)||(p.value.customerPhone="Please enter a valid phone number",n=!0));else if(d.value.method==="card"){if(d.value.cardNumber.replace(/\s/g,"")?_.value||(p.value.cardNumber||(p.value.cardNumber="Please enter a valid card number"),n=!0):(p.value.cardNumber="Card number is required",n=!0),!d.value.expiryDate.trim())p.value.expiryDate="Expiry date is required",n=!0;else if(d.value.expiryDate.length!==5)p.value.expiryDate="Please enter a valid expiry date (MM/YY)",n=!0;else{const[U,S]=d.value.expiryDate.split("/"),X=new Date,re=X.getFullYear()%100,ne=X.getMonth()+1,se=parseInt(U),be=parseInt(S);se<1||se>12?(p.value.expiryDate="Invalid month",n=!0):(be<re||be===re&&se<ne)&&(p.value.expiryDate="Card has expired",n=!0)}if(!d.value.cvv.trim())p.value.cvv="CVV is required",n=!0;else{const U=D.value==="American Express"?4:3;d.value.cvv.length!==U&&(p.value.cvv=`CVV must be ${U} digits`,n=!0)}d.value.cardName.trim()?d.value.cardName.trim().length<2&&(p.value.cardName="Please enter a valid name",n=!0):(p.value.cardName="Name on card is required",n=!0)}else d.value.method;if(n){r.error("Please fix the errors below");return}B.value=!0,setTimeout(()=>{z(),B.value=!1,r.success("Payment method saved successfully")},1e3)},ee=n=>{let t=0,U=!1;for(let S=n.length-1;S>=0;S--){let X=parseInt(n.charAt(S));U&&(X*=2,X>9&&(X-=9)),t+=X,U=!U}return t%10===0},R=async()=>{if(j.value="",v.value="",!E.value){const n="Please agree to the terms and conditions to continue";j.value=n,r.error(n);return}N.value=!0,v.value="Initializing order...";try{if(d.value.method==="dpopay")v.value="Preparing DPO Pay transaction...",await Y();else if(d.value.method==="cash")v.value="Processing cash on delivery order...",await ae();else{const n="Selected payment method is not available yet";j.value=n,r.error(n),N.value=!1,v.value=""}}catch(n){console.error("Order placement error:",n);let t="Failed to place order. Please try again.";n.message&&(t=n.message),j.value=t,r.error(t),N.value=!1,v.value=""}},Y=async()=>{var n;try{if(s.isEmpty)throw new Error("Your cart is empty. Please add items before checkout.");if(s.cartTotal<=0)throw new Error("Invalid cart total. Please refresh and try again.");if(s.cartTotal>999999.99)throw new Error("Cart total exceeds DPO Pay maximum limit of MK 999,999.99. Please reduce your order or contact support for large transactions.");const t={customer:{firstName:I.value.firstName,lastName:I.value.lastName,email:I.value.email,phone:I.value.phone},billing:{address:$.value.address,city:$.value.city,state:$.value.state,postalCode:$.value.postalCode,country:$.value.country},cart:{items:s.items,total:s.cartTotal},currency:"MWK"};v.value="Creating secure payment token...",r.info("Creating secure payment token...");const U=await fetch("/dpopay/checkout-payment",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||"",Accept:"application/json"},body:JSON.stringify(t)});if(!U.ok){let X="Payment processing failed. Please try again.",re=null,ne=null;try{const se=await U.json();se.message&&(X=se.message),re=se.error_code,ne=se.technical_error||se.error,(re||ne)&&console.error("DPO Pay API Error:",{code:re,message:X,technical:ne})}catch{try{const be=await U.text();console.error("DPO Pay Raw Error Response:",be),be.includes("Payment amount cannot exceed")?X="Cart total exceeds DPO Pay maximum limit of MK 999,999.99. Please reduce your order or contact support for large transactions.":be.includes("Invalid company token")?X="Payment gateway configuration error. Please contact support.":be.includes("Service Type not active")?X="Payment service temporarily unavailable. Please try again later.":be&&(X="Payment processing error. Please try again or contact support.")}catch{X=`Connection error (${U.status}). Please check your internet connection and try again.`}}throw new Error(X)}const S=await U.json();if(S.success&&S.payment_url){v.value="Payment token created! Redirecting to DPO Pay...",r.success("Payment token created! Redirecting to DPO Pay..."),localStorage.setItem("vertigo_order_ref",S.order_reference),localStorage.setItem("vertigo_transaction_token",S.transaction_token),localStorage.setItem("vertigo_payment_amount",s.cartTotal.toString()),localStorage.setItem("vertigo_payment_currency",d.value.currency);let X=3;const re=setInterval(()=>{v.value=`Redirecting to DPO Pay in ${X} seconds...`,X--,X<0&&(clearInterval(re),v.value="Redirecting to payment gateway...",window.location.href=S.payment_url)},1e3)}else{let X=S.message||"Failed to create payment token";throw(S.error_code||S.technical_error)&&console.error("DPO Pay API Error Details:",{code:S.error_code,message:X,technical:S.technical_error}),new Error(X)}}catch(t){console.error("DPO Payment error:",t);let U="Failed to process DPO payment. Please try again.";t.message.includes("Cart total exceeds")?U=t.message:t.message.includes("HTTP error")?U="Unable to connect to payment gateway. Please check your internet connection and try again.":t.message.includes("cart is empty")?U="Your cart is empty. Please add items before proceeding to checkout.":t.message.includes("Invalid cart total")?U="There was an issue with your cart total. Please refresh the page and try again.":t.message.includes("Failed to create payment token")&&(U="Payment token creation failed. This may be a temporary issue with the payment gateway. Please try again in a few moments."),j.value=U,r.error(U),N.value=!1,v.value=""}},ae=async()=>{try{r.info("Processing cash on delivery order..."),await new Promise(n=>setTimeout(n,2e3)),s.clearCart(),r.success("Order placed successfully! You will pay on delivery."),setTimeout(()=>{window.location.href="/"},2e3),N.value=!1}catch(n){console.error("Cash on delivery error:",n),r.error("Failed to process cash on delivery order"),N.value=!1}},z=()=>{const n=m.value.findIndex(t=>t.id===g.value);m.value[n].completed=!0,m.value[n].active=!1,n+1<m.value.length&&(m.value[n+1].active=!0,g.value=m.value[n+1].id)},y=()=>{const n=m.value.findIndex(t=>t.id===g.value);n>0&&(m.value[n].active=!1,m.value[n-1].active=!0,m.value[n-1].completed=!1,g.value=m.value[n-1].id)},P=()=>C.value.find(n=>n.id===d.value.method),H=()=>{j.value="",v.value=""},K=n=>{const t=n.replace(/\s/g,"");return/^4/.test(t)?"Visa":/^5[1-5]/.test(t)||/^2[2-7]/.test(t)?"Mastercard":/^3[47]/.test(t)?"American Express":/^6/.test(t)?"Discover":""},J=n=>{switch(n){case"American Express":return{minLength:15,maxLength:15,formatted:18};case"Visa":case"Mastercard":case"Discover":return{minLength:16,maxLength:16,formatted:19};default:return{minLength:13,maxLength:19,formatted:23}}},ie=()=>J(D.value).formatted,fe=n=>{let t=n.target.value.replace(/\s/g,"");const U=K(t);D.value=U;const S=J(U);t.length>S.maxLength&&(t=t.substring(0,S.maxLength));let X="";if(U==="American Express")for(let re=0;re<t.length;re++)(re===4||re===10)&&(X+=" "),X+=t[re];else for(let re=0;re<t.length;re++)re>0&&re%4===0&&(X+=" "),X+=t[re];d.value.cardNumber=X,Se(t)},Se=n=>{const t=J(D.value);if(p.value.cardNumber="",M.value="",_.value=!1,n.length!==0){if(n.length<t.minLength){n.length>=4&&(p.value.cardNumber=`Card number must be ${t.minLength} digits`);return}if(D.value&&n.length!==t.maxLength){p.value.cardNumber=`${D.value} cards must be ${t.maxLength} digits`;return}if(!D.value&&n.length>16){p.value.cardNumber="Card number is too long";return}n.length>=t.minLength&&(ee(n)?(M.value=`Valid ${D.value||"card"} number`,_.value=!0,p.value.cardNumber=""):(p.value.cardNumber="Invalid card number",_.value=!1))}},je=n=>{const t=n.target.value.replace(/\s/g,""),U=J(D.value);if(!([8,9,27,13,46,37,38,39,40].indexOf(n.keyCode)!==-1||n.keyCode===65&&n.ctrlKey===!0||n.keyCode===67&&n.ctrlKey===!0||n.keyCode===86&&n.ctrlKey===!0||n.keyCode===88&&n.ctrlKey===!0||n.keyCode===90&&n.ctrlKey===!0)){if(t.length>=U.maxLength){n.preventDefault();return}(n.shiftKey||n.keyCode<48||n.keyCode>57)&&(n.keyCode<96||n.keyCode>105)&&n.preventDefault()}},Ce=n=>{let t=n.target.value.replace(/\D/g,"");if(t.length>=2&&(t=t.substring(0,2)+"/"+t.substring(2,4)),d.value.expiryDate=t,t.length===5){const[U,S]=t.split("/"),X=new Date,re=X.getFullYear()%100,ne=X.getMonth()+1,se=parseInt(U),be=parseInt(S);se<1||se>12?p.value.expiryDate="Invalid month":be<re||be===re&&se<ne?p.value.expiryDate="Card has expired":p.value.expiryDate=""}},Te=n=>{[8,9,27,13,46].indexOf(n.keyCode)!==-1||n.keyCode===65&&n.ctrlKey===!0||n.keyCode===67&&n.ctrlKey===!0||n.keyCode===86&&n.ctrlKey===!0||n.keyCode===88&&n.ctrlKey===!0||(n.shiftKey||n.keyCode<48||n.keyCode>57)&&(n.keyCode<96||n.keyCode>105)&&n.preventDefault()},b=n=>{let t=n.target.value.replace(/\D/g,"");const U=D.value==="American Express"?4:3;t=t.substring(0,U),d.value.cvv=t,t.length>=3&&(p.value.cvv="")},f=n=>{[8,9,27,13,46].indexOf(n.keyCode)!==-1||n.keyCode===65&&n.ctrlKey===!0||n.keyCode===67&&n.ctrlKey===!0||n.keyCode===86&&n.ctrlKey===!0||n.keyCode===88&&n.ctrlKey===!0||(n.shiftKey||n.keyCode<48||n.keyCode>57)&&(n.keyCode<96||n.keyCode>105)&&n.preventDefault()},Q=n=>{let t=n.target.value.replace(/[^a-zA-Z\s\-']/g,"");t=t.replace(/\b\w/g,U=>U.toUpperCase()),d.value.cardName=t,t.trim().length>0&&(p.value.cardName="")};return $e(()=>{const n=window.cartItems||[];if(s.initializeCart(n),s.isEmpty&&g.value==="customer"&&(r.warning("Your cart is empty. Redirecting to homepage..."),setTimeout(()=>{window.location.href="/"},2e3)),d.value.cardNumber){const t=d.value.cardNumber.replace(/\s/g,"");Se(t)}}),(n,t)=>(a(),l("div",Nd,[e("div",zd,[t[20]||(t[20]=Be('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-a7a2dac4></div><div class="absolute inset-0" data-v-a7a2dac4><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md" data-v-a7a2dac4></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg" data-v-a7a2dac4></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-a7a2dac4></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-a7a2dac4></div></div>',2)),A(i(we),{class:"relative z-10"},{default:O(()=>[e("div",Dd,[e("div",Ld,[t[19]||(t[19]=e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500/30 to-emerald-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Secure Checkout "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-blue-200 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/cart",class:"hover:text-blue-200 transition-colors duration-200"},"Cart")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Checkout")])])])],-1)),e("div",Pd,[A(i(te),{variant:"outline",size:"sm",onClick:V,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:O(()=>t[18]||(t[18]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Back to Cart ")])),_:1,__:[18]})])])])]),_:1})]),A(i(we),{class:"py-12"},{default:O(()=>{var U;return[c.value?(a(),l("div",Ed,[e("div",Vd,[A(i(ze),{size:"lg"}),t[21]||(t[21]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading checkout...",-1))])])):(a(),l("div",Rd,[e("div",Fd,[e("div",qd,[t[23]||(t[23]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900"},"Checkout Progress")],-1)),e("div",Hd,[e("div",Od,[(a(!0),l(ue,null,ce(m.value,(S,X)=>(a(),l("div",{key:S.id,class:G(["flex items-center",{"flex-1":X<m.value.length-1}])},[e("div",Wd,[e("div",{class:G(["w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300",k(S,X)])},[S.completed?(a(),l("svg",Ud,t[22]||(t[22]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):(a(),l("span",Yd,x(X+1),1))],2),e("div",Kd,[e("p",{class:G(["text-sm font-medium",S.active||S.completed?"text-gray-900":"text-gray-500"])},x(S.title),3),e("p",{class:G(["text-xs",S.active||S.completed?"text-gray-600":"text-gray-400"])},x(S.description),3)])]),X<m.value.length-1?(a(),l("div",{key:0,class:G(["flex-1 h-0.5 mx-4 transition-all duration-300",m.value[X+1].completed||m.value[X+1].active?"bg-green-500":"bg-gray-200"])},null,2)):L("",!0)],2))),128))])])]),e("div",Qd,[g.value==="customer"?(a(),l("div",Xd,[t[25]||(t[25]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Customer Information"),e("p",{class:"text-sm text-gray-600"},"Please provide your contact details")])],-1)),e("form",{onSubmit:xe(Z,["prevent"]),class:"space-y-6"},[e("div",Gd,[A(i(he),{modelValue:I.value.firstName,"onUpdate:modelValue":t[0]||(t[0]=S=>I.value.firstName=S),label:"First Name",placeholder:"Enter your first name",required:"",error:F.value.firstName},null,8,["modelValue","error"]),A(i(he),{modelValue:I.value.lastName,"onUpdate:modelValue":t[1]||(t[1]=S=>I.value.lastName=S),label:"Last Name",placeholder:"Enter your last name",required:"",error:F.value.lastName},null,8,["modelValue","error"])]),A(i(he),{modelValue:I.value.email,"onUpdate:modelValue":t[2]||(t[2]=S=>I.value.email=S),type:"email",label:"Email Address",placeholder:"Enter your email address",required:"",error:F.value.email},null,8,["modelValue","error"]),A(i(he),{modelValue:I.value.phone,"onUpdate:modelValue":t[3]||(t[3]=S=>I.value.phone=S),type:"tel",label:"Phone Number",placeholder:"Enter your phone number",required:"",error:F.value.phone},null,8,["modelValue","error"]),e("div",Zd,[A(i(te),{type:"submit",loading:h.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:O(()=>t[24]||(t[24]=[W(" Continue to Billing ")])),_:1,__:[24]},8,["loading"])])],32)])):L("",!0),g.value==="billing"?(a(),l("div",Jd,[t[28]||(t[28]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Billing Address"),e("p",{class:"text-sm text-gray-600"},"Where should we send your invoice?")])],-1)),e("form",{onSubmit:xe(oe,["prevent"]),class:"space-y-6"},[A(i(he),{modelValue:$.value.address,"onUpdate:modelValue":t[4]||(t[4]=S=>$.value.address=S),label:"Street Address",placeholder:"Enter your street address",required:"",error:u.value.address},null,8,["modelValue","error"]),e("div",eu,[A(i(he),{modelValue:$.value.city,"onUpdate:modelValue":t[5]||(t[5]=S=>$.value.city=S),label:"City",placeholder:"Enter your city",required:"",error:u.value.city},null,8,["modelValue","error"]),A(i(he),{modelValue:$.value.postalCode,"onUpdate:modelValue":t[6]||(t[6]=S=>$.value.postalCode=S),label:"Postal Code",placeholder:"Enter postal code",required:"",error:u.value.postalCode},null,8,["modelValue","error"])]),e("div",tu,[A(i(he),{modelValue:$.value.state,"onUpdate:modelValue":t[7]||(t[7]=S=>$.value.state=S),label:"State/Province",placeholder:"Enter state or province",required:"",error:u.value.state},null,8,["modelValue","error"]),A(i(he),{modelValue:$.value.country,"onUpdate:modelValue":t[8]||(t[8]=S=>$.value.country=S),label:"Country",placeholder:"Enter country",required:"",error:u.value.country},null,8,["modelValue","error"])]),e("div",su,[A(i(te),{type:"button",variant:"outline",onClick:y,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:O(()=>t[26]||(t[26]=[W(" Back to Customer Info ")])),_:1,__:[26]}),A(i(te),{type:"submit",loading:w.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:O(()=>t[27]||(t[27]=[W(" Continue to Payment ")])),_:1,__:[27]},8,["loading"])])],32)])):L("",!0),g.value==="payment"?(a(),l("div",ou,[t[51]||(t[51]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Payment Method"),e("p",{class:"text-sm text-gray-600"},"Choose how you'd like to pay")])],-1)),e("form",{onSubmit:xe(le,["prevent"]),class:"space-y-6"},[e("div",au,[e("div",ru,[(a(!0),l(ue,null,ce(C.value,S=>(a(),l("div",{key:S.id,onClick:X=>S.enabled&&(d.value.method=S.id),class:G(["border-2 rounded-lg p-4 transition-all duration-200",S.enabled?"cursor-pointer":"cursor-not-allowed opacity-60",d.value.method===S.id&&S.enabled?"border-blue-500 bg-blue-50":S.enabled?"border-gray-200 hover:border-gray-300":"border-gray-200 bg-gray-50"])},[e("div",lu,[e("div",{class:G(["w-4 h-4 rounded-full border-2 transition-all duration-200",d.value.method===S.id?"border-blue-500 bg-blue-500":"border-gray-300"])},[d.value.method===S.id?(a(),l("div",iu)):L("",!0)],2),e("div",du,[S.id==="dpopay"?(a(),l("svg",uu,t[29]||(t[29]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"},null,-1)]))):S.id==="cash"?(a(),l("svg",cu,t[30]||(t[30]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"},null,-1)]))):S.id==="bank"?(a(),l("svg",mu,t[31]||(t[31]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},null,-1)]))):S.id==="card"?(a(),l("svg",vu,t[32]||(t[32]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]))):L("",!0),e("span",{class:G(["font-medium",S.enabled?"text-gray-900":"text-gray-500"])},x(S.name),3),S.enabled?L("",!0):(a(),l("span",gu," Coming Soon "))])]),e("p",{class:G(["text-sm mt-2 ml-7",S.enabled?"text-gray-600":"text-gray-500"])},x(S.description),3)],10,nu))),128))])]),d.value.method==="dpopay"?(a(),l("div",pu,[t[40]||(t[40]=e("h4",{class:"font-medium text-gray-900"},"DPO Payment Details",-1)),e("div",hu,[t[36]||(t[36]=e("div",{class:"flex items-center space-x-2 mb-3"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})]),e("span",{class:"text-sm font-medium text-blue-900"},"Secure Payment via DPO")],-1)),t[37]||(t[37]=e("p",{class:"text-sm text-gray-600 mb-4"}," You will be redirected to DPO's secure payment gateway where you can pay using: ",-1)),t[38]||(t[38]=e("div",{class:"grid grid-cols-2 gap-3 text-xs mb-4"},[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-green-500 rounded-full"}),e("span",null,"Mobile Money (Airtel, TNM)")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-blue-500 rounded-full"}),e("span",null,"Credit/Debit Cards")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"}),e("span",null,"Bank Transfer")]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-2 h-2 bg-orange-500 rounded-full"}),e("span",null,"Digital Wallets")])],-1)),e("div",fu,[e("div",bu,[t[33]||(t[33]=e("span",{class:"text-sm text-gray-600"},"Total Amount:",-1)),e("span",{class:G(["text-lg font-bold",i(s).cartTotal>999999.99?"text-red-600":"text-gray-900"])},x(i(s).formatCurrency(i(s).cartTotal)),3)]),t[35]||(t[35]=e("div",{class:"flex justify-between items-center mt-1"},[e("span",{class:"text-xs text-gray-500"},"Currency:"),e("span",{class:"text-xs font-medium text-gray-700"},"MWK (Malawi Kwacha)")],-1)),i(s).cartTotal>999999.99?(a(),l("div",yu,t[34]||(t[34]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})]),e("span",{class:"text-xs text-red-800"},"Amount exceeds DPO Pay limit (MK 999,999.99)")],-1)]))):L("",!0)]),t[39]||(t[39]=e("div",{class:"mt-3 p-2 bg-green-50 rounded border border-green-200"},[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),e("span",{class:"text-xs text-green-800"},"SSL encrypted and PCI compliant")])],-1))])])):d.value.method==="card"?(a(),l("div",xu,[t[44]||(t[44]=e("h4",{class:"font-medium text-gray-900"},"Card Details",-1)),e("div",wu,[A(i(he),{modelValue:d.value.cardNumber,"onUpdate:modelValue":t[9]||(t[9]=S=>d.value.cardNumber=S),label:"Card Number",placeholder:"1234 5678 9012 3456",required:"",error:p.value.cardNumber,success:M.value,onInput:fe,onKeydown:je,maxlength:ie()},null,8,["modelValue","error","success","maxlength"]),e("div",_u,[A(i(he),{modelValue:d.value.expiryDate,"onUpdate:modelValue":t[10]||(t[10]=S=>d.value.expiryDate=S),label:"Expiry Date",placeholder:"MM/YY",required:"",error:p.value.expiryDate,onInput:Ce,onKeydown:Te,maxlength:"5"},null,8,["modelValue","error"]),A(i(he),{modelValue:d.value.cvv,"onUpdate:modelValue":t[11]||(t[11]=S=>d.value.cvv=S),label:"CVV",placeholder:"123",required:"",error:p.value.cvv,onInput:b,onKeydown:f,maxlength:"4"},null,8,["modelValue","error"])]),A(i(he),{modelValue:d.value.cardName,"onUpdate:modelValue":t[12]||(t[12]=S=>d.value.cardName=S),label:"Name on Card",placeholder:"Enter name as shown on card",required:"",error:p.value.cardName,onInput:Q},null,8,["modelValue","error"])]),D.value||d.value.cardNumber?(a(),l("div",ku,[D.value?(a(),l("div",Cu,[(a(),l("svg",{class:G(["w-5 h-5",_.value?"text-green-500":"text-blue-500"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},t[41]||(t[41]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)]),2)),e("span",{class:G(_.value?"text-green-600":"text-blue-600")},x(D.value)+" detected ",3),_.value?(a(),l("svg",$u,t[42]||(t[42]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):L("",!0)])):L("",!0),d.value.cardNumber?(a(),l("div",Mu,[e("span",null,x(d.value.cardNumber.replace(/\s/g,"").length),1),t[43]||(t[43]=e("span",null,"/",-1)),e("span",null,x(J(D.value).maxLength)+" digits",1),e("div",ju,[e("div",{class:G(["h-1 rounded-full transition-all duration-300",_.value?"bg-green-500":p.value.cardNumber?"bg-red-500":"bg-blue-500"]),style:He({width:`${Math.min(d.value.cardNumber.replace(/\s/g,"").length/J(D.value).maxLength*100,100)}%`})},null,6)])])):L("",!0)])):L("",!0)])):L("",!0),d.value.method==="dpopay"?(a(),l("div",Iu,[t[47]||(t[47]=e("div",{class:"flex items-center space-x-3 mb-4"},[e("div",{class:"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("div",null,[e("h4",{class:"font-medium text-gray-900"},"DPO Pay - Secure Payment Gateway"),e("p",{class:"text-sm text-gray-600"},"You'll be redirected to DPO Pay to complete your payment")])],-1)),e("div",Su,[e("div",Au,[A(i(he),{modelValue:d.value.customerName,"onUpdate:modelValue":t[13]||(t[13]=S=>d.value.customerName=S),label:"Full Name",placeholder:"Enter your full name",required:"",error:p.value.customerName},null,8,["modelValue","error"]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Currency",-1)),pe(e("select",{"onUpdate:modelValue":t[14]||(t[14]=S=>d.value.currency=S),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:""},t[45]||(t[45]=[e("option",{value:"MWK"},"MWK - Malawi Kwacha",-1),e("option",{value:"USD"},"USD - US Dollar",-1),e("option",{value:"EUR"},"EUR - Euro",-1),e("option",{value:"GBP"},"GBP - British Pound",-1),e("option",{value:"ZAR"},"ZAR - South African Rand",-1)]),512),[[It,d.value.currency]])])]),A(i(he),{modelValue:d.value.customerEmail,"onUpdate:modelValue":t[15]||(t[15]=S=>d.value.customerEmail=S),label:"Email Address",type:"email",placeholder:"Enter your email address",required:"",error:p.value.customerEmail},null,8,["modelValue","error"]),A(i(he),{modelValue:d.value.customerPhone,"onUpdate:modelValue":t[16]||(t[16]=S=>d.value.customerPhone=S),label:"Phone Number",placeholder:"+265 123 456 789",error:p.value.customerPhone},null,8,["modelValue","error"])]),t[48]||(t[48]=e("div",{class:"bg-white rounded-lg p-4 border border-gray-200"},[e("h5",{class:"font-medium text-gray-900 mb-3"},"Supported Payment Methods"),e("div",{class:"grid grid-cols-3 gap-4 text-center"},[e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})])]),e("span",{class:"text-xs text-gray-600"},"Credit Cards")]),e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])]),e("span",{class:"text-xs text-gray-600"},"Mobile Money")]),e("div",{class:"flex flex-col items-center space-y-2"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])]),e("span",{class:"text-xs text-gray-600"},"Bank Transfer")])])],-1))])):L("",!0),e("div",Bu,[A(i(te),{type:"button",variant:"outline",onClick:y,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:O(()=>t[49]||(t[49]=[W(" Back to Billing ")])),_:1,__:[49]}),A(i(te),{type:"submit",loading:B.value,class:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"},{default:O(()=>t[50]||(t[50]=[W(" Review Order ")])),_:1,__:[50]},8,["loading"])])],32)])):L("",!0),g.value==="review"?(a(),l("div",Tu,[t[68]||(t[68]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-bold text-gray-900"},"Review Your Order"),e("p",{class:"text-sm text-gray-600"},"Please review all details before placing your order")])],-1)),e("div",Nu,[e("div",zu,[t[55]||(t[55]=e("h4",{class:"font-medium text-gray-900 mb-4"},"Order Summary",-1)),e("div",Du,[e("div",Lu,[e("span",Pu,"Items ("+x(i(s).cartCount)+")",1),e("span",Eu,x(i(s).formatCurrency(i(s).cartTotal)),1)]),t[54]||(t[54]=e("div",{class:"flex justify-between"},[e("span",{class:"text-gray-600"},"Shipping"),e("span",{class:"font-medium"},"Free")],-1)),e("div",Vu,[t[52]||(t[52]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",Ru,x(i(s).formatCurrency(0)),1)]),e("div",Fu,[t[53]||(t[53]=e("span",{class:"text-lg font-bold text-gray-900"},"Total",-1)),e("span",qu,x(i(s).formatCurrency(i(s).cartTotal)),1)])])]),e("div",Hu,[e("div",Ou,[t[59]||(t[59]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Customer Information",-1)),e("div",Wu,[e("p",null,[t[56]||(t[56]=e("span",{class:"text-gray-600"},"Name:",-1)),W(" "+x(I.value.firstName)+" "+x(I.value.lastName),1)]),e("p",null,[t[57]||(t[57]=e("span",{class:"text-gray-600"},"Email:",-1)),W(" "+x(I.value.email),1)]),e("p",null,[t[58]||(t[58]=e("span",{class:"text-gray-600"},"Phone:",-1)),W(" "+x(I.value.phone),1)])])]),e("div",Uu,[t[60]||(t[60]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Billing Address",-1)),e("div",Yu,[e("p",null,x($.value.address),1),e("p",null,x($.value.city)+", "+x($.value.state)+" "+x($.value.postalCode),1),e("p",null,x($.value.country),1)])])]),e("div",Ku,[t[61]||(t[61]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Payment Method",-1)),e("div",Qu,[e("p",null,x((U=P())==null?void 0:U.name),1),d.value.method==="dpopay"?(a(),l("p",Xu," Secure payment via DPO Gateway ")):d.value.method==="card"?(a(),l("p",Gu," **** **** **** "+x(d.value.cardNumber.slice(-4)),1)):d.value.method==="cash"?(a(),l("p",Zu," Payment on delivery ")):d.value.method==="bank"?(a(),l("p",Ju," Direct bank transfer ")):L("",!0)])]),N.value?(a(),l("div",ec,[e("div",tc,[t[63]||(t[63]=e("div",{class:"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"},null,-1)),e("div",sc,[t[62]||(t[62]=e("h4",{class:"text-sm font-medium text-blue-800 mb-1"},"Processing Your Order",-1)),e("p",oc,x(v.value||"Please wait while we process your order..."),1)])])])):j.value?(a(),l("div",ac,[e("div",rc,[t[65]||(t[65]=e("svg",{class:"w-5 h-5 text-red-600 mt-0.5 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)),e("div",nc,[t[64]||(t[64]=e("h4",{class:"text-sm font-medium text-red-800 mb-1"},"Order Processing Error",-1)),e("p",lc,x(j.value),1),e("button",{onClick:H,class:"mt-2 text-xs text-red-600 hover:text-red-800 underline"}," Dismiss ")])])])):L("",!0),e("div",ic,[pe(e("input",{id:"terms","onUpdate:modelValue":t[17]||(t[17]=S=>E.value=S),type:"checkbox",class:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[Ee,E.value]]),t[66]||(t[66]=e("label",{for:"terms",class:"text-sm text-gray-700"},[W(" I agree to the "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500"},"Terms and Conditions"),W(" and "),e("a",{href:"#",class:"text-blue-600 hover:text-blue-500"},"Privacy Policy")],-1))]),e("div",dc,[A(i(te),{type:"button",variant:"outline",onClick:y,class:"border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-medium px-6 py-2 rounded-lg transition-all duration-200"},{default:O(()=>t[67]||(t[67]=[W(" Back to Payment ")])),_:1,__:[67]}),A(i(te),{onClick:R,loading:N.value,disabled:!E.value||N.value,class:G(["font-bold px-8 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200",j.value?"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white":"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"])},{default:O(()=>[N.value?(a(),l("span",uc,"Processing Order...")):j.value?(a(),l("span",cc,"Try Again")):(a(),l("span",mc,"Place Order"))]),_:1},8,["loading","disabled","class"])])])])):L("",!0)])]),e("div",vc,[e("div",gc,[e("div",pc,[e("div",hc,[e("div",fc,[t[70]||(t[70]=e("div",{class:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",null,[t[69]||(t[69]=e("h3",{class:"text-lg font-bold"},"Order Summary",-1)),e("p",bc,x(i(s).cartCount)+" "+x(i(s).cartCount===1?"item":"items")+" in cart",1)])])]),e("div",yc,[e("div",xc,[(a(!0),l(ue,null,ce(i(s).items,S=>(a(),l("div",{key:S.id,class:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"},[t[71]||(t[71]=e("div",{class:"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",wc,[e("p",_c,x(S.name),1),e("p",kc,"Qty: "+x(S.quantity),1)]),e("div",Cc,x(i(s).formatCurrency(i(s).getItemSubtotal(S))),1)]))),128))]),e("div",$c,[e("div",Mc,[t[72]||(t[72]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",jc,x(i(s).formatCurrency(i(s).cartTotal)),1)]),t[75]||(t[75]=e("div",{class:"flex justify-between text-sm"},[e("span",{class:"text-gray-600"},"Shipping"),e("span",{class:"font-medium text-green-600"},"Free")],-1)),e("div",Ic,[t[73]||(t[73]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",Sc,x(i(s).formatCurrency(0)),1)]),e("div",Ac,[t[74]||(t[74]=e("span",{class:"text-gray-900"},"Total",-1)),e("span",Bc,x(i(s).formatCurrency(i(s).cartTotal)),1)])])])]),t[76]||(t[76]=e("div",{class:"bg-white rounded-xl shadow-lg border border-gray-100 p-6"},[e("h4",{class:"font-bold text-gray-900 mb-4 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),W(" Secure Checkout ")]),e("div",{class:"space-y-3"},[e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" 256-bit SSL encryption ")]),e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Secure payment processing ")]),e("div",{class:"flex items-center text-sm text-gray-600"},[e("svg",{class:"w-4 h-4 mr-3 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Money-back guarantee ")])])],-1))])])]))]}),_:1})]))}});const rt=ke(Tc,[["__scopeId","data-v-a7a2dac4"]]),Nc={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},zc={class:"relative bg-gradient-to-r from-green-800 via-green-700 to-emerald-600 overflow-hidden border-b border-green-600/30"},Dc={key:0,class:"flex justify-center py-20"},Lc={class:"text-center"},Pc={key:1,class:"max-w-4xl mx-auto"},Ec={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-8"},Vc={class:"px-6 py-8 text-center"},Rc={class:"inline-flex items-center space-x-2 bg-gray-50 rounded-lg px-4 py-2"},Fc={class:"text-sm font-bold text-gray-900 font-mono"},qc={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},Hc={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},Oc={class:"p-6 space-y-4"},Wc={class:"flex justify-between items-center py-2 border-b border-gray-100"},Uc={class:"text-sm font-mono text-gray-900"},Yc={class:"flex justify-between items-center py-2 border-b border-gray-100"},Kc={class:"text-lg font-bold text-gray-900"},Qc={class:"flex justify-between items-center py-2 border-b border-gray-100"},Xc={class:"text-sm text-gray-900"},Gc={key:0,class:"flex justify-between items-center py-2"},Zc={class:"text-sm font-mono text-gray-900"},Jc={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},e0={class:"p-6"},t0={class:"flex flex-col sm:flex-row gap-4 justify-center"},s0=me({__name:"PaymentSuccess",setup(o){const s=Ae(),r=Me(),c=T(!0),h=T(""),w=T(""),B=T(0);T("MWK");const N=T(new Date),j=T(""),v=m=>!m||m<=0?"MK 0":`MK ${m.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1,")}`,g=m=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(m),E=()=>{window.location.href="/"},D=()=>{window.print()},M=async()=>{var m;try{(await fetch("/api/cart/clear",{method:"POST",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((m=document.querySelector('meta[name="csrf-token"]'))==null?void 0:m.getAttribute("content"))||""}})).ok&&console.log("Backend cart cleared successfully"),s.reset(),localStorage.removeItem("vertigo_cart"),localStorage.removeItem("cart_items"),localStorage.removeItem("vertigo_order_ref"),localStorage.removeItem("vertigo_transaction_token"),window.dispatchEvent(new CustomEvent("cart-cleared",{detail:{reason:"payment_success"}})),console.log("Cart cleared successfully after payment")}catch(I){console.error("Error clearing cart after payment:",I)}},_=()=>{try{const m=new URLSearchParams(window.location.search);h.value=m.get("CompanyRef")||localStorage.getItem("vertigo_order_ref")||"N/A",w.value=m.get("TransID")||"N/A",j.value=m.get("CCDapproval")||"";const I=localStorage.getItem("vertigo_payment_amount");B.value=I?parseFloat(I):0,N.value=new Date}catch(m){console.error("Error loading payment data:",m)}};return $e(async()=>{_(),await M(),r.success("Payment completed successfully!"),setTimeout(()=>{c.value=!1},1e3)}),(m,I)=>(a(),l("div",Nc,[e("div",zc,[I[1]||(I[1]=Be('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5" data-v-828e1a7e></div><div class="absolute inset-0" data-v-828e1a7e><div class="absolute top-2 left-4 w-12 h-12 bg-green-400/10 rounded-full blur-md" data-v-828e1a7e></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-emerald-400/15 rounded-full blur-lg" data-v-828e1a7e></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl" data-v-828e1a7e></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;" data-v-828e1a7e></div></div>',2)),A(i(we),{class:"relative z-10"},{default:O(()=>I[0]||(I[0]=[e("div",{class:"py-6"},[e("div",{class:"flex items-center justify-between"},[e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500/30 to-emerald-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-green-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Payment Successful "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-green-200"},[e("li",null,[e("a",{href:"/",class:"hover:text-green-100 transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/cart",class:"hover:text-green-100 transition-colors duration-200"},"Cart")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",null,[e("a",{href:"/checkout",class:"hover:text-green-100 transition-colors duration-200"},"Checkout")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-green-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Success")])])])]),e("div",{class:"hidden md:flex items-center space-x-3"},[e("div",{class:"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20"},[e("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e("span",{class:"text-sm font-medium text-green-100"},"Payment Confirmed")])])])],-1)])),_:1,__:[0]})]),A(i(we),{class:"py-12"},{default:O(()=>[c.value?(a(),l("div",Dc,[e("div",Lc,[A(i(ze),{size:"lg"}),I[2]||(I[2]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading payment details...",-1))])])):(a(),l("div",Pc,[e("div",Ec,[e("div",Vc,[I[4]||(I[4]=e("div",{class:"flex justify-center mb-6"},[e("div",{class:"w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg success-checkmark"},[e("svg",{class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),I[5]||(I[5]=e("h2",{class:"text-3xl font-bold text-gray-900 mb-3"},"Payment Successful!",-1)),I[6]||(I[6]=e("p",{class:"text-lg text-gray-600 mb-6"}," Your payment has been processed successfully and your order is confirmed. ",-1)),e("div",Rc,[I[3]||(I[3]=e("span",{class:"text-sm font-medium text-gray-600"},"Order Reference:",-1)),e("span",Fc,x(h.value),1)])])]),e("div",qc,[e("div",Hc,[I[12]||(I[12]=e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),W(" Payment Information ")])],-1)),e("div",Oc,[e("div",Wc,[I[7]||(I[7]=e("span",{class:"text-sm font-medium text-gray-600"},"Transaction ID:",-1)),e("span",Uc,x(w.value),1)]),e("div",Yc,[I[8]||(I[8]=e("span",{class:"text-sm font-medium text-gray-600"},"Amount:",-1)),e("span",Kc,x(v(B.value)),1)]),I[11]||(I[11]=e("div",{class:"flex justify-between items-center py-2 border-b border-gray-100"},[e("span",{class:"text-sm font-medium text-gray-600"},"Payment Method:"),e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"}," DPO Pay ")],-1)),e("div",Qc,[I[9]||(I[9]=e("span",{class:"text-sm font-medium text-gray-600"},"Payment Date:",-1)),e("span",Xc,x(g(N.value)),1)]),j.value?(a(),l("div",Gc,[I[10]||(I[10]=e("span",{class:"text-sm font-medium text-gray-600"},"Approval Code:",-1)),e("span",Zc,x(j.value),1)])):L("",!0)])]),I[13]||(I[13]=e("div",{class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},[e("div",{class:"px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200"},[e("h3",{class:"text-lg font-bold text-gray-900 flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),W(" Order Status ")])]),e("div",{class:"p-6"},[e("div",{class:"text-center"},[e("div",{class:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4"},[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),W(" Order Confirmed ")]),e("p",{class:"text-gray-600 mb-6"}," Your order has been confirmed and is being processed. You will receive an email confirmation shortly. "),e("div",{class:"bg-blue-50 rounded-lg p-4"},[e("h4",{class:"text-sm font-semibold text-blue-900 mb-2"},"What's Next?"),e("ul",{class:"text-sm text-blue-800 space-y-1"},[e("li",null,"• Email confirmation sent"),e("li",null,"• Items marked as sold"),e("li",null,"• Cart automatically cleared")])])])])],-1))]),e("div",Jc,[e("div",e0,[e("div",t0,[A(i(te),{variant:"primary",size:"lg",onClick:E,class:"flex items-center justify-center"},{default:O(()=>I[14]||(I[14]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Continue Shopping ")])),_:1,__:[14]}),A(i(te),{variant:"outline",size:"lg",onClick:D,class:"flex items-center justify-center"},{default:O(()=>I[15]||(I[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})],-1),W(" Print Receipt ")])),_:1,__:[15]})])])])]))]),_:1})]))}});const nt=ke(s0,[["__scopeId","data-v-828e1a7e"]]),o0={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},a0={key:0,class:"flex justify-center items-center min-h-screen"},r0={class:"text-center"},n0={key:1,class:"flex justify-center items-center min-h-screen"},l0={class:"text-center"},i0={class:"text-gray-600 mb-4"},d0={key:2,class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},u0={class:"py-6"},c0={class:"flex items-center justify-between"},m0={class:"flex items-center space-x-4"},v0={class:"mt-1"},g0={class:"flex items-center space-x-2 text-sm text-slate-300"},p0={class:"text-gray-100 font-medium truncate max-w-xs"},h0={class:"flex items-center space-x-3"},f0={key:0,class:"max-w-7xl mx-auto"},b0={class:"grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8"},y0={class:"xl:col-span-2 space-y-6"},x0={class:"relative bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden group"},w0={class:"aspect-[4/3] bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center relative"},_0=["src","alt"],k0={key:1,class:"text-gray-400 text-center"},C0={class:"absolute top-4 left-4 right-4 flex justify-between items-start"},$0={class:"bg-white/90 backdrop-blur-sm rounded-xl px-3 py-2 shadow-lg"},M0={class:"text-xs font-bold"},j0={class:"bg-white/90 backdrop-blur-sm rounded-xl px-3 py-2 shadow-lg"},I0={class:"text-xs font-medium text-gray-600"},S0={key:0,class:"grid grid-cols-5 gap-3"},A0=["onClick"],B0=["src","alt"],T0={class:"xl:col-span-1"},N0={class:"sticky top-8 space-y-6"},z0={class:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"},D0={class:"p-8"},L0={class:"text-3xl font-bold text-gray-900 mb-6 leading-tight"},P0={class:"mb-8"},E0={class:"text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100"},V0={class:"text-sm font-medium text-gray-600 mb-2"},R0={class:"text-4xl font-bold text-gray-900 mb-2"},F0={key:0,class:"text-sm text-gray-500"},q0={class:"space-y-4 mb-8"},H0={key:0,class:"flex items-center justify-between py-3 border-b border-gray-100"},O0={class:"font-mono text-gray-900 font-semibold"},W0={key:1,class:"flex items-center justify-between py-3 border-b border-gray-100"},U0={class:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold"},Y0={key:2,class:"flex items-center justify-between py-3"},K0={class:"text-sm font-bold"},Q0={class:"space-y-4"},X0={key:0},G0={key:0,class:"bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-4"},Z0={class:"flex items-start space-x-3"},J0={class:"text-sm text-yellow-700 mt-1"},e1={class:"mt-3"},t1={class:"relative"},s1={key:0,class:"mt-2 text-sm text-red-600 bg-red-50 p-3 rounded-xl"},o1={key:1,class:"mt-2 text-sm text-green-600 bg-green-50 p-3 rounded-xl"},a1={class:"mt-2 text-xs text-gray-500 bg-gray-50 p-3 rounded-xl"},r1={key:1},n1={class:"space-y-4"},l1={key:1,class:"space-y-3"},i1={class:"grid grid-cols-2 gap-3"},d1={class:"pt-6 border-t border-gray-200"},u1={key:0,class:"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden"},c1={class:"p-8"},m1={class:"prose prose-lg max-w-none"},v1={class:"text-gray-700 leading-relaxed text-lg"},g1={class:"max-w-6xl max-h-full p-6 relative"},p1={class:"absolute top-2 left-6 right-6 flex items-center justify-between z-10"},h1={class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-4 py-2"},f1={class:"text-white font-medium text-sm"},b1={class:"bg-white rounded-xl shadow-2xl overflow-hidden"},y1=["src","alt"],lt=me({__name:"ItemDetail",setup(o){const s=Ae(),r=Ue(),c=Me(),{isAuthenticated:h,user:w,initialize:B}=Vt(),N=At(),j=Ne(),v=T(null),g=T(!0),E=T(null),D=T(0),M=T(!1),_=T(null),m=T(!1),I=T(!1),F=T(!1);T(!1);const $=T(""),u=T(!1),d=T(null),p=T(!1),C=q(()=>{var f,Q,n;if(!((f=v.value)!=null&&f.media)||v.value.media.length===0)return((Q=v.value)==null?void 0:Q.image)||((n=v.value)==null?void 0:n.cropped)||null;const b=v.value.media[D.value];return(b==null?void 0:b.original_url)||(b==null?void 0:b.url)||null}),k=q(()=>v.value?s.isInCart(v.value.id):!1);q(()=>v.value?r.isInWatchlist(v.value.id):!1);const V=q(()=>{var b,f;return v.value?((b=v.value.auction_type)==null?void 0:b.type)!=="live"?!0:((f=d.value)==null?void 0:f.subscribed)===!0:!1}),Z=q(()=>{var b,f,Q;return!v.value||((b=v.value.auction_type)==null?void 0:b.type)!=="live"?null:p.value?"Checking subscription status...":((f=d.value)==null?void 0:f.subscribed)===!1?`You need to subscribe to "${d.value.auction_type||((Q=v.value.auction_type)==null?void 0:Q.name)||"this live auction"}" to place bids on this live auction item.`:null}),oe=async()=>{var b,f,Q,n;try{g.value=!0,E.value=null;const t=N.params.id;if(!t)throw new Error("Invalid item ID");const U=await fetch(`/ajax-item/${t}`);if(!U.ok)throw U.status===404?new Error("Item not found"):U.status>=500?new Error("Server error. Please try again later."):new Error("Failed to load item");const S=await U.json();if(!S||!S.id)throw new Error("Invalid item data received");if(v.value=S,((b=S.auction_type)==null?void 0:b.type)!=="cash"){const X=S.bid_amount||0,re=S.target_amount||0,ne=Math.max(X,re)+1;_.value=ne.toString();const se=H(ne.toString());setTimeout(()=>{const be=document.getElementById("bidAmount");be&&(be.value=se)},100)}((f=S.auction_type)==null?void 0:f.type)==="live"&&h.value&&S.auction_type_id&&(console.log("Item loaded, checking subscription for live auction:",{auction_type_id:S.auction_type_id,auction_type_name:(Q=S.auction_type)==null?void 0:Q.name,auction_type_type:(n=S.auction_type)==null?void 0:n.type}),await le(S.auction_type_id))}catch(t){console.error("Error loading item:",t),E.value=t instanceof Error?t.message:"Failed to load item"}finally{g.value=!1}},le=async b=>{var f;if(!h.value){d.value={subscribed:!1,message:"User not authenticated"};return}try{p.value=!0,console.log("Checking subscription for auction type ID:",b);const Q=await fetch(`/api/check-subscription/${b}`,{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((f=document.querySelector('meta[name="csrf-token"]'))==null?void 0:f.getAttribute("content"))||""},credentials:"include"});if(Q.ok){const n=await Q.json();console.log("Subscription check response:",n),d.value=n}else console.error("Failed to check subscription status:",Q.status),d.value={subscribed:!1,message:"Failed to check subscription"}}catch(Q){console.error("Error checking subscription status:",Q),d.value={subscribed:!1,message:"Error checking subscription"}}finally{p.value=!1}},ee=b=>{D.value=b},R=()=>{M.value=!0},Y=()=>{M.value=!1},ae=b=>new Intl.NumberFormat("en-MW",{style:"currency",currency:"MWK",minimumFractionDigits:0,maximumFractionDigits:0}).format(b),z=b=>{var f;switch((f=b.auction_type)==null?void 0:f.type){case"cash":return b.target_amount||0;case"online":case"live":return b.bid_amount||b.target_amount||0;default:return b.target_amount||0}},y=b=>{if(!b.date_to)return null;const f=new Date(b.date_to),Q=new Date;if(f<Q)return"Ended";const n=f.getTime()-Q.getTime();return Math.floor(n/(1e3*60*60))<24?"Ending Soon":"Active"},P=b=>{const f=y(b),Q="px-2 py-1 rounded-full text-xs font-medium";switch(f){case"Ended":return`${Q} bg-red-100 text-red-800`;case"Ending Soon":return`${Q} bg-yellow-100 text-yellow-800`;case"Active":return`${Q} bg-green-100 text-green-800`;default:return`${Q} bg-gray-100 text-gray-800`}},H=b=>b.replace(/[^0-9]/g,"").replace(/\B(?=(\d{3})+(?!\d))/g,","),K=b=>{const f=b.target;if(f.value&&!f.value.includes(",")){const Q=H(f.value);f.value=Q}},J=b=>{var ne,se;const f=b.target,n=f.value.replace(/[^0-9]/g,""),t=H(n);if(f.value=t,_.value=n,!n){$.value="",u.value=!1;return}const U=parseInt(n),S=((ne=v.value)==null?void 0:ne.bid_amount)||0,X=((se=v.value)==null?void 0:se.target_amount)||0,re=Math.max(S,X)+1;if(isNaN(U)||U<=0){$.value="Please enter a valid amount",u.value=!1;return}if(U<re){$.value=`Minimum bid is ${ae(re)}`,u.value=!1;return}$.value="",u.value=!0},ie=async()=>{var b;if(!(!v.value||!_.value)){if(!h.value){c.error("You must be signed in to place a bid","Authentication Required");return}try{m.value=!0;const f=typeof _.value=="string"?_.value.replace(/,/g,""):_.value,Q=parseInt(f.toString())||0;if(isNaN(Q)||Q<=0){c.error("Please enter a valid bid amount","Invalid Bid");return}console.log("Placing bid:",{item_id:v.value.id,amount:Q});const n=await fetch("/ajax-bid",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((b=document.querySelector('meta[name="csrf-token"]'))==null?void 0:b.getAttribute("content"))||""},body:JSON.stringify({item_id:v.value.id,amount:Q})});if(console.log("Response status:",n.status),!n.ok){const U=await n.text();throw console.error("Bid error response:",U),new Error(`Failed to place bid: ${n.status} ${U}`)}const t=await n.json();console.log("Bid response data:",t),c.success(`Your bid of ${ae(Q)} has been placed successfully!`,"Bid Placed"),await oe()}catch(f){c.error(f instanceof Error?f.message:"Failed to place bid","Bid Failed")}finally{m.value=!1}}},fe=async()=>{if(v.value)try{I.value=!0,await s.addToCart(v.value)&&c.success(`${v.value.name} has been added to your cart.`,"Added to Cart")}catch(b){c.error(b instanceof Error?b.message:"Failed to add item to cart","Cart Error")}finally{I.value=!1}},Se=async()=>{if(v.value)try{F.value=!0,await s.removeFromCart(v.value)&&c.info(`${v.value.name} has been removed from your cart.`,"Removed from Cart")}catch(b){c.error(b instanceof Error?b.message:"Failed to remove item from cart","Cart Error")}finally{F.value=!1}},je=()=>{j.push("/checkout")},Ce=()=>{j.back()},Te=async b=>{var f,Q;c.success("You are now signed in and can place bids!","Welcome!"),((Q=(f=v.value)==null?void 0:f.auction_type)==null?void 0:Q.type)==="live"&&v.value.auction_type_id&&await le(v.value.auction_type_id),oe()};return $e(async()=>{await B();const b=window.cartItems||[];b.length>0?s.initializeCart(b):await s.fetchCart(),oe()}),(b,f)=>{var n,t;const Q=gt("router-link");return a(),l("div",o0,[g.value?(a(),l("div",a0,[e("div",r0,[A(i(ze),{size:"lg"}),f[2]||(f[2]=e("p",{class:"mt-6 text-base text-gray-600"},"Loading item details...",-1))])])):E.value?(a(),l("div",n0,[e("div",l0,[f[4]||(f[4]=e("div",{class:"text-red-500 mb-4"},[e("svg",{class:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),f[5]||(f[5]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Item Not Found",-1)),e("p",i0,x(E.value),1),A(i(te),{onClick:Ce,variant:"outline"},{default:O(()=>f[3]||(f[3]=[W(" Go Back ")])),_:1,__:[3]})])])):v.value?(a(),l("div",d0,[f[13]||(f[13]=Be('<div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5"></div><div class="absolute inset-0"><div class="absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md"></div><div class="absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg"></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div><div class="absolute inset-0 opacity-5" style="background-image:radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0);background-size:20px 20px;"></div></div>',2)),A(i(we),{class:"relative z-10"},{default:O(()=>[e("div",u0,[e("div",c0,[e("div",m0,[f[11]||(f[11]=e("div",{class:"flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500/30 to-indigo-600/30 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-blue-100",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[f[10]||(f[10]=e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Item Details ",-1)),e("nav",v0,[e("ol",g0,[e("li",null,[A(Q,{to:"/",class:"hover:text-blue-200 transition-colors duration-200"},{default:O(()=>f[6]||(f[6]=[W("Home")])),_:1,__:[6]})]),f[8]||(f[8]=e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1)),e("li",null,[A(Q,{to:"/auctions",class:"hover:text-blue-200 transition-colors duration-200"},{default:O(()=>f[7]||(f[7]=[W("Auctions")])),_:1,__:[7]})]),f[9]||(f[9]=e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])],-1)),e("li",p0,x(v.value.name),1)])])])]),e("div",h0,[A(i(te),{variant:"outline",size:"sm",onClick:Ce,class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:O(()=>f[12]||(f[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Back ")])),_:1,__:[12]})])])])]),_:1})])):L("",!0),A(i(we),{class:"py-8"},{default:O(()=>{var U,S,X,re;return[v.value?(a(),l("div",f0,[e("div",b0,[e("div",y0,[e("div",x0,[e("div",w0,[C.value?(a(),l("img",{key:0,src:C.value,alt:v.value.name,class:"w-full h-full object-cover cursor-pointer group-hover:scale-105 transition-transform duration-500",onClick:R},null,8,_0)):(a(),l("div",k0,f[14]||(f[14]=[e("svg",{class:"w-20 h-20 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg font-medium"},"No image available",-1)]))),e("div",C0,[e("div",$0,[y(v.value)?(a(),l("div",{key:0,class:G(P(v.value))},[e("span",M0,x(y(v.value)),1)],2)):L("",!0)]),e("div",j0,[e("span",I0,x(((U=v.value.auction_type)==null?void 0:U.name)||"Auction Item"),1)])]),f[15]||(f[15]=e("div",{class:"absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})])],-1))])]),v.value.media&&v.value.media.length>1?(a(),l("div",S0,[(a(!0),l(ue,null,ce(v.value.media,(ne,se)=>(a(),l("div",{key:se,class:G(["aspect-square bg-gray-100 rounded-xl overflow-hidden cursor-pointer border-2 transition-all duration-300 hover:shadow-lg",D.value===se?"border-blue-500 ring-2 ring-blue-200 shadow-lg":"border-gray-200 hover:border-gray-300"]),onClick:be=>ee(se)},[e("img",{src:ne.url||ne.original_url,alt:`${v.value.name} - Image ${se+1}`,class:"w-full h-full object-cover hover:scale-110 transition-transform duration-300"},null,8,B0)],10,A0))),128))])):L("",!0)]),e("div",T0,[e("div",N0,[e("div",z0,[e("div",D0,[e("h1",L0,x(v.value.name),1),e("div",P0,[e("div",E0,[e("p",V0,x(((S=v.value.auction_type)==null?void 0:S.type)==="cash"?"Price":"Current Bid"),1),e("div",R0,x(ae(z(v.value))),1),((X=v.value.auction_type)==null?void 0:X.type)!=="cash"?(a(),l("p",F0," Starting: "+x(ae(v.value.target_amount||0)),1)):L("",!0)])]),e("div",q0,[v.value.code||v.value.reference_number?(a(),l("div",H0,[f[16]||(f[16]=e("span",{class:"text-gray-600 font-medium"},"Reference",-1)),e("span",O0,x(v.value.code||v.value.reference_number),1)])):L("",!0),v.value.auction_type?(a(),l("div",W0,[f[17]||(f[17]=e("span",{class:"text-gray-600 font-medium"},"Category",-1)),e("span",U0,x(v.value.auction_type.name),1)])):L("",!0),y(v.value)?(a(),l("div",Y0,[f[18]||(f[18]=e("span",{class:"text-gray-600 font-medium"},"Status",-1)),e("div",{class:G(P(v.value))},[e("span",K0,x(y(v.value)),1)],2)])):L("",!0)]),e("div",Q0,[((re=v.value.auction_type)==null?void 0:re.type)!=="cash"?(a(),l("div",X0,[A(i(ht),{title:"Sign in to place a bid",message:"You need to be signed in to participate in bidding on this item.","fallback-style":"card",onAuthSuccess:Te},{default:O(()=>{var ne;return[((ne=v.value.auction_type)==null?void 0:ne.type)==="live"&&!V.value?(a(),l("div",G0,[e("div",Z0,[f[21]||(f[21]=e("svg",{class:"w-5 h-5 text-yellow-600 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",null,[f[20]||(f[20]=e("h4",{class:"text-sm font-medium text-yellow-800"},"Subscription Required",-1)),e("p",J0,x(Z.value),1),e("div",e1,[A(Q,{to:"/register-bid",class:"inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"},{default:O(()=>f[19]||(f[19]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Subscribe to Live Auction ")])),_:1,__:[19]})])])])])):L("",!0),V.value?(a(),l("form",{key:1,onSubmit:xe(ie,["prevent"]),class:"space-y-4"},[e("div",null,[f[24]||(f[24]=e("label",{for:"bidAmount",class:"block text-sm font-bold text-gray-700 mb-3"}," Place Your Bid ",-1)),e("div",t1,[f[22]||(f[22]=e("span",{class:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-bold"},"MWK",-1)),pe(e("input",{id:"bidAmount","onUpdate:modelValue":f[0]||(f[0]=se=>_.value=se),type:"text",inputmode:"numeric",pattern:"[0-9]*",class:"w-full pl-16 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg font-semibold transition-all duration-200",placeholder:"Enter your bid",required:"",onInput:J,onFocus:K},null,544),[[Ie,_.value]])]),$.value?(a(),l("div",s1,x($.value),1)):_.value&&u.value?(a(),l("div",o1," ✓ Valid bid amount: "+x(ae(parseInt(_.value.toString().replace(/,/g,"")))),1)):L("",!0),e("p",a1,[f[23]||(f[23]=e("svg",{class:"w-3 h-3 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),W(" Minimum bid: "+x(ae(Math.max(v.value.bid_amount||0,v.value.target_amount||0)+1)),1)])]),A(i(te),{type:"submit",loading:m.value,disabled:!_.value||!u.value,class:"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",size:"lg"},{default:O(()=>f[25]||(f[25]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})],-1),W(" Place Bid ")])),_:1,__:[25]},8,["loading","disabled"])],32)):L("",!0)]}),_:1})])):(a(),l("div",r1,[e("div",n1,[k.value?(a(),l("div",l1,[f[29]||(f[29]=e("div",{class:"p-4 bg-green-50 border-2 border-green-200 rounded-2xl"},[e("div",{class:"flex items-center justify-center text-green-800"},[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),e("span",{class:"font-bold"},"Item is in your cart")])],-1)),e("div",i1,[A(i(te),{onClick:Se,loading:F.value,variant:"outline",class:"border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 font-bold py-3 rounded-xl",size:"lg"},{default:O(()=>f[27]||(f[27]=[W(" Remove ")])),_:1,__:[27]},8,["loading"]),A(i(te),{onClick:je,class:"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-bold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300",size:"lg"},{default:O(()=>f[28]||(f[28]=[W(" Checkout ")])),_:1,__:[28]})])])):(a(),de(i(te),{key:0,onClick:fe,loading:I.value,class:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",size:"lg"},{default:O(()=>f[26]||(f[26]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8m-8 0a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4z"})],-1),W(" Add to Cart ")])),_:1,__:[26]},8,["loading"]))])]))]),e("div",d1,[A(i(te),{onClick:Ce,variant:"outline",class:"w-full border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 font-bold py-3 rounded-xl",size:"lg"},{default:O(()=>f[30]||(f[30]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})],-1),W(" Back to Listings ")])),_:1,__:[30]})])])])])])]),v.value.description?(a(),l("div",u1,[e("div",c1,[f[31]||(f[31]=e("div",{class:"flex items-center space-x-3 mb-6"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h2",{class:"text-2xl font-bold text-gray-900"},"Description"),e("p",{class:"text-gray-600"},"Detailed information about this item")])],-1)),e("div",m1,[e("p",v1,x(v.value.description),1)])])])):L("",!0)])):L("",!0)]}),_:1}),M.value?(a(),l("div",{key:3,class:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 backdrop-blur-sm",onClick:Y},[e("div",g1,[e("div",p1,[e("div",h1,[e("h3",f1,x((n=v.value)==null?void 0:n.name),1)]),e("button",{onClick:Y,class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-70 transition-all duration-200"},f[32]||(f[32]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",b1,[C.value?(a(),l("img",{key:0,src:C.value,alt:(t=v.value)==null?void 0:t.name,class:"max-w-full max-h-[80vh] object-contain mx-auto block",onClick:f[1]||(f[1]=xe(()=>{},["stop"]))},null,8,y1)):L("",!0)]),f[33]||(f[33]=e("div",{class:"absolute bottom-2 left-1/2 transform -translate-x-1/2"},[e("div",{class:"bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-4 py-2"},[e("p",{class:"text-white text-xs"},"Click outside to close")])],-1))])])):L("",!0)])}}}),x1={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},w1={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},_1={class:"grid grid-cols-1 lg:grid-cols-5 gap-8"},k1={class:"lg:col-span-2 lg:order-1"},C1={class:"space-y-6"},$1={class:"lg:col-span-3 lg:order-2"},M1={class:"p-6"},j1={key:0,class:"mt-1 text-sm text-red-600"},I1={key:1,class:"mt-1 text-sm text-gray-500"},S1={class:"bg-gray-50 rounded-lg p-4"},A1={class:"flex items-start"},B1={class:"flex items-center h-5"},T1=["disabled"],N1={class:"flex justify-end pt-4"},z1=me({__name:"RegisterBid",setup(o){Ne();const s=Me(),r=ge(),c=T(!1),h=T(!1),w=T(""),B=T(""),N=T([]),j=T({referenceNumber:"",selectedAuction:null,agreedToTerms:!1}),v=T({referenceNumber:"",auctionTypeId:"",agreedToTerms:""}),g=q(()=>N.value.map(F=>({key:F.id.toString(),label:F.name,description:F.description||`${F.type} auction`,value:F.id}))),E=q(()=>j.value.referenceNumber.trim()!==""&&j.value.selectedAuction!==null&&j.value.agreedToTerms&&!c.value),D=()=>{v.value={referenceNumber:"",auctionTypeId:"",agreedToTerms:""},w.value=""},M=()=>{D();let F=!0;return j.value.referenceNumber.trim()||(v.value.referenceNumber="Bank deposit reference number is required",F=!1),j.value.selectedAuction||(v.value.auctionTypeId="Please select an auction to register for",F=!1),j.value.agreedToTerms||(v.value.agreedToTerms="required",F=!1),F},_=async()=>{var F,$;try{h.value=!0;const u={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},d=(F=document.querySelector('meta[name="csrf-token"]'))==null?void 0:F.getAttribute("content");d&&(u["X-CSRF-TOKEN"]=d);const p=await ye.get("/api/live-auctions-session",{headers:u,withCredentials:!0});N.value=p.data.data||p.data}catch(u){console.error("Error fetching auction types:",u),(($=u.response)==null?void 0:$.status)===401?console.log("User not authenticated for auction types"):s.error("Failed to load available auctions")}finally{h.value=!1}},m=async()=>{var F,$,u;if(M())try{c.value=!0,D();const d=(F=document.querySelector('meta[name="csrf-token"]'))==null?void 0:F.getAttribute("content");if(!d){w.value="Security token not found. Please refresh the page.",c.value=!1;return}const p=new FormData;p.append("_token",d),p.append("reference_number",j.value.referenceNumber.trim()),p.append("auction_type_id",(($=j.value.selectedAuction)==null?void 0:$.value.toString())||"");const C=await fetch("/register-bid",{method:"POST",body:p,credentials:"same-origin",headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}}),k=await C.json();if(C.ok&&k.success)B.value=k.message||`You have successfully registered to the ${((u=j.value.selectedAuction)==null?void 0:u.label)||"auction"}`,s.success(B.value),setTimeout(()=>{window.location.href="/shop"},1500);else{let V=k.error||k.message||"Registration failed. Please try again.";w.value=V,s.error(V)}}catch(d){console.error("Registration error:",d),w.value="Registration failed. Please check your connection and try again.",s.error(w.value)}finally{c.value=!1}},I=async F=>{console.log("Auth success in RegisterBid:",F),window.user=F,r.setUser(F),await _(),w.value=""};return _e(()=>r.isAuthenticated||!!window.user,async F=>{F&&N.value.length===0&&(console.log("User became authenticated, fetching auction types"),await _())},{immediate:!1}),$e(async()=>{var $;if(!document.querySelector('meta[name="csrf-token"]')){const u=document.createElement("meta");u.name="csrf-token",u.content=(($=window.Laravel)==null?void 0:$.csrfToken)||"",document.head.appendChild(u)}const F=window.user;F?(console.log("User authenticated via server data:",F),r.setUser(F),await _()):console.log("User not authenticated - AuthGuard will handle login")}),(F,$)=>(a(),l("div",x1,[A(i(ht),{title:"Sign in to register for auctions",message:"You need to be signed in to register for auction participation.","require-auth":!0,onAuthSuccess:I},{default:O(()=>[$[11]||($[11]=e("div",{class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},[e("div",{class:"absolute inset-0 bg-gradient-to-b from-transparent to-black/5"}),e("div",{class:"absolute inset-0"},[e("div",{class:"absolute top-2 left-4 w-12 h-12 bg-blue-400/10 rounded-full blur-md"}),e("div",{class:"absolute bottom-2 right-4 w-16 h-16 bg-indigo-400/15 rounded-full blur-lg"}),e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"})]),e("div",{class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},[e("div",{class:"text-center"},[e("h1",{class:"text-3xl font-bold text-white mb-2"},"Register for Auction"),e("p",{class:"text-slate-200 text-lg"},"Join live auctions and start bidding on amazing items")])])],-1)),e("div",w1,[e("div",_1,[e("div",k1,[e("div",C1,[A(i(Fe),{class:"checkout-section order-summary sticky top-8"},{default:O(()=>$[5]||($[5]=[e("div",{class:"p-8"},[e("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Registration Process"),e("div",{class:"space-y-6"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"1")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Make Bank Deposit"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Visit your bank or use online banking to make a deposit. Keep your deposit receipt as you'll need the reference number for registration. "),e("div",{class:"mt-2 text-xs text-blue-600 font-medium"}," 💡 Tip: Save a photo of your receipt ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"2")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Select Your Auction"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Choose from available live auctions. Each auction has different items and schedules. Review the auction details before registering. "),e("div",{class:"mt-2 text-xs text-green-600 font-medium"}," 📅 Check auction dates and times ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"3")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Complete Registration"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Fill out the registration form with your deposit reference number and agree to the terms and conditions. "),e("div",{class:"mt-2 text-xs text-purple-600 font-medium"}," ✅ Review terms carefully ")])]),e("div",{class:"border-l-2 border-gray-200 ml-6 h-4"}),e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center shadow-lg"},[e("span",{class:"text-white font-bold text-lg"},"4")])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-gray-900 mb-2"},"Start Bidding"),e("p",{class:"text-sm text-gray-600 leading-relaxed"}," Once registered, you can participate in the live auction. Place bids on items you're interested in and track your bidding activity. "),e("div",{class:"mt-2 text-xs text-orange-600 font-medium"}," 🎯 Good luck with your bids! ")])])])],-1)])),_:1,__:[5]}),A(i(Fe),{class:"checkout-section"},{default:O(()=>$[6]||($[6]=[e("div",{class:"p-6"},[e("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"Important Information"),e("div",{class:"space-y-4"},[e("div",{class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-blue-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-blue-900"},"Registration Requirements"),e("p",{class:"text-sm text-blue-700 mt-1"}," A valid bank deposit is required to participate in auctions. This ensures serious bidders and helps maintain auction integrity. ")])])]),e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-yellow-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-yellow-900"},"Auction Rules"),e("p",{class:"text-sm text-yellow-700 mt-1"}," Please familiarize yourself with our auction terms and bidding rules before participating. All sales are final. ")])])]),e("div",{class:"bg-green-50 border border-green-200 rounded-lg p-4"},[e("div",{class:"flex items-start"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-green-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("h5",{class:"text-sm font-medium text-green-900"},"Support Available"),e("p",{class:"text-sm text-green-700 mt-1"}," Need help? Our support team is available during auction hours to assist with registration and bidding questions. ")])])])])],-1)])),_:1,__:[6]}),A(i(Fe),{class:"checkout-section security-badge"},{default:O(()=>$[7]||($[7]=[e("div",{class:"p-6"},[e("div",{class:"flex items-center"},[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])]),e("div",{class:"ml-4"},[e("h4",{class:"text-base font-semibold text-green-800"},"Secure & Protected"),e("p",{class:"text-sm text-green-700 mt-1"}," Your registration information is encrypted and securely stored. We use industry-standard security measures to protect your data. ")])])],-1)])),_:1,__:[7]})])]),e("div",$1,[A(i(Fe),{class:"checkout-section form-section"},{default:O(()=>[e("div",M1,[$[10]||($[10]=e("div",{class:"mb-6"},[e("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Auction Registration"),e("p",{class:"text-gray-600"},"Complete your registration to participate in live auctions")],-1)),w.value?(a(),de(i(it),{key:0,variant:"error",title:w.value,class:"mb-6",onClose:$[0]||($[0]=u=>w.value="")},null,8,["title"])):L("",!0),B.value?(a(),de(i(it),{key:1,variant:"success",title:B.value,class:"mb-6",onClose:$[1]||($[1]=u=>B.value="")},null,8,["title"])):L("",!0),e("form",{onSubmit:xe(m,["prevent"]),class:"space-y-6"},[e("div",null,[A(i(he),{modelValue:j.value.referenceNumber,"onUpdate:modelValue":$[2]||($[2]=u=>j.value.referenceNumber=u),label:"Bank Deposit Reference Number",placeholder:"Enter your bank deposit reference number",required:"",error:v.value.referenceNumber,disabled:c.value,"help-text":"Enter the reference number from your bank deposit receipt"},null,8,["modelValue","error","disabled"])]),e("div",null,[$[8]||($[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[W(" Select Auction "),e("span",{class:"text-red-500"},"*")],-1)),A(i(Tt),{modelValue:j.value.selectedAuction,"onUpdate:modelValue":$[3]||($[3]=u=>j.value.selectedAuction=u),items:g.value,placeholder:"Choose an auction to register for",disabled:c.value||h.value,error:v.value.auctionTypeId,searchable:""},null,8,["modelValue","items","disabled","error"]),v.value.auctionTypeId?(a(),l("p",j1,x(v.value.auctionTypeId),1)):L("",!0),h.value?(a(),l("p",I1," Loading available auctions... ")):L("",!0)]),e("div",S1,[e("div",A1,[e("div",B1,[pe(e("input",{id:"terms","onUpdate:modelValue":$[4]||($[4]=u=>j.value.agreedToTerms=u),type:"checkbox",class:G(["h-4 w-4 rounded transition-colors duration-200",v.value.agreedToTerms?"border-red-500 text-red-600 focus:ring-red-500 bg-red-50":"text-primary-600 focus:ring-primary-500 border-gray-300"]),disabled:c.value},null,10,T1),[[Ee,j.value.agreedToTerms]])]),$[9]||($[9]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"terms",class:"font-medium text-gray-700"}," I agree to the terms and conditions "),e("p",{class:"text-gray-500"},[W(" By registering, you agree to our auction terms, bidding rules, and payment policies. "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Read full terms")])],-1))])]),e("div",N1,[A(i(te),{type:"submit",variant:"primary",size:"lg",loading:c.value,disabled:!E.value,class:"btn-enhanced"},{default:O(()=>[c.value?(a(),l(ue,{key:0},[W(" Registering... ")],64)):(a(),l(ue,{key:1},[W(" Register for Auction ")],64))]),_:1},8,["loading","disabled"])])],32)])]),_:1})])])])]),_:1,__:[11]})]))}});const Ct=ke(z1,[["__scopeId","data-v-e8f87a63"]]),D1=We("bidDashboard",()=>{const o=T([]),s=T([]),r=T([]),c=T({total_bids:0,active_bids:0,won_auctions:0,lost_auctions:0,watchlist_count:0,total_bid_amount:0,average_bid_amount:0,highest_bid:0,win_rate:0}),h=T(!1),w=T(null),B=T(0),N=q(()=>o.value.length>0),j=q(()=>s.value.length>0),v=q(()=>r.value.length>0),g=async()=>{var p,C,k,V;const d=ge();if(!d.isAuthenticated)return m(),!1;h.value=!0,w.value=null;try{const Z={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},oe=(p=document.querySelector('meta[name="csrf-token"]'))==null?void 0:p.getAttribute("content");oe&&(Z["X-CSRF-TOKEN"]=oe),d.token&&(Z.Authorization=`Bearer ${d.token}`);const le=await fetch("/api/bid-dashboard",{headers:Z,credentials:"include"});if(!le.ok)throw new Error("Failed to fetch dashboard data");const ee=await le.json();return o.value=Array.isArray(ee.active_bids)?ee.active_bids:((C=ee.active_bids)==null?void 0:C.data)||[],s.value=Array.isArray(ee.bid_history)?ee.bid_history:((k=ee.bid_history)==null?void 0:k.data)||[],r.value=Array.isArray(ee.watchlist)?ee.watchlist:((V=ee.watchlist)==null?void 0:V.data)||[],c.value=ee.stats||c.value,B.value=Date.now(),!0}catch(Z){return w.value=Z instanceof Error?Z.message:"Failed to fetch dashboard data",console.error("Dashboard fetch error:",Z),!1}finally{h.value=!1}},E=async(d=1,p=15,C="")=>{const k=ge();if(!k.isAuthenticated)return{data:[],meta:{}};try{const V=new URLSearchParams({page:d.toString(),per_page:p.toString(),...C&&{search:C}}),Z=await fetch(`/api/bid-dashboard/active-bids?${V}`,{headers:{Accept:"application/json",Authorization:`Bearer ${k.token}`}});if(!Z.ok)throw new Error("Failed to fetch active bids");const oe=await Z.json();return d===1?o.value=oe.data||[]:o.value.push(...oe.data||[]),oe}catch(V){return w.value=V instanceof Error?V.message:"Failed to fetch active bids",console.error("Active bids fetch error:",V),{data:[],meta:{}}}},D=async(d=1,p=15,C="",k="")=>{var Z;const V=ge();if(!V.isAuthenticated)return{data:[],meta:{}};try{const le=`/api/bid-dashboard/bid-history?${new URLSearchParams({page:d.toString(),per_page:p.toString(),...C&&{search:C},...k&&{status:k}})}`,ee={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},R=(Z=document.querySelector('meta[name="csrf-token"]'))==null?void 0:Z.getAttribute("content");R&&(ee["X-CSRF-TOKEN"]=R),V.token&&(ee.Authorization=`Bearer ${V.token}`);const Y=await fetch(le,{headers:ee,credentials:"include"});if(!Y.ok)throw new Error(`Failed to fetch bid history: ${Y.status} ${Y.statusText}`);const ae=await Y.json();return d===1?s.value=ae.data||[]:s.value.push(...ae.data||[]),ae}catch(oe){return w.value=oe instanceof Error?oe.message:"Failed to fetch bid history",console.error("Bid history fetch error:",oe),{data:[],meta:{}}}},M=async()=>{const d=ge();if(!d.isAuthenticated)return!1;try{const p=await fetch("/api/bid-dashboard/stats",{headers:{Accept:"application/json",Authorization:`Bearer ${d.token}`}});if(!p.ok)throw new Error("Failed to fetch stats");const C=await p.json();return c.value=C,!0}catch(p){return w.value=p instanceof Error?p.message:"Failed to fetch stats",console.error("Stats fetch error:",p),!1}},_=async()=>await g(),m=()=>{o.value=[],s.value=[],r.value=[],c.value={total_bids:0,active_bids:0,won_auctions:0,lost_auctions:0,watchlist_count:0,total_bid_amount:0,average_bid_amount:0,highest_bid:0,win_rate:0},w.value=null,B.value=0};return{activeBids:o,bidHistory:s,watchlistItems:r,stats:c,isLoading:h,error:w,lastSyncTime:B,hasActiveBids:N,hasBidHistory:j,hasWatchlistItems:v,fetchDashboardData:g,fetchActiveBids:E,fetchBidHistory:D,fetchStats:M,refreshDashboard:_,clearDashboard:m,getActiveBidById:d=>o.value.find(p=>p.id===d),getBidHistoryById:d=>s.value.find(p=>p.id===d),isActiveBid:d=>o.value.some(p=>p.id===d),initializeDashboard:async()=>{ge().isAuthenticated&&await g()}}}),L1={class:"bg-white -mx-4 -my-5 sm:-m-6"},P1={class:"px-4 py-5 sm:px-6 border-b border-gray-200 bg-gray-50"},E1={class:"flex items-center justify-between"},V1={class:"flex items-center space-x-3"},R1={class:"text-sm text-gray-600 mt-0.5 max-w-md truncate"},F1={class:"p-6"},q1={key:0,class:"flex flex-col items-center justify-center py-16"},H1={key:1,class:"text-center py-16"},O1={class:"text-gray-600 mb-6"},W1={key:2},U1={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8"},Y1={class:"bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"},K1={class:"flex items-center"},Q1={class:"ml-3"},X1={class:"text-lg font-bold text-blue-700"},G1={class:"bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200"},Z1={class:"flex items-center"},J1={class:"ml-3"},em={class:"text-lg font-bold text-green-700"},tm={class:"relative"},sm={class:"space-y-6"},om={class:"relative flex-shrink-0"},am={key:0,class:"absolute -top-1 -left-1 w-5 h-5 bg-green-100 rounded-full animate-pulse"},rm={class:"flex-1 min-w-0"},nm={class:"flex items-center justify-between"},lm={class:"flex items-center space-x-3"},im={class:"w-10 h-10 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center"},dm={class:"text-sm font-medium text-gray-600"},um={class:"flex items-center space-x-2"},cm={class:"text-sm font-semibold text-gray-900"},mm={class:"text-xs text-gray-500 mt-0.5"},vm={class:"text-right"},gm={key:0,class:"flex items-center justify-end mt-1"},pm={class:"text-xs font-medium text-green-600"},hm={key:3,class:"text-center py-16"},fm=me({__name:"BidTimelineModal",props:{show:{type:Boolean},item:{}},emits:["close"],setup(o,{emit:s}){const r=o,c=T([]),h=T(!1),w=T(null),B=q(()=>new Set(c.value.map(g=>g.user_id)).size);q(()=>c.value.length>0?Math.max(...c.value.map(v=>v.bid_amount||0)):0),q(()=>c.value.length===0?0:c.value.reduce((g,E)=>g+(E.bid_amount||0),0)/c.value.length);const N=v=>{const g=new Date(v);return(new Date().getTime()-g.getTime())/(1e3*60)<=5},j=async()=>{var v,g;if((v=r.item)!=null&&v.id){h.value=!0,w.value=null;try{const E={Accept:"application/json","X-Requested-With":"XMLHttpRequest"},D=(g=document.querySelector('meta[name="csrf-token"]'))==null?void 0:g.getAttribute("content");D&&(E["X-CSRF-TOKEN"]=D);const M=localStorage.getItem("auth_token");M&&(E.Authorization=`Bearer ${M}`);const _=await fetch(`/api/open-item/${r.item.id}/bids?limit=10`,{headers:E,credentials:"include"});if(!_.ok)throw new Error("Failed to fetch bid history");const m=await _.json();c.value=m.data||[]}catch(E){w.value=E instanceof Error?E.message:"Failed to load bid history",console.error("Error fetching bids:",E)}finally{h.value=!1}}};return _e(()=>r.show,v=>{v&&r.item&&j()}),(v,g)=>(a(),de(i(St),{show:v.show,onClose:g[1]||(g[1]=E=>v.$emit("close")),"max-width":"2xl",closeable:!0},{default:O(()=>{var E;return[e("div",L1,[e("div",P1,[e("div",E1,[e("div",V1,[g[3]||(g[3]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",null,[g[2]||(g[2]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Bid History",-1)),e("p",R1,x((E=v.item)==null?void 0:E.name),1)])]),e("button",{onClick:g[0]||(g[0]=D=>v.$emit("close")),class:"flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"},g[4]||(g[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("div",F1,[h.value?(a(),l("div",q1,g[5]||(g[5]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mb-4"},null,-1),e("p",{class:"text-sm text-gray-500"},"Loading bid history...",-1)]))):w.value?(a(),l("div",H1,[g[7]||(g[7]=e("div",{class:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),g[8]||(g[8]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"Failed to load bid history",-1)),e("p",O1,x(w.value),1),e("button",{onClick:j,class:"inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200"},g[6]||(g[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),W(" Try again ")]))])):c.value.length>0?(a(),l("div",W1,[e("div",U1,[e("div",Y1,[e("div",K1,[g[10]||(g[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})])])],-1)),e("div",Q1,[g[9]||(g[9]=e("p",{class:"text-sm font-medium text-blue-900"},"Total Bids",-1)),e("p",X1,x(c.value.length),1)])])]),e("div",G1,[e("div",Z1,[g[12]||(g[12]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])])],-1)),e("div",J1,[g[11]||(g[11]=e("p",{class:"text-sm font-medium text-green-900"},"Bidders",-1)),e("p",em,x(B.value),1)])])])]),e("div",tm,[g[16]||(g[16]=e("div",{class:"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"},null,-1)),e("div",sm,[(a(!0),l(ue,null,ce(c.value,(D,M)=>{var _,m;return a(),l("div",{key:D.id,class:"relative flex items-start space-x-4 group"},[e("div",om,[e("div",{class:G(["w-3 h-3 rounded-full border-2 border-white shadow-sm transition-all duration-200 group-hover:scale-110",M===0?"bg-green-500":"bg-gray-400"])},null,2),M===0?(a(),l("div",am)):L("",!0)]),e("div",rm,[e("div",{class:G(["bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 group-hover:border-gray-300",M===0?"ring-2 ring-green-100 border-green-200":""])},[e("div",nm,[e("div",lm,[e("div",im,[e("span",dm,x((((_=D.user)==null?void 0:_.name)||"Anonymous").charAt(0).toUpperCase()),1)]),e("div",null,[e("div",um,[e("span",cm,x(((m=D.user)==null?void 0:m.name)||"Anonymous Bidder"),1),M===0?(a(),de(i(Ve),{key:0,variant:"success",size:"sm",class:"animate-pulse"},{default:O(()=>g[13]||(g[13]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3l14 9-14 9V3z"})],-1),W(" Leading ")])),_:1,__:[13]})):L("",!0),N(D.created_at)?(a(),de(i(Ve),{key:1,variant:"info",size:"sm"},{default:O(()=>g[14]||(g[14]=[W(" New ")])),_:1,__:[14]})):L("",!0)]),e("p",mm,x(i(ut)(D.created_at)),1)])]),e("div",vm,[e("div",{class:G(["text-lg font-bold transition-colors duration-200",M===0?"text-green-600":"text-gray-900"])},x(i(kt)(D.bid_amount)),3),M<c.value.length-1?(a(),l("div",gm,[g[15]||(g[15]=e("svg",{class:"w-3 h-3 text-green-500 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 11l5-5m0 0l5 5m-5-5v12"})],-1)),e("span",pm," +"+x(i(kt)(D.bid_amount-c.value[M+1].bid_amount)),1)])):L("",!0)])])],2)])])}),128))])])])):(a(),l("div",hm,g[17]||(g[17]=[e("div",{class:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1),e("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"No bids yet",-1),e("p",{class:"text-gray-500"},"Be the first to place a bid on this item!",-1)])))])])]}),_:1},8,["show"]))}}),bm={key:0,class:"bg-white border border-gray-200 rounded-2xl overflow-hidden hover:shadow-lg hover:border-gray-300 transition-all duration-300 group"},ym={class:"relative"},xm={class:"flex items-center justify-between"},wm={class:"flex items-center space-x-3"},_m={class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},km={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"},Cm={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},$m={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},Mm={class:"text-right"},jm={class:"text-xs text-white/80 font-medium uppercase tracking-wide"},Im={class:"text-sm font-semibold text-white"},Sm={class:"p-6"},Am={class:"flex items-start space-x-4"},Bm={class:"flex-shrink-0 relative"},Tm={class:"relative overflow-hidden rounded-xl shadow-sm"},Nm=["src","alt"],zm={key:0,class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center animate-pulse"},Dm={class:"flex-1 min-w-0"},Lm={class:"flex items-start justify-between mb-3"},Pm={class:"flex-1"},Em={class:"text-lg font-bold text-gray-900 truncate group-hover:text-gray-700 transition-colors duration-200"},Vm={class:"flex items-center mt-2 space-x-2"},Rm={class:"inline-flex items-center px-3 py-1 text-xs font-semibold bg-gray-100 text-gray-700 rounded-full"},Fm={key:0,class:"inline-flex items-center px-3 py-1 text-xs font-semibold bg-blue-50 text-blue-700 rounded-full"},qm={class:"grid grid-cols-2 gap-4 mb-4"},Hm={class:"bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200"},Om={class:"text-xl font-bold text-blue-900"},Wm={class:"text-xs text-blue-600 mt-1"},Um={class:"flex items-center justify-between mb-2"},Ym={key:0,class:"mb-4"},Km={class:"flex items-center justify-between mb-2"},Qm={class:"w-full bg-gray-200 rounded-full h-2"},Xm={class:"mb-4"},Gm={key:0,class:"flex items-center p-3 bg-green-50 border border-green-200 rounded-lg"},Zm={key:1,class:"flex items-center p-3 bg-amber-50 border border-amber-200 rounded-lg"},Jm={key:2,class:"flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg"},ev={class:"text-xs text-gray-600"},tv={class:"flex items-center justify-between"},sv={class:"flex items-center space-x-4"},ov=["disabled"],av=["disabled"],rv={key:1,class:"bg-gray-50 border border-gray-200 rounded-xl p-5 opacity-60"},nv=me({__name:"BidCard",props:{bid:{},variant:{}},emits:["view-details","increase-bid"],setup(o,{emit:s}){const r=o,c=s,h=T(!1),w=T(!1),B=q(()=>r.bid&&r.bid.item&&r.bid.item.id),N=q(()=>{var z;return((z=r.bid.item)==null?void 0:z.name)||"Unknown Item"}),j=q(()=>{var z;return((z=r.bid.auctionType)==null?void 0:z.name)||"Unknown Type"}),v=q(()=>{var z,y;return h.value?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+":((z=r.bid.item)==null?void 0:z.image)||((y=r.bid.item)==null?void 0:y.cropped)||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"}),g=q(()=>parseFloat(r.bid.bid_amount||"0")),E=q(()=>{var z;return parseFloat(((z=r.bid.item)==null?void 0:z.bid_amount)||"0")}),D=q(()=>{var z,y;return!r.bid.date_to&&!((z=r.bid.item)!=null&&z.date_to)?"No end date":Pt(r.bid.date_to||((y=r.bid.item)==null?void 0:y.date_to))}),M=q(()=>ut(r.bid.created_at)),_=q(()=>r.variant==="active"?F.value?"success":"warning":r.bid.closed_by===r.bid.user_id?"success":"secondary"),m=q(()=>r.variant==="active"?F.value?"Winning":"Outbid":r.bid.closed_by===r.bid.user_id?"Won":"Lost"),I=q(()=>{var P;if(r.variant!=="active"||!B.value)return!1;const z=new Date,y=r.bid.date_to?new Date(r.bid.date_to):(P=r.bid.item)!=null&&P.date_to?new Date(r.bid.item.date_to):null;return y&&y<=z?!1:g.value<E.value}),F=q(()=>r.variant!=="active"||!B.value?!1:g.value>=E.value&&g.value>0),$=q(()=>r.variant==="active"?F.value?"bg-gradient-to-r from-green-500 to-green-600":"bg-gradient-to-r from-amber-500 to-amber-600":r.bid.closed_by===r.bid.user_id?"bg-gradient-to-r from-green-500 to-green-600":"bg-gradient-to-r from-gray-500 to-gray-600"),u=q(()=>r.variant==="active"?F.value?"bg-green-600":"bg-amber-600":r.bid.closed_by===r.bid.user_id?"bg-green-600":"bg-gray-600"),d=q(()=>{var ie,fe;if(r.variant==="history")return ut(r.bid.date_to||((ie=r.bid.item)==null?void 0:ie.date_to)||r.bid.created_at);const z=r.bid.date_to||((fe=r.bid.item)==null?void 0:fe.date_to);if(!z)return"No end date";const y=new Date,H=new Date(z).getTime()-y.getTime();if(H<=0)return"Ended";const K=Math.floor(H/(1e3*60*60)),J=Math.floor(H%(1e3*60*60)/(1e3*60));return K>24?`${Math.floor(K/24)}d ${K%24}h`:K>0?`${K}h ${J}m`:`${J}m`}),p=q(()=>{var K;if(r.variant!=="active")return!1;const z=r.bid.date_to||((K=r.bid.item)==null?void 0:K.date_to);if(!z)return!1;const y=new Date;return new Date(z).getTime()-y.getTime()<=1e3*60*60&&!F.value}),C=q(()=>F.value?"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200":"bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200"),k=q(()=>F.value?"text-gray-700":"text-red-700"),V=q(()=>F.value?"text-gray-900":"text-red-900"),Z=q(()=>{const z=E.value-g.value;return z<=0?"You lead":`+${Pe(z)} ahead`}),oe=q(()=>{if(E.value===0)return 100;const z=g.value/E.value*100;return Math.min(Math.max(z,10),100)}),le=q(()=>F.value?"bg-gradient-to-r from-green-400 to-green-500":"bg-gradient-to-r from-amber-400 to-amber-500"),ee=()=>{h.value=!0},R=()=>{var z;(z=r.bid.item)!=null&&z.id&&c("view-details",r.bid)},Y=()=>{c("increase-bid",r.bid)},ae=()=>{var z;(z=r.bid.item)!=null&&z.id&&(w.value=!0)};return(z,y)=>{var P,H,K;return a(),l(ue,null,[B.value?(a(),l("div",bm,[e("div",ym,[e("div",{class:G([$.value,"px-6 py-4"])},[e("div",xm,[e("div",wm,[e("div",{class:G([u.value,"p-2 rounded-lg"])},[(a(),l("svg",_m,[F.value&&z.variant==="active"?(a(),l("path",km)):z.variant==="active"?(a(),l("path",Cm)):(a(),l("path",$m))]))],2),e("div",null,[A(i(Ve),{variant:_.value,size:"sm",class:"text-white border-white/20"},{default:O(()=>[W(x(m.value),1)]),_:1},8,["variant"])])]),e("div",Mm,[e("div",jm,x(z.variant==="active"?"Time Left":"Ended"),1),e("div",Im,x(d.value),1)])])],2)]),e("div",Sm,[e("div",Am,[e("div",Bm,[e("div",Tm,[e("img",{src:v.value,alt:N.value,class:"w-24 h-24 object-cover group-hover:scale-105 transition-transform duration-300",onError:ee},null,40,Nm),y[1]||(y[1]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1))]),p.value?(a(),l("div",zm,y[2]||(y[2]=[e("svg",{class:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)]))):L("",!0)]),e("div",Dm,[e("div",Lm,[e("div",Pm,[e("h3",Em,x(N.value),1),e("div",Vm,[e("span",Rm,x(j.value),1),(P=z.bid.item)!=null&&P.code?(a(),l("span",Fm," #"+x(z.bid.item.code),1)):L("",!0)])])]),e("div",qm,[e("div",Hm,[y[3]||(y[3]=e("div",{class:"flex items-center justify-between mb-2"},[e("span",{class:"text-xs font-bold text-blue-700 uppercase tracking-wide"},"Your Bid"),e("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",Om,x(i(Pe)(g.value)),1),e("div",Wm,x(M.value),1)]),e("div",{class:G(C.value)},[e("div",Um,[e("span",{class:G(["text-xs font-bold uppercase tracking-wide",k.value])},"Current Bid",2),(a(),l("svg",{class:G(["w-4 h-4",k.value]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},y[4]||(y[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"},null,-1)]),2))]),e("div",{class:G(["text-xl font-bold",V.value])},x(i(Pe)(E.value)),3),e("div",{class:G(["text-xs mt-1",k.value])},x(Z.value),3)],2)]),z.variant==="active"?(a(),l("div",Ym,[e("div",Km,[y[5]||(y[5]=e("span",{class:"text-xs font-medium text-gray-600"},"Bid Position",-1)),e("span",{class:G(["text-xs font-semibold",F.value?"text-green-600":"text-amber-600"])},x(F.value?"Leading":"Behind"),3)]),e("div",Qm,[e("div",{class:G([le.value,"h-2 rounded-full transition-all duration-500"]),style:He({width:oe.value+"%"})},null,6)])])):L("",!0),e("div",Xm,[F.value&&z.variant==="active"?(a(),l("div",Gm,y[6]||(y[6]=[e("svg",{class:"w-5 h-5 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})],-1),e("div",null,[e("div",{class:"text-sm font-semibold text-green-800"},"You're currently winning!"),e("div",{class:"text-xs text-green-600"},"Keep monitoring for new bids")],-1)]))):z.variant==="active"?(a(),l("div",Zm,y[7]||(y[7]=[e("svg",{class:"w-5 h-5 text-amber-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1),e("div",null,[e("div",{class:"text-sm font-semibold text-amber-800"},"You've been outbid"),e("div",{class:"text-xs text-amber-600"},"Consider placing a higher bid")],-1)]))):z.variant==="history"?(a(),l("div",Jm,[y[9]||(y[9]=e("svg",{class:"w-5 h-5 text-gray-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[y[8]||(y[8]=e("div",{class:"text-sm font-semibold text-gray-800"},"Auction completed",-1)),e("div",ev,x(D.value),1)])])):L("",!0)]),e("div",tv,[e("div",sv,[e("button",{onClick:R,class:"text-sm text-gray-600 hover:text-gray-900 transition-colors underline",disabled:!((H=z.bid.item)!=null&&H.id)}," View Details ",8,ov),e("button",{onClick:ae,class:"text-sm text-gray-600 hover:text-gray-900 transition-colors underline",disabled:!((K=z.bid.item)!=null&&K.id),title:"View bid history"}," History ",8,av)]),z.variant==="active"&&I.value?(a(),de(i(te),{key:0,onClick:Y,variant:"primary",size:"sm",class:"font-medium"},{default:O(()=>y[10]||(y[10]=[W(" Increase Bid ")])),_:1,__:[10]})):L("",!0)])])])])])):(a(),l("div",rv,y[11]||(y[11]=[Be('<div class="flex items-center justify-center text-gray-500"><svg class="w-8 h-8 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg><div><p class="text-sm font-medium">Invalid Bid Record</p><p class="text-xs">This bid is missing required information</p></div></div>',1)]))),A(fm,{show:w.value,item:z.bid.item,onClose:y[0]||(y[0]=J=>w.value=!1)},null,8,["show","item"])],64)}}}),lv={class:"py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50/50 transition-colors duration-200 group"},iv={class:"flex items-center space-x-3"},dv={class:"flex-shrink-0"},uv=["src","alt"],cv={class:"flex-1 min-w-0"},mv={class:"flex items-center justify-between"},vv={class:"flex-1 min-w-0"},gv={class:"text-sm font-medium text-gray-900 truncate"},pv={class:"flex items-center mt-1 space-x-3 text-xs text-gray-500"},hv={key:0},fv={key:1},bv={key:0,class:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"},yv=me({__name:"WatchlistItemCard",props:{item:{},variant:{default:"default"}},emits:["view-details","remove-from-watchlist","place-bid"],setup(o,{emit:s}){const r=o,c=s,h=q(()=>{if(!r.item.date_to)return"";const N=new Date,j=new Date(r.item.date_to);return j<=N?"Ended":tr(j)}),w=q(()=>{if(r.item.closed_by)return!1;if(r.item.date_to){const N=new Date;return new Date(r.item.date_to)>N}return!0}),B=()=>{c("place-bid",r.item)};return(N,j)=>{var v;return a(),l("div",lv,[e("div",iv,[e("div",dv,[e("img",{src:N.item.image||"/images/placeholder.jpg",alt:N.item.name,class:"w-12 h-12 object-cover rounded"},null,8,uv)]),e("div",cv,[e("div",mv,[e("div",vv,[e("h3",gv,x(N.item.name),1),e("div",pv,[e("span",null,x(((v=N.item.auctionType)==null?void 0:v.name)||"Unknown Type"),1),j[2]||(j[2]=e("span",null,"•",-1)),e("span",null,x(i(Pe)(N.item.bid_amount||0)),1),N.item.date_to?(a(),l("span",hv,"•")):L("",!0),N.item.date_to?(a(),l("span",fv,x(h.value),1)):L("",!0)])]),e("button",{onClick:j[0]||(j[0]=g=>N.$emit("remove-from-watchlist",N.item)),class:"p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 opacity-0 group-hover:opacity-100",title:"Remove from watchlist"},j[3]||(j[3]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),N.variant!=="compact"?(a(),l("div",bv,[e("button",{onClick:j[1]||(j[1]=g=>N.$emit("view-details",N.item)),class:"text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200"}," View "),w.value?(a(),l("button",{key:0,onClick:B,class:"text-xs text-gray-900 hover:text-gray-700 font-medium transition-colors duration-200"}," Bid ")):L("",!0)])):L("",!0)])])])}}}),xv={class:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100"},wv={class:"relative bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 overflow-hidden border-b border-slate-600/30"},_v={class:"py-6"},kv={class:"flex items-center justify-between"},Cv={class:"flex items-center space-x-3"},$v={key:0,class:"flex justify-center py-16"},Mv={class:"text-center"},jv={key:1,class:"space-y-8"},Iv={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Sv={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Av={class:"flex items-center justify-between"},Bv={class:"text-3xl font-bold text-gray-900 mt-2"},Tv={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Nv={class:"flex items-center justify-between"},zv={class:"text-3xl font-bold text-gray-900 mt-2"},Dv={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Lv={class:"flex items-center justify-between"},Pv={class:"text-3xl font-bold text-gray-900 mt-2"},Ev={class:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 group"},Vv={class:"flex items-center justify-between"},Rv={class:"text-3xl font-bold text-gray-900 mt-2"},Fv={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},qv={class:"lg:col-span-2"},Hv={class:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"},Ov={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},Wv={class:"flex items-center justify-between"},Uv={class:"p-6"},Yv={key:0,class:"text-center py-12"},Kv={key:1},Qv={class:"w-full"},Xv={key:0,class:"text-center py-4 text-sm text-gray-500"},Gv={class:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"},Zv={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},Jv={class:"flex items-center justify-between"},eg={key:0,class:"text-center py-12 px-6"},tg={key:1,class:"px-6"},sg={class:"bg-white rounded-xl shadow-lg border border-gray-200/50 overflow-hidden"},og={class:"bg-gradient-to-r from-gray-50 to-slate-50 px-6 py-4 border-b border-gray-200/50"},ag={class:"flex items-center justify-between"},rg={class:"flex items-center space-x-2"},ng={class:"relative"},lg={class:"p-0"},ig={key:0,class:"text-center py-12 px-6"},dg={key:1},ug={key:0,class:"px-6 py-4 border-t border-gray-200"},$t=me({__name:"BidDashboard",setup(o){const s=ge(),r=D1(),c=Ue(),h=Me(),w=Ne(),B=q(()=>r.activeBids.length>0||r.bidHistory.length>0||c.items.length>0),N=q(()=>r.activeBids.filter(z=>z.item&&z.item.id)),j=q(()=>r.activeBids.length-N.value.length),v=T([]),g=T({current_page:1,last_page:1,per_page:10,total:0,from:0,to:0}),E=T(!1),D=T(""),M=q(()=>v.value.length>0?v.value:r.bidHistory.slice(0,10)),_=[{key:"item",label:"Item",sortable:!1,width:"300px",render:(z,y)=>{const P=oe(y),H=le(y);return`
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <img
              src="${Z(y)}"
              alt="${P}"
              class="w-12 h-12 object-cover rounded-lg"
              onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+'"
            />
          </div>
          <div class="min-w-0 flex-1">
            <div class="text-sm font-medium text-gray-900 truncate">
              ${P}
            </div>
            <div class="text-sm text-gray-500 truncate">
              ${H}
            </div>
          </div>
        </div>
      `}},{key:"bid_amount",label:"Bid Amount",sortable:!0,align:"right",render:(z,y)=>{const P=parseFloat(y.bid_amount||"0");return`
        <div class="text-right">
          <div class="text-sm font-semibold text-gray-900">
            ${Pe(P)}
          </div>
        </div>
      `}},{key:"status",label:"Status",sortable:!1,align:"center",render:(z,y)=>{const P=ee(y),H=R(y);return`
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${P==="success"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}">
          ${H}
        </span>
      `}},{key:"created_at",label:"Date Placed",sortable:!0,render:(z,y)=>`
        <div class="text-sm text-gray-900">
          ${Pt(y.created_at)}
        </div>
      `},{key:"actions",label:"",sortable:!1,align:"center",width:"60px",render:(z,y)=>{var J;const P=!((J=y.item)!=null&&J.id),H=P?"inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-400 bg-gray-100 cursor-not-allowed":"inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-600 bg-gray-50 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer transition-colors duration-200",K=`
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      `;return`
        <button
          class="${H}"
          onclick="window.handleViewBidDetails && window.handleViewBidDetails(${JSON.stringify(y).replace(/"/g,"&quot;")})"
          ${P?"disabled":""}
          title="View Details"
        >
          ${K}
        </button>
      `}}],m=async()=>{!r.activeBids.length&&!r.bidHistory.length&&await I()},I=async()=>{try{await c.initializeWatchlist();const[z,y]=await Promise.all([r.fetchDashboardData(),c.fetchWatchlist(),C(1)]);z||console.warn("Failed to load dashboard data"),y||console.warn("Failed to load watchlist data")}catch(z){console.error("Error loading dashboard data:",z),h.error("Failed to load dashboard data. Please try refreshing the page.")}},F=async()=>{await I(),h.success("Dashboard refreshed successfully")},$=z=>{var y;(y=z.item)!=null&&y.id&&w.push(`/item/${z.item.id}`)},u=z=>{var y;(y=z.item)!=null&&y.id&&w.push(`/item/${z.item.id}`)},d=z=>{w.push(`/item/${z.id}`)},p=async z=>{await c.removeFromWatchlist(z.id)?h.success(`${z.name} removed from watchlist`):h.error(c.error||"Failed to remove item from watchlist")},C=async(z=1)=>{E.value=!0;try{const y=await r.fetchBidHistory(z,g.value.per_page,D.value,"");v.value=y.data||[],g.value={current_page:y.current_page||1,last_page:y.last_page||1,per_page:y.per_page||10,total:y.total||0,from:y.from||0,to:y.to||0}}catch(y){console.error("Failed to load bid history:",y),v.value=[]}finally{E.value=!1}},k=z=>{C(z)},V=()=>{clearTimeout(Y),Y=setTimeout(()=>{C(1)},500)},Z=z=>{var y,P;return((y=z.item)==null?void 0:y.image)||((P=z.item)==null?void 0:P.cropped)||"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkM0MS42NzMxIDMyIDU2IDQ2LjMyNjkgNTYgNjRDNTYgNjQuNzM1NiA1NS45NDE0IDY1LjQ1NzggNTUuODI4NCA2Ni4xNjI4TDQ4IDU4LjMzNDRMMzYgNzAuMzM0NEwyNCA1OC4zMzQ0TDE2LjE3MTYgNjYuMTYyOEMxNi4wNTg2IDY1LjQ1NzggMTYgNjQuNzM1NiAxNiA2NEMxNiA0Ni4zMjY5IDMwLjMyNjkgMzIgNDggMzJaIiBmaWxsPSIjRTVFN0VCIi8+CjxjaXJjbGUgY3g9IjM2IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+"},oe=z=>{var y;return((y=z.item)==null?void 0:y.name)||"Unknown Item"},le=z=>{var y;return((y=z.auctionType)==null?void 0:y.name)||"Unknown Type"},ee=z=>z.closed_by===z.user_id?"success":"secondary",R=z=>z.closed_by===z.user_id?"Won":"Lost";let Y;const ae=async()=>{const z=window.user;z&&!s.user&&s.setUser(z),!s.user&&!s.isLoading&&await s.initialize()};return $e(async()=>{window.handleViewBidDetails=$,await ae(),s.isAuthenticated&&await I()}),_e(()=>s.isAuthenticated,async(z,y)=>{z&&!y?await I():!z&&y&&r.clearDashboard()},{immediate:!1}),(z,y)=>{const P=gt("router-link");return a(),l("div",xv,[A(ht,{title:"Sign in to view your bid dashboard",message:"You need to be signed in to access your bidding activity and manage your watchlist.","fallback-style":"page",onAuthSuccess:m},{default:O(()=>[e("div",wv,[y[6]||(y[6]=e("div",{class:"absolute inset-0 bg-gradient-to-b from-transparent to-black/5"},null,-1)),y[7]||(y[7]=e("div",{class:"absolute inset-0"},[e("div",{class:"absolute top-2 left-4 w-12 h-12 bg-white/5 rounded-full blur-md"}),e("div",{class:"absolute bottom-2 right-4 w-16 h-16 bg-white/8 rounded-full blur-lg"}),e("div",{class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl"}),e("div",{class:"absolute inset-0 opacity-5",style:{"background-image":"radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)","background-size":"20px 20px"}})],-1)),A(i(we),{class:"relative z-10"},{default:O(()=>[e("div",_v,[e("div",kv,[y[5]||(y[5]=e("div",{class:"flex items-center space-x-4"},[e("div",{class:"flex items-center justify-center w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full border border-white/20 shadow-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])]),e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-100 tracking-tight"}," Bid Dashboard "),e("nav",{class:"mt-1"},[e("ol",{class:"flex items-center space-x-2 text-sm text-slate-300"},[e("li",null,[e("a",{href:"/",class:"hover:text-white transition-colors duration-200"},"Home")]),e("li",null,[e("svg",{class:"w-3 h-3 mx-1 text-slate-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})])]),e("li",{class:"text-gray-100 font-medium"},"Bid Dashboard")])])])],-1)),e("div",Cv,[A(i(te),{onClick:F,loading:i(r).isLoading,variant:"outline",size:"sm",class:"bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 backdrop-blur-sm text-sm transition-all duration-200 shadow-lg hover:shadow-xl"},{default:O(()=>y[4]||(y[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),W(" Refresh ")])),_:1,__:[4]},8,["loading"])])])])]),_:1})]),A(i(we),{class:"py-8"},{default:O(()=>[i(r).isLoading&&!B.value?(a(),l("div",$v,[e("div",Mv,[A(i(ze),{size:"lg"}),y[8]||(y[8]=e("p",{class:"mt-4 text-sm text-gray-500"},"Loading your dashboard...",-1))])])):(a(),l("div",jv,[e("div",Iv,[e("div",Sv,[e("div",Av,[e("div",null,[y[9]||(y[9]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Active Bids",-1)),e("p",Bv,x(i(r).stats.active_bids),1),y[10]||(y[10]=e("p",{class:"text-gray-500 text-sm mt-1"},"Currently bidding",-1))]),y[11]||(y[11]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Tv,[e("div",Nv,[e("div",null,[y[12]||(y[12]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Won Auctions",-1)),e("p",zv,x(i(r).stats.won_auctions),1),y[13]||(y[13]=e("p",{class:"text-gray-500 text-sm mt-1"},"Successful bids",-1))]),y[14]||(y[14]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])],-1))])]),e("div",Dv,[e("div",Lv,[e("div",null,[y[15]||(y[15]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Watchlist Items",-1)),e("p",Pv,x(i(r).stats.watchlist_count),1),y[16]||(y[16]=e("p",{class:"text-gray-500 text-sm mt-1"},"Items watching",-1))]),y[17]||(y[17]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))])]),e("div",Ev,[e("div",Vv,[e("div",null,[y[18]||(y[18]=e("p",{class:"text-gray-600 text-sm font-medium uppercase tracking-wide"},"Win Rate",-1)),e("p",Rv,x(i(r).stats.win_rate)+"%",1),y[19]||(y[19]=e("p",{class:"text-gray-500 text-sm mt-1"},"Success rate",-1))]),y[20]||(y[20]=e("div",{class:"bg-gray-100 p-3 rounded-full group-hover:bg-gray-200 transition-colors duration-300"},[e("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])])]),e("div",Fv,[e("div",qv,[e("div",Hv,[e("div",Ov,[e("div",Wv,[y[22]||(y[22]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Active Bids")],-1)),A(P,{to:"/bid-dashboard/active-bids",class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"},{default:O(()=>y[21]||(y[21]=[W(" View all "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:1,__:[21]})])]),e("div",Uv,[i(r).activeBids.length===0?(a(),l("div",Yv,[y[24]||(y[24]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),y[25]||(y[25]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No active bids",-1)),y[26]||(y[26]=e("p",{class:"text-gray-500 mb-4"},"Start bidding on items to see them here.",-1)),A(i(te),{onClick:y[0]||(y[0]=H=>z.$router.push("/")),variant:"primary",size:"sm"},{default:O(()=>y[23]||(y[23]=[W(" Browse Auctions ")])),_:1,__:[23]})])):(a(),l("div",Kv,[A(i(er),{items:N.value,"auto-play":!1,"show-counter":!0,"container-classes":"rounded-lg","navigation-button-classes":"hover:bg-gray-50","dot-classes":"hover:scale-105","key-extractor":H=>H.id},{default:O(({item:H,isActive:K})=>[e("div",Qv,[A(nv,{bid:H,variant:"active",onViewDetails:$,onIncreaseBid:u},null,8,["bid"])])]),_:1},8,["items","key-extractor"]),j.value>0?(a(),l("div",Xv,x(j.value)+" incomplete bid record"+x(j.value>1?"s":"")+" hidden ",1)):L("",!0)]))])])]),e("div",null,[e("div",Gv,[e("div",Zv,[e("div",Jv,[y[28]||(y[28]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Watchlist")],-1)),A(P,{to:"/bid-dashboard/watchlist",class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"},{default:O(()=>y[27]||(y[27]=[W(" View all "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])),_:1,__:[27]})])]),e("div",null,[i(c).isEmpty?(a(),l("div",eg,[y[30]||(y[30]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1)),y[31]||(y[31]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No watched items",-1)),y[32]||(y[32]=e("p",{class:"text-gray-500 mb-4"},"Add items to your watchlist to track them.",-1)),A(i(te),{onClick:y[1]||(y[1]=H=>z.$router.push("/")),variant:"outline",size:"sm"},{default:O(()=>y[29]||(y[29]=[W(" Browse Items ")])),_:1,__:[29]})])):(a(),l("div",tg,[(a(!0),l(ue,null,ce(i(c).items.slice(0,5),H=>(a(),de(yv,{key:H.id,item:H,variant:"compact",onViewDetails:d,onPlaceBid:d,onRemoveFromWatchlist:p},null,8,["item"]))),128))]))])])])]),e("div",sg,[e("div",og,[e("div",ag,[y[34]||(y[34]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"bg-gray-600 p-2 rounded-lg"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])]),e("h2",{class:"text-lg font-semibold text-gray-900"},"Recent Bid History")],-1)),e("div",rg,[e("div",ng,[A(i(he),{modelValue:D.value,"onUpdate:modelValue":y[2]||(y[2]=H=>D.value=H),placeholder:"Search history...",size:"sm",class:"w-48",onInput:V},null,8,["modelValue"]),y[33]||(y[33]=e("svg",{class:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])])]),e("div",lg,[M.value.length===0&&!E.value?(a(),l("div",ig,[y[36]||(y[36]=e("div",{class:"bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4"},[e("svg",{class:"w-8 h-8 text-gray-400 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1)),y[37]||(y[37]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No bid history",-1)),y[38]||(y[38]=e("p",{class:"text-gray-500 mb-4"},"Your completed bids will appear here.",-1)),A(i(te),{onClick:y[3]||(y[3]=H=>z.$router.push("/")),variant:"outline",size:"sm"},{default:O(()=>y[35]||(y[35]=[W(" Start Bidding ")])),_:1,__:[35]})])):(a(),l("div",dg,[A(i(Ks),{columns:_,data:M.value,loading:E.value,bordered:!1,striped:!0,hover:!0,class:"min-h-[400px]"},null,8,["data","loading"]),g.value.last_page>1&&v.value.length>0?(a(),l("div",ug,[A(i(Nt),{"current-page":g.value.current_page,"total-pages":g.value.last_page,total:g.value.total,"per-page":g.value.per_page,"show-info":!0,"show-page-size":!1,onPageChange:k},null,8,["current-page","total-pages","total","per-page"])])):L("",!0)]))])])]))]),_:1})]),_:1})])}}}),cg=()=>{const o=window.location.pathname;return o.startsWith("/spa")?[{path:"/",name:"spa-homepage",component:ot,meta:{title:"Vertigo AMS - Auction Management System",requiresAuth:!1}},{path:"/item/:id",name:"spa-item-detail",component:lt,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}},{path:"/cart",name:"spa-cart",component:at,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}},{path:"/checkout",name:"spa-checkout",component:rt,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}},{path:"/payment-success",name:"spa-payment-success",component:nt,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}},{path:"/register-bid",name:"spa-register-bid",component:Ct,meta:{title:"Register for Auction - Vertigo AMS",requiresAuth:!1}},{path:"/bid-dashboard",name:"spa-bid-dashboard",component:$t,meta:{title:"Bid Dashboard - Vertigo AMS",requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"spa-not-found",component:ot,meta:{title:"Page Not Found - Vertigo AMS",requiresAuth:!1}}]:o==="/cart"?[{path:"/",name:"cart",component:at,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}}]:o==="/checkout"?[{path:"/",name:"checkout",component:rt,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}}]:o==="/payment-success"?[{path:"/",name:"payment-success",component:nt,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}}]:o.startsWith("/item/")?[{path:"/",name:"item-detail",component:lt,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}}]:[{path:"/",name:"homepage",component:ot,meta:{title:"Vertigo AMS - Auction Management System",requiresAuth:!1}},{path:"/item/:id",name:"item-detail",component:lt,meta:{title:"Item Details - Vertigo AMS",requiresAuth:!1}},{path:"/cart",name:"cart",component:at,meta:{title:"Shopping Cart - Vertigo AMS",requiresAuth:!1}},{path:"/checkout",name:"checkout",component:rt,meta:{title:"Secure Checkout - Vertigo AMS",requiresAuth:!1}},{path:"/payment-success",name:"payment-success",component:nt,meta:{title:"Payment Successful - Vertigo AMS",requiresAuth:!1}},{path:"/register-bid",name:"register-bid",component:Ct,meta:{title:"Register for Auction - Vertigo AMS",requiresAuth:!1}},{path:"/bid-dashboard",name:"bid-dashboard",component:$t,meta:{title:"Bid Dashboard - Vertigo AMS",requiresAuth:!0}}]},mg=cg(),vg=()=>{const o=window.location.pathname;return o.startsWith("/spa")?"/spa":o.startsWith("/home-vue")?"/home-vue":o.startsWith("/cart")?"/cart":o.startsWith("/checkout")?"/checkout":o.startsWith("/payment-success")?"/payment-success":o.startsWith("/register-bid")?"/register-bid":(o.startsWith("/item/"),"/")},Ot=es({history:ts(vg()),routes:mg,scrollBehavior(o,s,r){return r||{top:0}}});Ot.beforeEach(async(o,s,r)=>{o.meta.title&&(document.title=o.meta.title);const c=ge();if(!c.user)try{await c.initialize()}catch(h){console.error("Failed to initialize auth:",h)}if(o.meta.requiresAuth){const h=gg();if(console.log("Router guard: isAuthenticated =",h,"user =",c.user,"sessionAuth =",c.sessionAuth),!h&&!c.user)if(window.location.pathname.startsWith("/spa")){console.log("Allowing navigation to continue, will check auth in component"),r();return}else{window.location.href="/login?redirect="+encodeURIComponent(o.fullPath);return}}if(o.meta.requiresAdmin&&!pg()){console.warn("Admin access required for this route"),r("/");return}r()});function gg(){const o=ge(),s=o.sessionAuth||o.user&&!o.token,r=o.token&&o.user;return s||r||o.isAuthenticated}function pg(){var r;const o=ge();if(!o.isAuthenticated)return!1;const s=o.user;return((r=s==null?void 0:s.roles)==null?void 0:r.some(c=>c.name==="admin"))||!1}const hg={template:`
    <div class="min-h-screen bg-gray-50">
      <router-view />
      <NotificationContainer />

      <!-- Global Auth Modal -->
      <AuthModal
        v-model:show="showAuthModal"
        :start-with-register="authModalTab === 'register'"
        title="Welcome to Vertigo AMS"
        subtitle="Your premier auction management system"
        @success="handleAuthSuccess"
        @close="hideAuthModal"
      />
    </div>
  `,setup(){const o=Me(),s=ge(),r=Ne(),c=T(!1),h=T("login"),w=j=>{var v;h.value=((v=j.detail)==null?void 0:v.tab)||"login",c.value=!0},B=()=>{c.value=!1},N=j=>{c.value=!1;const v=(j==null?void 0:j.name)||(j==null?void 0:j.email)||"there";o.success(`Welcome back, ${v}! You have successfully signed in.`);const g=sessionStorage.getItem("auth_redirect");g?(sessionStorage.removeItem("auth_redirect"),r.push(g)):window.location.reload()};return $e(async()=>{window.addEventListener("show-auth-modal",w);try{await s.initialize()}catch(j){console.error("Failed to initialize auth store:",j)}}),vt(()=>{window.removeEventListener("show-auth-modal",w)}),{showAuthModal:c,authModalTab:h,hideAuthModal:B,handleAuthSuccess:N}}},ve=Qt(hg),fg=Xt();ve.use(fg);ve.use(Ot);ve.component("AppLayout",bn);ve.component("Button",te);ve.component("Input",he);ve.component("Card",Fe);ve.component("Modal",St);ve.component("Select",Tt);ve.component("Loading",ze);ve.component("Container",we);ve.component("ItemCard",Rt);ve.component("Banner",Lt);ve.component("Badge",Ve);ve.component("Alert",it);ve.component("Pagination",Nt);ve.component("NotificationContainer",Bt);ve.component("CartDrawer",Ft);ve.component("CartIcon",Fn);ve.component("CartNavIcon",Hn);ve.component("WatchlistNavIcon",da);ve.component("AuthModal",Xe);const ct=ge(),bg=qt(),yg=Ht(),Mt=Ae(),mt=Ue();Me();ct.initialize().then(()=>{ct.isAuthenticated&&mt.initializeWatchlist()});_e(()=>ct.isAuthenticated,async o=>{o?await mt.initializeWatchlist():mt.clearWatchlist()},{immediate:!1});window.branches&&bg.initializeBranches(window.branches);window.adverts&&yg.initializeAdverts(window.adverts);window.cartItems?Mt.initializeCart(window.cartItems):Mt.fetchCart();ve.mount("#app");const xg=async()=>{const o=document.getElementById("watchlist-nav-icon");if(o){const s=ge();o.addEventListener("click",r=>{r.preventDefault(),s.isAuthenticated?window.location.href="/bid-dashboard":window.dispatchEvent(new CustomEvent("show-auth-modal",{detail:{tab:"login"}}))})}};setTimeout(async()=>{await xg()},100);ve.config.errorHandler=(o,s,r)=>{console.error("Vue error:",o,r)};ve.config.warnHandler=(o,s,r)=>{console.warn("Vue warning:",o,r)};
