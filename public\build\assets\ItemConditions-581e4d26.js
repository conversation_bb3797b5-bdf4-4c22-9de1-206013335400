import{h as E,r as t,R as $,L as G,o as T,f as R,v as h,x as C,u as y,F as U,g as L}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as O,h as j}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import"./app-admin-1baa1658.js";import{_ as q}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{A as z}from"./AdminModal-15ebbba8.js";import{u as H}from"./useNotifications-98e2c61c.js";const ee=E({__name:"ItemConditions",setup(J){const r=O(),{showNotification:c}=H(),d=t(!1),u=t(null),l=t([]),m=t([]),o=t(!1),v=t(!1),p=t(null),b=t(1),_=t(1),f=t(0),w=t(25),n=$({search:"",status:"",sort_by:"name",sort_direction:"asc"}),k=[{key:"name",label:"Condition Name",sortable:!0},{key:"description",label:"Description",sortable:!1},{key:"grade",label:"Grade",sortable:!0},{key:"items_count",label:"Items",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"created_at",label:"Created",sortable:!0},{key:"actions",label:"Actions",sortable:!1}],D=[{label:"All Status",value:""},{label:"Active",value:"active"},{label:"Inactive",value:"inactive"}],s=async()=>{d.value=!0,u.value=null;try{await new Promise(e=>setTimeout(e,1e3)),l.value=[{id:1,name:"Mint",description:"Perfect condition, like new",grade:"A+",items_count:12,status:"active",created_at:"2024-01-15"},{id:2,name:"Excellent",description:"Very good condition with minimal wear",grade:"A",items_count:28,status:"active",created_at:"2024-01-10"},{id:3,name:"Good",description:"Good condition with some signs of use",grade:"B",items_count:45,status:"active",created_at:"2024-01-08"},{id:4,name:"Fair",description:"Fair condition with noticeable wear",grade:"C",items_count:23,status:"active",created_at:"2024-01-05"},{id:5,name:"Poor",description:"Poor condition, significant wear or damage",grade:"D",items_count:8,status:"active",created_at:"2024-01-01"}],f.value=l.value.length,_.value=Math.ceil(f.value/w.value)}catch(e){u.value="Failed to load conditions",console.error("Error fetching conditions:",e)}finally{d.value=!1}},g=()=>{s()},A=()=>{r.push("/admin-spa/items/conditions/create")},P=e=>{r.push(`/admin-spa/items/conditions/view/${e.id}`)},V=e=>{r.push(`/admin-spa/items/conditions/edit/${e.id}`)},F=e=>{p.value=e,o.value=!0},B=async()=>{if(p.value){v.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),c({type:"success",title:"Condition Deleted",message:"Condition has been successfully deleted."}),await s()}catch{c({type:"error",title:"Delete Failed",message:"Failed to delete condition. Please try again."})}finally{v.value=!1,o.value=!1,p.value=null}}},S=e=>{n.search=e,g()},x=(e,a)=>{n.sort_by=e,n.sort_direction=a,g()},I=e=>{b.value=e,s()},M=e=>{m.value=e?[...l.value]:[]},N=async()=>{m.value.length!==0&&c({type:"info",title:"Bulk Delete",message:"Bulk delete functionality will be implemented soon."})};return G(()=>{s()}),(e,a)=>(T(),R(U,null,[h(y(q),{title:"Item Conditions",subtitle:"Manage item condition standards",loading:d.value,error:u.value,items:l.value,columns:k,"selected-items":m.value,"show-bulk-actions":!0,"current-page":b.value,"total-pages":_.value,"total-items":f.value,"per-page":w.value,"create-button-text":"Add Condition","empty-state-title":"No conditions found","empty-state-message":"Get started by adding your first item condition.",onCreate:A,onSearch:S,onSort:x,onPageChange:I,onSelectAll:M,onBulkDelete:N,onView:P,onEdit:V,onDelete:F,onRefresh:s},{filters:C(()=>[h(y(j),{modelValue:n.status,"onUpdate:modelValue":a[0]||(a[0]=i=>n.status=i),options:D,placeholder:"Filter by status",class:"w-48",onChange:g},null,8,["modelValue"])]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]),h(y(z),{modelValue:o.value,"onUpdate:modelValue":a[1]||(a[1]=i=>o.value=i),title:"Delete Condition",loading:v.value,onConfirm:B,onCancel:a[2]||(a[2]=i=>o.value=!1)},{default:C(()=>a[3]||(a[3]=[L("p",{class:"text-gray-600"}," Are you sure you want to delete this condition? This action cannot be undone. ",-1)])),_:1,__:[3]},8,["modelValue","loading"])],64))}});export{ee as default};
