import{h as i,i as m,o as p,f as v,v as c,a0 as f,b as B}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";const V={class:"form-field"},F=i({__name:"FormField",props:{modelValue:{},error:{},validateOnBlur:{type:Boolean,default:!0},validator:{},type:{},placeholder:{},disabled:{type:Boolean},required:{type:Boolean}},emits:["update:modelValue","update:error","blur","focus"],setup(t,{emit:d}){const r=t,a=d,u=m(()=>{const{modelValue:e,error:o,validateOnBlur:l,validator:b,...n}=r;return n}),s=e=>{if(r.validateOnBlur&&r.validator){const o=r.validator(r.modelValue||"");a("update:error",o)}a("blur",e)};return(e,o)=>(p(),v("div",V,[c(B,f(u.value,{"model-value":e.modelValue,error:e.error,"onUpdate:modelValue":o[0]||(o[0]=l=>e.$emit("update:modelValue",l)),onBlur:s,onFocus:o[1]||(o[1]=l=>e.$emit("focus",l))}),null,16,["model-value","error"])]))}});export{F as _};
