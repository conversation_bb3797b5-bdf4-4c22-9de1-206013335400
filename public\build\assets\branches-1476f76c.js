import{s as F,r as u,i as p}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{a as i}from"./axios-917b1704.js";const T=F("adminBranches",()=>{const c=u([]),r=u(null),o=u(null),d=u(!1),s=u(null),h=u(!1),f=u(!1),v=u(!1),w=p(()=>{var a;return((a=r.value)==null?void 0:a.total)||0}),y=p(()=>{var a;return((a=r.value)==null?void 0:a.current_page)||1}),B=p(()=>{var a;return((a=r.value)==null?void 0:a.last_page)||1}),S=p(()=>y.value<B.value),x=p(()=>{var a;return((a=r.value)==null?void 0:a.data)||c.value}),m=p(()=>c.value.filter(a=>a.is_active!==!1));return{branches:c,paginatedBranches:r,currentBranch:o,loading:d,error:s,creating:h,updating:f,deleting:v,totalBranches:w,currentPage:y,lastPage:B,hasMorePages:S,branchesList:x,activeBranches:m,fetchBranches:async(a={})=>{var n,l;d.value=!0,s.value=null;try{const e=new URLSearchParams;a.search&&e.append("search",a.search),a.is_active!==void 0&&e.append("is_active",a.is_active.toString()),a.page&&e.append("page",a.page.toString()),a.per_page&&e.append("per_page",a.per_page.toString()),a.sort_by&&e.append("sort_by",a.sort_by),a.sort_order&&e.append("sort_order",a.sort_order);let t;try{t=await i.get(`/ajax-branches?${e.toString()}`),c.value=t.data}catch{t=await i.get(`/api/branches?${e.toString()}`),a.page||a.per_page?r.value=t.data:c.value=t.data.data||t.data}}catch(e){s.value=((l=(n=e.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to fetch branches",console.error("Error fetching branches:",e)}finally{d.value=!1}},fetchBranch:async a=>{var n,l;d.value=!0,s.value=null;try{const e=await i.get(`/api/branches/${a}`);return o.value=e.data.data,e.data.data}catch(e){throw s.value=((l=(n=e.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to fetch branch",console.error("Error fetching branch:",e),e}finally{d.value=!1}},createBranch:async a=>{var n,l;h.value=!0,s.value=null;try{const e=await i.post("/api/branches",a);return c.value.unshift(e.data.data),r.value&&(r.value.data.unshift(e.data.data),r.value.total+=1),e.data.data}catch(e){throw s.value=((l=(n=e.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to create branch",console.error("Error creating branch:",e),e}finally{h.value=!1}},updateBranch:async a=>{var n,l,e;f.value=!0,s.value=null;try{const g=(await i.put(`/api/branches/${a.id}`,a)).data.data,_=c.value.findIndex(b=>b.id===a.id);if(_!==-1&&(c.value[_]=g),r.value){const b=r.value.data.findIndex(E=>E.id===a.id);b!==-1&&(r.value.data[b]=g)}return((n=o.value)==null?void 0:n.id)===a.id&&(o.value=g),g}catch(t){throw s.value=((e=(l=t.response)==null?void 0:l.data)==null?void 0:e.message)||"Failed to update branch",console.error("Error updating branch:",t),t}finally{f.value=!1}},deleteBranch:async a=>{var n,l,e;v.value=!0,s.value=null;try{await i.delete(`/api/branches/${a}`),c.value=c.value.filter(t=>t.id!==a),r.value&&(r.value.data=r.value.data.filter(t=>t.id!==a),r.value.total-=1),((n=o.value)==null?void 0:n.id)===a&&(o.value=null)}catch(t){throw s.value=((e=(l=t.response)==null?void 0:l.data)==null?void 0:e.message)||"Failed to delete branch",console.error("Error deleting branch:",t),t}finally{v.value=!1}},bulkDeleteBranches:async a=>{var n,l;v.value=!0,s.value=null;try{await i.post("/api/branches/bulk-delete",{branch_ids:a.map(t=>parseInt(t))});const e=a.map(t=>parseInt(t));c.value=c.value.filter(t=>!e.includes(t.id)),r.value&&(r.value.data=r.value.data.filter(t=>!e.includes(t.id)),r.value.total-=a.length)}catch(e){throw s.value=((l=(n=e.response)==null?void 0:n.data)==null?void 0:l.message)||"Failed to delete branches",console.error("Error bulk deleting branches:",e),e}finally{v.value=!1}},getBranchOptions:()=>m.value.map(a=>({label:a.name,value:a.id.toString()})),resetState:()=>{c.value=[],r.value=null,o.value=null,s.value=null,d.value=!1,h.value=!1,f.value=!1,v.value=!1}}});export{T as u};
