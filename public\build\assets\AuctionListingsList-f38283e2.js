import{h as ne,r as le,R as ie,i as g,L as re,o as y,m as A,x as a,u as l,v as m,g as o,t as i,H as p,j as v,f as h,_ as f}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as ce,h as w}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{_ as U}from"./FormField.vue_vue_type_script_setup_true_lang-529d40d6.js";import{d as ue}from"./app-admin-1baa1658.js";import{_ as de}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{A as L}from"./AdminBadge-74cb3994.js";import{u as me}from"./auctionTypes-eeea8f1c.js";import{u as pe}from"./branches-1476f76c.js";import{u as ge}from"./useNotifications-98e2c61c.js";import"./axios-917b1704.js";const ye={class:"flex items-center space-x-3"},_e=["src","alt","onClick"],fe={class:"min-w-0 flex-1"},he={class:"text-sm font-medium text-gray-900 truncate"},ve={class:"text-sm text-gray-500 truncate"},xe={class:"text-xs text-gray-400 truncate"},be={class:"min-w-0"},Ae={class:"text-sm font-medium text-gray-900 truncate"},ke={class:"flex items-center space-x-2 mt-1"},we={key:0,class:"text-xs text-gray-500 truncate mt-1"},Ce={class:"text-sm"},Se={class:"font-medium text-gray-900"},Ve={class:"text-gray-600"},$e={key:0,class:"text-green-600 text-xs"},Te={class:"text-xs text-gray-500"},Be={key:0},Ne={key:1},De={key:2,class:"text-gray-400"},ze={key:0,class:"text-sm"},Fe={class:"font-medium text-gray-900"},Pe={class:"text-gray-500"},Ie={key:1,class:"text-sm text-gray-400"},Re={class:"flex justify-end gap-2"},We=ne({__name:"AuctionListingsList",setup(Ue){const r=ue(),C=me(),S=pe(),b=ce(),{showNotification:c}=ge(),n=le([]),u=ie({status:"",auction_type_id:"",branch_id:"",date_from:"",date_to:"",search:""}),V=g(()=>r.auctionsList||[]),j=g(()=>r.loading),E=g(()=>r.error),$=g(()=>r.currentPage),O=g(()=>r.lastPage),M=g(()=>r.totalAuctions),T=g(()=>{var t;return((t=r.auctions)==null?void 0:t.per_page)||20}),G=g(()=>[{key:"item",label:"Item",sortable:!1},{key:"auction_details",label:"Auction Details",sortable:!0},{key:"bidding",label:"Bidding Info",sortable:!1},{key:"dates",label:"Schedule",sortable:!0},{key:"status",label:"Status",sortable:!1},{key:"owner",label:"Owner",sortable:!1}]),H=g(()=>[{label:"All Status",value:""},{label:"Active",value:"active"},{label:"Closed",value:"closed"}]),q=g(()=>[{label:"All Types",value:""},...C.auctionTypes.map(t=>({label:t.name,value:t.id.toString()}))]),J=g(()=>[{label:"All Branches",value:""},...S.branches.map(t=>({label:t.name,value:t.id.toString()}))]),d=async()=>{await r.fetchAuctions({...u,page:$.value,per_page:T.value})},x=()=>{d()},K=()=>{b.push("/admin-spa/auction-listings/create")},B=t=>{b.push(`/admin-spa/auction-listings/view/${t.id}`)},N=t=>{b.push(`/admin-spa/auction-listings/edit/${t.id}`)},D=async t=>{if(confirm("Are you sure you want to delete this auction listing?"))try{await r.deleteAuction(t.id),c("Auction listing deleted successfully","success"),await d()}catch{c("Failed to delete auction listing","error")}},Q=async t=>{if(confirm("Are you sure you want to close this auction?"))try{await r.closeAuction(t.id),c("Auction closed successfully","success"),await d()}catch{c("Failed to close auction","error")}},W=async t=>{if(confirm("Are you sure you want to reopen this auction?"))try{await r.reopenAuction(t.id),c("Auction reopened successfully","success"),await d()}catch{c("Failed to reopen auction","error")}},z=async()=>{if(n.value.length!==0&&confirm(`Are you sure you want to delete ${n.value.length} auction listings?`))try{await r.bulkDeleteAuctions(n.value),c(`${n.value.length} auction listings deleted successfully`,"success"),n.value=[],await d()}catch{c("Failed to delete auction listings","error")}},X=async()=>{if(n.value.length!==0&&confirm(`Are you sure you want to close ${n.value.length} auctions?`))try{c(`${n.value.length} auctions closed successfully`,"success"),n.value=[],await d()}catch{c("Failed to close auctions","error")}},Y=async()=>{if(n.value.length!==0&&confirm(`Are you sure you want to reopen ${n.value.length} auctions?`))try{c(`${n.value.length} auctions reopened successfully`,"success"),n.value=[],await d()}catch{c("Failed to reopen auctions","error")}},Z=t=>{u.search=t,d()},ee=(t,s)=>{d()},te=t=>{d()},se=t=>{t?n.value=V.value.map(s=>s.id.toString()):n.value=[]},ae=t=>{t!=null&&t.id&&b.push(`/admin-spa/items/view/${t.id}`)},oe=t=>{switch(t){case"live":return"success";case"online":return"primary";case"cash":return"warning";default:return"secondary"}},k=t=>t?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t):"$0.00",F=t=>t?new Date(t).toLocaleString():"N/A";return re(async()=>{await Promise.all([d(),C.fetchAuctionTypes(),S.fetchBranches()])}),(t,s)=>(y(),A(l(de),{title:"Auction Listings",subtitle:"Manage auction listings and bidding sessions",loading:j.value,error:E.value,items:V.value,columns:G.value,"selected-items":n.value,"show-bulk-actions":!0,"current-page":$.value,"total-pages":O.value,"total-items":M.value,"per-page":T.value,"create-button-text":"Create Auction","empty-state-title":"No auction listings found","empty-state-message":"Get started by creating your first auction listing.",onCreate:K,onSearch:Z,onSort:ee,onPageChange:te,onSelectAll:se,onBulkDelete:z,onView:B,onEdit:N,onDelete:D,onRefresh:d},{filters:a(()=>[m(l(w),{modelValue:u.status,"onUpdate:modelValue":s[0]||(s[0]=e=>u.status=e),placeholder:"All Status",options:H.value,onChange:x},null,8,["modelValue","options"]),m(l(w),{modelValue:u.auction_type_id,"onUpdate:modelValue":s[1]||(s[1]=e=>u.auction_type_id=e),placeholder:"All Types",options:q.value,onChange:x},null,8,["modelValue","options"]),m(l(w),{modelValue:u.branch_id,"onUpdate:modelValue":s[2]||(s[2]=e=>u.branch_id=e),placeholder:"All Branches",options:J.value,onChange:x},null,8,["modelValue","options"]),m(l(U),{modelValue:u.date_from,"onUpdate:modelValue":s[3]||(s[3]=e=>u.date_from=e),type:"date",placeholder:"From Date",onChange:x},null,8,["modelValue"]),m(l(U),{modelValue:u.date_to,"onUpdate:modelValue":s[4]||(s[4]=e=>u.date_to=e),type:"date",placeholder:"To Date",onChange:x},null,8,["modelValue"])]),"cell-item":a(({item:e})=>{var _,P,I,R;return[o("div",ye,[o("img",{src:((_=e.item)==null?void 0:_.image)||"/img/product.jpeg",alt:((P=e.item)==null?void 0:P.name)||"No item",class:"w-12 h-12 rounded-lg object-cover cursor-pointer hover:opacity-75 transition-opacity",onClick:Le=>ae(e.item)},null,8,_e),o("div",fe,[o("p",he,i(((I=e.item)==null?void 0:I.name)||"No item assigned"),1),o("p",ve," Ref: "+i(((R=e.item)==null?void 0:R.reference_number)||"N/A"),1),o("p",xe," Code: "+i(e.code||"N/A"),1)])])]}),"cell-auction_details":a(({item:e})=>[o("div",be,[o("p",Ae,i(e.name||"Unnamed Auction"),1),o("div",ke,[e.auctionType?(y(),A(l(L),{key:0,variant:oe(e.auctionType.type),size:"sm"},{default:a(()=>[p(i(e.auctionType.name),1)]),_:2},1032,["variant"])):v("",!0)]),e.description?(y(),h("p",we,i(e.description),1)):v("",!0)])]),"cell-bidding":a(({item:e})=>{var _;return[o("div",Ce,[o("p",Se," Current: "+i(k(e.bid_amount)),1),o("p",Ve," Target: "+i(k((_=e.item)==null?void 0:_.target_amount)),1),e.initial_payment?(y(),h("p",$e," Paid: "+i(k(e.initial_payment)),1)):v("",!0)])]}),"cell-dates":a(({item:e})=>[o("div",Te,[e.date_from?(y(),h("p",Be,[s[5]||(s[5]=o("span",{class:"font-medium"},"From:",-1)),p(" "+i(F(e.date_from)),1)])):v("",!0),e.date_to?(y(),h("p",Ne,[s[6]||(s[6]=o("span",{class:"font-medium"},"To:",-1)),p(" "+i(F(e.date_to)),1)])):v("",!0),!e.date_from&&!e.date_to?(y(),h("p",De," No dates set ")):v("",!0)])]),"cell-status":a(({item:e})=>[m(l(L),{variant:e.closed_by?"success":"warning",size:"sm"},{default:a(()=>[p(i(e.closed_by?"Closed":"Active"),1)]),_:2},1032,["variant"])]),"cell-owner":a(({item:e})=>[e.user?(y(),h("div",ze,[o("p",Fe,i(e.user.name),1),o("p",Pe,i(e.user.email),1)])):(y(),h("span",Ie,"No owner"))]),actions:a(({item:e})=>[o("div",Re,[m(l(f),{variant:"ghost",size:"sm",onClick:_=>B(e),class:"text-blue-600 hover:text-blue-700"},{default:a(()=>s[7]||(s[7]=[p(" View ")])),_:2,__:[7]},1032,["onClick"]),m(l(f),{variant:"ghost",size:"sm",onClick:_=>N(e),class:"text-green-600 hover:text-green-700"},{default:a(()=>s[8]||(s[8]=[p(" Edit ")])),_:2,__:[8]},1032,["onClick"]),e.closed_by?(y(),A(l(f),{key:1,variant:"ghost",size:"sm",onClick:_=>W(e),class:"text-blue-600 hover:text-blue-700"},{default:a(()=>s[10]||(s[10]=[p(" Reopen ")])),_:2,__:[10]},1032,["onClick"])):(y(),A(l(f),{key:0,variant:"ghost",size:"sm",onClick:_=>Q(e),class:"text-orange-600 hover:text-orange-700"},{default:a(()=>s[9]||(s[9]=[p(" Close ")])),_:2,__:[9]},1032,["onClick"])),m(l(f),{variant:"ghost",size:"sm",onClick:_=>D(e),class:"text-red-600 hover:text-red-700"},{default:a(()=>s[11]||(s[11]=[p(" Delete ")])),_:2,__:[11]},1032,["onClick"])])]),"bulk-actions":a(({selectedItems:e})=>[m(l(f),{variant:"outline",size:"sm",onClick:X,class:"text-orange-600 hover:text-orange-700"},{default:a(()=>s[12]||(s[12]=[p(" Close Selected ")])),_:1,__:[12]}),m(l(f),{variant:"outline",size:"sm",onClick:Y,class:"text-blue-600 hover:text-blue-700"},{default:a(()=>s[13]||(s[13]=[p(" Reopen Selected ")])),_:1,__:[13]}),m(l(f),{variant:"outline",size:"sm",onClick:z,class:"text-red-600 hover:text-red-700"},{default:a(()=>s[14]||(s[14]=[p(" Delete Selected ")])),_:1,__:[14]})]),_:1},8,["loading","error","items","columns","selected-items","current-page","total-pages","total-items","per-page"]))}});export{We as default};
