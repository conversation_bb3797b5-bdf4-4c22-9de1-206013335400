import{h as R,r as p,L as B,o as r,f as a,g as t,n as h,H as y,t as n,C as x,Q as g,p as f,F as V,k as F,j as H}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{d as A}from"./export-37598506.js";const P={class:"space-y-6"},S={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},z={class:"flex items-center justify-between mb-6"},N={class:"flex items-center space-x-3"},E=["disabled"],L={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},q={class:"bg-green-50 border border-green-200 rounded-lg p-4"},U={class:"flex items-center"},$={class:"ml-4"},X={class:"text-2xl font-semibold text-green-900"},W={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},G={class:"flex items-center"},I={class:"ml-4"},Q={class:"text-2xl font-semibold text-blue-900"},Y={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},J={class:"flex items-center"},K={class:"ml-4"},O={class:"text-2xl font-semibold text-yellow-900"},Z={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},tt={class:"flex items-center"},et={class:"ml-4"},st={class:"text-2xl font-semibold text-purple-900"},ot={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},nt={key:0,class:"flex items-center justify-center py-12"},rt={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-6"},at={class:"flex items-center"},it={class:"text-red-800"},lt={key:2,class:"overflow-x-auto"},dt={class:"min-w-full divide-y divide-gray-200"},ut={class:"bg-white divide-y divide-gray-200"},pt={class:"px-6 py-4 whitespace-nowrap"},ct={class:"text-sm font-medium text-gray-900"},mt={class:"text-sm text-gray-500"},xt={class:"px-6 py-4 whitespace-nowrap"},gt={class:"text-sm font-medium text-gray-900"},ft={class:"text-sm text-gray-500"},vt={class:"px-6 py-4 whitespace-nowrap"},ht={class:"text-sm font-medium text-gray-900"},yt={class:"px-6 py-4 whitespace-nowrap"},bt={class:"text-sm text-gray-900"},wt={class:"px-6 py-4 whitespace-nowrap"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},_t={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ct=["onClick"],Dt=["onClick"],Mt={key:3,class:"text-center py-12"},Bt=R({__name:"DepositsReport",setup(Tt){const l=p(!1),c=p(""),d=p([]),u=p({totalDeposits:0,confirmedDeposits:0,pendingDeposits:0,totalAmount:0}),i=p({dateRange:"month",status:"",paymentMethod:""}),m=async()=>{l.value=!0,c.value="";try{const s=await fetch("/api/admin/reports/deposits",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const e=await s.json();if(e.success)d.value=e.deposits||[],u.value=e.summary||{totalDeposits:0,confirmedDeposits:0,pendingDeposits:0,totalAmount:0};else throw new Error(e.error||"Failed to fetch deposits data")}catch(s){c.value=s.message||"Failed to load deposits data",console.error("Deposits report error:",s)}finally{l.value=!1}},b=async()=>{try{const s=await fetch("/api/admin/reports/branches",{method:"GET",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"same-origin"});if(s.ok){const e=await s.json();e.success&&(branches.value=e.branches||[])}}catch(s){console.error("Failed to load branches:",s)}},w=()=>{m()},k=()=>{if(d.value.length===0){alert("No data to export");return}A(d.value,"csv")},_=s=>{console.log("Viewing deposit:",s)},C=s=>{console.log("Confirming deposit:",s)},v=s=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(s),D=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),M=s=>({credit_card:"Credit Card",bank_transfer:"Bank Transfer",paypal:"PayPal",cash:"Cash"})[s]||s,T=s=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800",refunded:"bg-blue-100 text-blue-800"})[s]||"bg-gray-100 text-gray-800";return B(()=>{m(),b()}),(s,e)=>(r(),a("div",P,[t("div",S,[t("div",z,[e[6]||(e[6]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"Deposits Report"),t("p",{class:"text-gray-600 mt-1"},"Track deposit transactions and balances")],-1)),t("div",N,[t("button",{onClick:m,disabled:l.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[(r(),a("svg",{class:h(["w-4 h-4 mr-2",{"animate-spin":l.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[3]||(e[3]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),e[4]||(e[4]=y(" Refresh "))],8,E),t("button",{onClick:k,class:"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},e[5]||(e[5]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),y(" Export ")]))])]),t("div",L,[t("div",q,[t("div",U,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",$,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-green-600"},"Total Deposits",-1)),t("p",X,n(u.value.totalDeposits),1)])])]),t("div",W,[t("div",G,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",I,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-blue-600"},"Confirmed",-1)),t("p",Q,n(u.value.confirmedDeposits),1)])])]),t("div",Y,[t("div",J,[e[12]||(e[12]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",K,[e[11]||(e[11]=t("p",{class:"text-sm font-medium text-yellow-600"},"Pending",-1)),t("p",O,n(u.value.pendingDeposits),1)])])]),t("div",Z,[t("div",tt,[e[14]||(e[14]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),t("div",et,[e[13]||(e[13]=t("p",{class:"text-sm font-medium text-purple-600"},"Total Amount",-1)),t("p",st,"$"+n(v(u.value.totalAmount)),1)])])])]),t("div",ot,[t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),x(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>i.value.dateRange=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},e[15]||(e[15]=[f('<option value="today">Today</option><option value="week">This Week</option><option value="month">This Month</option><option value="quarter">This Quarter</option><option value="year">This Year</option><option value="custom">Custom Range</option>',6)]),512),[[g,i.value.dateRange]])]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),x(t("select",{"onUpdate:modelValue":e[1]||(e[1]=o=>i.value.status=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},e[17]||(e[17]=[f('<option value="">All Statuses</option><option value="pending">Pending</option><option value="confirmed">Confirmed</option><option value="failed">Failed</option><option value="refunded">Refunded</option>',5)]),512),[[g,i.value.status]])]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Payment Method",-1)),x(t("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>i.value.paymentMethod=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"},e[19]||(e[19]=[f('<option value="">All Methods</option><option value="credit_card">Credit Card</option><option value="bank_transfer">Bank Transfer</option><option value="paypal">PayPal</option><option value="cash">Cash</option>',5)]),512),[[g,i.value.paymentMethod]])]),t("div",{class:"flex items-end"},[t("button",{onClick:w,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Apply Filters ")])]),l.value?(r(),a("div",nt,e[21]||(e[21]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),t("span",{class:"ml-2 text-gray-600"},"Loading deposits data...",-1)]))):c.value?(r(),a("div",rt,[t("div",at,[e[22]||(e[22]=t("svg",{class:"h-5 w-5 text-red-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),t("span",it,n(c.value),1)])])):d.value.length>0?(r(),a("div",lt,[t("table",dt,[e[23]||(e[23]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Transaction ID"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Customer"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Amount"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Payment Method"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),t("tbody",ut,[(r(!0),a(V,null,F(d.value,o=>(r(),a("tr",{key:o.id,class:"hover:bg-gray-50"},[t("td",pt,[t("div",ct,"#"+n(o.transaction_id),1),t("div",mt,n(o.reference_number),1)]),t("td",xt,[t("div",gt,n(o.customer_name),1),t("div",ft,n(o.customer_email),1)]),t("td",vt,[t("div",ht,"$"+n(v(o.amount)),1)]),t("td",yt,[t("div",bt,n(M(o.payment_method)),1)]),t("td",wt,[t("span",{class:h([T(o.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(o.status),3)]),t("td",kt,n(D(o.created_at)),1),t("td",_t,[t("button",{onClick:j=>_(o),class:"text-blue-600 hover:text-blue-900 mr-3"}," View ",8,Ct),o.status==="pending"?(r(),a("button",{key:0,onClick:j=>C(o),class:"text-green-600 hover:text-green-900"}," Confirm ",8,Dt)):H("",!0)])]))),128))])])])):(r(),a("div",Mt,e[24]||(e[24]=[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})],-1),t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No deposits found",-1),t("p",{class:"mt-1 text-sm text-gray-500"},"No deposit transactions match the current filters.",-1)])))])]))}});export{Bt as default};
