import{h as q,r,R as J,i as K,L as Q,o as v,m as W,x as l,u as i,v as d,f as w,k as X,g as s,t as h,j as Y,H as V,_ as x,F as Z}from"./Modal.vue_vue_type_script_setup_true_lang-a410f5b9.js";import{b as T,h as ee,e as N}from"./Container.vue_vue_type_script_setup_true_lang-1dca7823.js";import{b as M}from"./app-admin-1baa1658.js";import{_ as te}from"./AdminListTemplate.vue_vue_type_script_setup_true_lang-3ebf2e02.js";import{u as ae}from"./useNotifications-98e2c61c.js";const se={class:"px-6 py-4"},oe={class:"min-w-0 flex-1"},re={class:"text-sm font-medium text-gray-900 truncate"},ne=["href"],le={key:0,class:"text-sm text-gray-500 truncate"},ie={class:"px-6 py-4 whitespace-nowrap"},ce={class:"px-6 py-4 whitespace-nowrap text-center"},ue={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},de={class:"px-6 py-4 whitespace-nowrap text-center text-sm font-medium"},pe={class:"flex justify-center space-x-2"},_e=q({__name:"AuctionCategoriesList",setup(me){const f=T(),{showNotification:p}=ae(),y=r(!1),b=r(null),c=r([]),m=r(1),C=r(20),A=r(0),$=r(1),_=r([]),g=J({type:"",search:""}),j=[{key:"name",label:"Name",sortable:!0},{key:"type",label:"Type",sortable:!0},{key:"items_count",label:"Available Items",sortable:!1},{key:"created_by",label:"Created By",sortable:!0}],z=K(()=>[{label:"All Types",value:""},{label:"Online",value:"online"},{label:"Cash",value:"cash"}]),u=async()=>{var e,t,o;y.value=!0;try{const a={page:m.value.toString(),per_page:C.value.toString(),...g},n=await M.get("/auction-types",a);_.value=n.data,A.value=((e=n.meta)==null?void 0:e.total)||0,$.value=((t=n.meta)==null?void 0:t.last_page)||1,m.value=((o=n.meta)==null?void 0:o.current_page)||1}catch(a){b.value="Failed to fetch auction categories",console.error("Error fetching auction categories:",a)}finally{y.value=!1}},S=()=>{m.value=1,u()},F=()=>{f.push("/admin-spa/auction-types/create")},L=e=>{g.search=e,S()},P=(e,t)=>{console.log("Sort:",e,t)},D=e=>{m.value=e,u()},H=e=>{c.value=e?_.value.map(t=>t.id.toString()):[]},I=e=>{f.push(`/admin-spa/auction-types/edit/${e.id}`)},E=e=>{f.push(`/admin-spa/auction-types/view/${e.id}`)},R=async e=>{if(k(e)>0){p("Cannot delete auction category with active items","error");return}if(confirm("Are you sure you want to delete this auction category?"))try{await M.delete(`/auction-types/${e.id}`),p("Auction category deleted successfully","success"),await u()}catch{p("Failed to delete auction category","error")}},O=async()=>{if(c.value.length!==0&&confirm(`Are you sure you want to delete ${c.value.length} auction categories?`))try{p(`${c.value.length} auction categories deleted successfully`,"success"),c.value=[],await u()}catch{p("Failed to delete auction categories","error")}},k=e=>{var t;return((t=e.items)==null?void 0:t.filter(o=>!o.closed_by).length)||0},U=e=>e.charAt(0).toUpperCase()+e.slice(1),G=e=>{switch(e){case"online":return"success";case"cash":return"warning";default:return"default"}};return Q(()=>{u()}),(e,t)=>(v(),W(i(te),{title:"Auction Categories",subtitle:"Manage auction categories and types",loading:y.value,error:b.value,items:_.value,columns:j,"selected-items":c.value,"show-bulk-actions":!0,"current-page":m.value,"total-pages":$.value,"total-items":A.value,"per-page":C.value,"create-button-text":"Add Auction Category","empty-state-title":"No auction categories found","empty-state-message":"Get started by adding your first auction category.",onCreate:F,onSearch:L,onSort:P,onPageChange:D,onSelectAll:H,onBulkDelete:O,onRefresh:u},{filters:l(()=>[d(i(ee),{modelValue:g.type,"onUpdate:modelValue":t[0]||(t[0]=o=>g.type=o),placeholder:"All Types",options:z.value,onChange:S},null,8,["modelValue","options"])]),rows:l(({items:o})=>[(v(!0),w(Z,null,X(o,a=>{var n;return v(),w("tr",{key:a.id,class:"hover:bg-gray-50"},[s("td",se,[s("div",oe,[s("p",re,[s("a",{href:`/auction-types/${a.id}`,class:"hover:text-blue-600"},h(a.name),9,ne)]),a.description?(v(),w("p",le,h(a.description),1)):Y("",!0)])]),s("td",ie,[d(i(N),{variant:G(a.type)},{default:l(()=>[V(h(U(a.type)),1)]),_:2},1032,["variant"])]),s("td",ce,[d(i(N),{variant:"primary"},{default:l(()=>[V(h(k(a)),1)]),_:2},1024)]),s("td",ue,h(((n=a.created_by)==null?void 0:n.name)||"-"),1),s("td",de,[s("div",pe,[d(i(x),{size:"sm",variant:"outline",onClick:B=>I(a)},{default:l(()=>t[1]||(t[1]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)])),_:2,__:[1]},1032,["onClick"]),d(i(x),{size:"sm",variant:"info",onClick:B=>E(a)},{default:l(()=>t[2]||(t[2]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[2]},1032,["onClick"]),d(i(x),{size:"sm",variant:"danger",onClick:B=>R(a),disabled:k(a)>0},{default:l(()=>t[3]||(t[3]=[s("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)])),_:2,__:[3]},1032,["onClick","disabled"])])])])}),128))]),_:1},8,["loading","error","items","selected-items","current-page","total-pages","total-items","per-page"]))}});export{_e as default};
