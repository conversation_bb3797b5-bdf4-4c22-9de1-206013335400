<template>
  <AdminListTemplate
    title="Orders"
    subtitle="Manage customer orders and transactions"
    :loading="loading"
    :error="error"
    :items="orders"
    :columns="columns"
    :show-create-button="true"
    create-button-text="New Order"
    create-route="/admin-spa/sales/orders/create"
    empty-state-title="No orders found"
    empty-state-message="Orders will appear here when customers make purchases."
    :show-pagination="true"
    :current-page="currentPage"
    :total-pages="totalPages"
    :total-items="totalItems"
    @create="handleCreate"
    @search="handleSearch"
    @sort="handleSort"
    @page-change="handlePageChange"
  >
    <template #actions>
      <div class="flex space-x-3">
        <Button variant="outline" size="sm" @click="exportOrders">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
          </svg>
          Export
        </Button>
        <Button variant="primary" size="sm" @click="handleCreate">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          New Order
        </Button>
      </div>
    </template>

    <template #filters>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Select
          v-model="filters.status"
          placeholder="Filter by status"
          :options="statusOptions"
          @change="applyFilters"
        />
        <Select
          v-model="filters.dateRange"
          placeholder="Date range"
          :options="dateRangeOptions"
          @change="applyFilters"
        />
        <Input
          v-model="filters.customer"
          placeholder="Search customer..."
          @input="applyFilters"
        />
        <Input
          v-model="filters.orderId"
          placeholder="Order ID..."
          @input="applyFilters"
        />
      </div>
    </template>

    <template #rows="{ items }">
      <tr v-for="order in items" :key="order.id" class="hover:bg-gray-50">
        <!-- Order ID -->
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          {{ order.order_id }}
        </td>

        <!-- Customer -->
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">{{ order.customer }}</div>
          <div class="text-sm text-gray-500">{{ order.customer_email }}</div>
        </td>

        <!-- Items -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {{ order.sales_count }} item{{ order.sales_count === 1 ? '' : 's' }}
        </td>

        <!-- Amount -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          <div class="font-medium">${{ formatCurrency(order.amount_total) }}</div>
          <div v-if="order.discount > 0" class="text-xs text-green-600">
            -${{ formatCurrency(order.discount) }} discount
          </div>
        </td>

        <!-- Status -->
        <td class="px-6 py-4 whitespace-nowrap">
          <span :class="getStatusClass(order.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
            {{ order.status }}
          </span>
        </td>

        <!-- Date -->
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {{ formatDate(order.created_at) }}
        </td>

        <!-- Actions -->
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex justify-end space-x-2">
            <Button variant="ghost" size="sm" @click="viewOrder(order)">
              View
            </Button>
            <Button variant="ghost" size="sm" @click="editOrder(order)">
              Edit
            </Button>
            <Button
              v-if="order.status_id !== 11"
              variant="ghost"
              size="sm"
              @click="processOrder(order)"
            >
              Process
            </Button>
          </div>
        </td>
      </tr>
    </template>
  </AdminListTemplate>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { AdminListTemplate } from '@/components/admin';
import { Button, Select, Input } from '@/components/ui';
import { useApi } from '@/composables/useApi';

interface Order {
  id: number;
  order_id: string;
  customer: string;
  customer_email: string;
  customer_id: number;
  description: string;
  sub_total: number;
  amount_total: number;
  amount_paid: number;
  discount: number;
  vat: number;
  status: string;
  status_id: number;
  prepared_by: string;
  approved_by_name: string;
  branch: string;
  branch_id: number;
  sales_count: number;
  payments_count: number;
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// API setup
const { data: apiData, isLoading: loading, error: apiError, execute: fetchOrders } = useApi<any>('/admin/orders');

// State
const error = computed(() => apiError.value?.message || null);
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const perPage = ref(20);

// Filters
const filters = ref({
  status: '',
  dateRange: '',
  customer: '',
  orderId: ''
});

// Options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: '1', label: 'Active' },
  { value: '11', label: 'Completed' },
  { value: '13', label: 'Cancelled' }
];

const dateRangeOptions = [
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
];

// Table columns
const columns = [
  { key: 'order_id', label: 'Order ID', sortable: true },
  { key: 'customer', label: 'Customer', sortable: true },
  { key: 'items', label: 'Items', sortable: false },
  { key: 'amount_total', label: 'Amount', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Date', sortable: true },
  { key: 'actions', label: 'Actions', sortable: false }
];

// Computed
const orders = computed(() => {
  return apiData.value?.data || [];
});

// Update pagination when API data changes
watch(apiData, (newData) => {
  if (newData) {
    currentPage.value = newData.current_page || 1;
    totalPages.value = newData.last_page || 1;
    totalItems.value = newData.total || 0;
    perPage.value = newData.per_page || 20;
  }
}, { immediate: true });

// Methods
const loadOrders = async () => {
  const params: any = {
    page: currentPage.value,
    per_page: perPage.value
  };

  // Apply filters
  if (filters.value.status) params.status = filters.value.status;
  if (filters.value.customer) params.customer = filters.value.customer;
  if (filters.value.orderId) params.order_id = filters.value.orderId;
  if (filters.value.dateRange) params.date_range = filters.value.dateRange;

  try {
    await fetchOrders(params);
  } catch (err) {
    console.error('Error loading orders:', err);
  }
};

const handleCreate = () => {
  router.push('/admin-spa/sales/orders/create');
};

const handleSearch = (query: string) => {
  filters.value.customer = query;
  applyFilters();
};

const handleSort = (column: string, order: 'asc' | 'desc') => {
  // Implement sorting logic
  loadOrders();
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadOrders();
};

const applyFilters = () => {
  currentPage.value = 1;
  loadOrders();
};

const exportOrders = () => {
  console.log('Exporting orders...');
};

const viewOrder = (order: Order) => {
  router.push(`/admin-spa/sales/orders/${order.id}`);
};

const editOrder = (order: Order) => {
  router.push(`/admin-spa/sales/orders/${order.id}/edit`);
};

const processOrder = (order: Order) => {
  console.log('Processing order:', order.id);
  // Implement order processing logic
};

// Utility functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'Active': 'bg-blue-100 text-blue-800',
    'Completed': 'bg-green-100 text-green-800',
    'Cancelled': 'bg-red-100 text-red-800',
    'Pending': 'bg-yellow-100 text-yellow-800',
    'Processing': 'bg-purple-100 text-purple-800'
  }; 
  return statusClasses[status] || 'bg-gray-100 text-gray-800';
};

// Watch for filter changes
watch(filters, () => {
  applyFilters();
}, { deep: true });

// Lifecycle
onMounted(() => {
  loadOrders();
});
</script>
