<template>
  <div class="p-6">
    <PrimeDataTable
      title="Orders"
      subtitle="Manage customer orders and transactions"
      :items="orders"
      :columns="primeColumns"
      :loading="loading"
      :current-page="currentPage"
      :total-items="totalItems"
      :per-page="perPage"
      :lazy="true"
      :show-pagination="true"
      :show-global-filter="true"
      global-filter-placeholder="Search orders..."
      :global-filter-fields="['order_id', 'customer', 'customer_email', 'status']"
      :show-toolbar="true"
      :show-create-button="true"
      create-button-text="New Order"
      :show-export="true"
      :show-refresh="true"
      empty-state-title="No orders found"
      empty-state-message="Orders will appear here when customers make purchases."
      data-key="id"
      @create="handleCreate"
      @page="handlePageChange"
      @sort="handleSort"
      @filter="handleFilter"
      @refresh="loadOrders"
      @export="exportOrders"
      @view="viewOrder"
      @edit="editOrder"
      @delete="deleteOrder"
    >
      <!-- Custom filters in toolbar -->
      <template #filters>
        <div class="flex items-center space-x-3">
          <PrimeDropdown
            v-model="filters.status"
            :options="statusOptions"
            option-label="label"
            option-value="value"
            placeholder="Filter by status"
            class="w-48"
            @change="applyFilters"
          />
          <PrimeDropdown
            v-model="filters.dateRange"
            :options="dateRangeOptions"
            option-label="label"
            option-value="value"
            placeholder="Date range"
            class="w-48"
            @change="applyFilters"
          />
          <InputText
            v-model="filters.customer"
            placeholder="Search customer..."
            class="w-48"
            @input="applyFilters"
          />
          <InputText
            v-model="filters.orderId"
            placeholder="Order ID..."
            class="w-32"
            @input="applyFilters"
          />
        </div>
      </template>

      <!-- Custom column templates -->
      <template #customer-body="{ data }">
        <div>
          <div class="font-medium text-gray-900">{{ data.customer }}</div>
          <div class="text-sm text-gray-500">{{ data.customer_email }}</div>
        </div>
      </template>

      <template #items-body="{ data }">
        <span class="text-gray-900">
          {{ data.sales_count }} item{{ data.sales_count === 1 ? '' : 's' }}
        </span>
      </template>

      <template #amount-body="{ data }">
        <div>
          <div class="font-medium text-gray-900">${{ formatCurrency(data.amount_total) }}</div>
          <div v-if="data.discount > 0" class="text-xs text-green-600">
            -${{ formatCurrency(data.discount) }} discount
          </div>
        </div>
      </template>

      <template #status-body="{ data }">
        <span :class="getStatusClass(data.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ data.status }}
        </span>
      </template>

      <template #date-body="{ data }">
        <span class="text-gray-500">{{ formatDate(data.created_at) }}</span>
      </template>

      <!-- Custom row actions -->
      <template #row-actions="{ data }">
        <PrimeButton
          v-if="data.status_id !== 11"
          icon="pi pi-cog"
          severity="info"
          outlined
          size="small"
          @click="processOrder(data)"
          v-tooltip="'Process Order'"
        />
      </template>
    </PrimeDataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { PrimeDataTable } from '@/components/admin';
import { useApi } from '@/composables/useApi';

interface Order {
  id: number;
  order_id: string;
  customer: string;
  customer_email: string;
  customer_id: number;
  description: string;
  sub_total: number;
  amount_total: number;
  amount_paid: number;
  discount: number;
  vat: number;
  status: string;
  status_id: number;
  prepared_by: string;
  approved_by_name: string;
  branch: string;
  branch_id: number;
  sales_count: number;
  payments_count: number;
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// API setup
const { data: apiData, isLoading: loading, execute: fetchOrders } = useApi<any>('/admin/orders');

// State
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const perPage = ref(20);

// Filters
const filters = ref({
  status: '',
  dateRange: '',
  customer: '',
  orderId: ''
});

// Options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: '1', label: 'Active' },
  { value: '11', label: 'Completed' },
  { value: '13', label: 'Cancelled' }
];

const dateRangeOptions = [
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
];

// PrimeVue DataTable columns
const primeColumns = [
  {
    field: 'order_id',
    header: 'Order ID',
    sortable: true,
    bodySlot: 'order-id-body'
  },
  {
    field: 'customer',
    header: 'Customer',
    sortable: true,
    bodySlot: 'customer-body'
  },
  {
    field: 'sales_count',
    header: 'Items',
    sortable: false,
    bodySlot: 'items-body'
  },
  {
    field: 'amount_total',
    header: 'Amount',
    sortable: true,
    bodySlot: 'amount-body'
  },
  {
    field: 'status',
    header: 'Status',
    sortable: true,
    bodySlot: 'status-body'
  },
  {
    field: 'created_at',
    header: 'Date',
    sortable: true,
    bodySlot: 'date-body'
  }
];

// Computed
const orders = computed(() => {
  return apiData.value?.data || [];
});

// Update pagination when API data changes
watch(apiData, (newData) => {
  if (newData) {
    currentPage.value = newData.current_page || 1;
    totalPages.value = newData.last_page || 1;
    totalItems.value = newData.total || 0;
    perPage.value = newData.per_page || 20;
  }
}, { immediate: true });

// Methods
const loadOrders = async () => {
  const params: any = {
    page: currentPage.value,
    per_page: perPage.value
  };

  // Apply filters
  if (filters.value.status) params.status = filters.value.status;
  if (filters.value.customer) params.customer = filters.value.customer;
  if (filters.value.orderId) params.order_id = filters.value.orderId;
  if (filters.value.dateRange) params.date_range = filters.value.dateRange;

  try {
    await fetchOrders(params);
  } catch (err) {
    console.error('Error loading orders:', err);
  }
};

const handleCreate = () => {
  router.push('/admin-spa/sales/orders/create');
};

const handleSort = (event: any) => {
  // PrimeVue sort event contains sortField and sortOrder
  console.log('Sort event:', event);
  loadOrders();
};

const handlePageChange = (event: any) => {
  // PrimeVue page event contains page, first, rows, etc.
  currentPage.value = event.page + 1; // PrimeVue uses 0-based indexing
  perPage.value = event.rows;
  loadOrders();
};

const handleFilter = (event: any) => {
  // PrimeVue filter event
  console.log('Filter event:', event);
  loadOrders();
};

const applyFilters = () => {
  currentPage.value = 1;
  loadOrders();
};

const exportOrders = () => {
  console.log('Exporting orders...');
};

const deleteOrder = (order: Order) => {
  console.log('Deleting order:', order.id);
  // Implement delete logic
};

const viewOrder = (order: Order) => {
  router.push(`/admin-spa/sales/orders/${order.id}`);
};

const editOrder = (order: Order) => {
  router.push(`/admin-spa/sales/orders/${order.id}/edit`);
};

const processOrder = (order: Order) => {
  console.log('Processing order:', order.id);
  // Implement order processing logic
};

// Utility functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'Active': 'bg-blue-100 text-blue-800',
    'Completed': 'bg-green-100 text-green-800',
    'Cancelled': 'bg-red-100 text-red-800',
    'Pending': 'bg-yellow-100 text-yellow-800',
    'Processing': 'bg-purple-100 text-purple-800'
  }; 
  return statusClasses[status] || 'bg-gray-100 text-gray-800';
};

// Watch for filter changes
watch(filters, () => {
  applyFilters();
}, { deep: true });

// Lifecycle
onMounted(() => {
  loadOrders();
});
</script>
